import React, { useState, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { Palette, Download, Copy, RefreshCw, Sparkles, Eye, Zap, Upload } from 'lucide-react';

interface ColorPalette {
  id: string;
  name: string;
  colors: string[];
  type: string;
  description: string;
  industry?: string;
}

interface ColorHarmony {
  id: string;
  name: string;
  description: string;
  algorithm: (baseColor: string) => string[];
}

const colorHarmonies: ColorHarmony[] = [
  {
    id: 'monochromatic',
    name: 'Monochromatic',
    description: 'Variasi satu warna dengan tingkat kecerahan berbeda',
    algorithm: (baseColor: string) => {
      const hsl = hexToHsl(baseColor);
      return [
        hslToHex(hsl.h, hsl.s, Math.max(hsl.l - 0.3, 0.1)),
        hslToHex(hsl.h, hsl.s, Math.max(hsl.l - 0.15, 0.2)),
        baseColor,
        hslToHex(hsl.h, hsl.s, Math.min(hsl.l + 0.15, 0.8)),
        hslToHex(hsl.h, hsl.s, Math.min(hsl.l + 0.3, 0.9))
      ];
    }
  },
  {
    id: 'complementary',
    name: 'Complementary',
    description: 'Warna berlawanan di color wheel untuk kontras maksimal',
    algorithm: (baseColor: string) => {
      const hsl = hexToHsl(baseColor);
      const complementHue = (hsl.h + 180) % 360;
      return [
        hslToHex(hsl.h, hsl.s, Math.max(hsl.l - 0.2, 0.1)),
        baseColor,
        hslToHex(hsl.h, hsl.s, Math.min(hsl.l + 0.2, 0.9)),
        hslToHex(complementHue, hsl.s, Math.max(hsl.l - 0.1, 0.2)),
        hslToHex(complementHue, hsl.s, hsl.l)
      ];
    }
  },
  {
    id: 'triadic',
    name: 'Triadic',
    description: '3 warna dengan jarak sama di color wheel',
    algorithm: (baseColor: string) => {
      const hsl = hexToHsl(baseColor);
      return [
        baseColor,
        hslToHex((hsl.h + 120) % 360, hsl.s, hsl.l),
        hslToHex((hsl.h + 240) % 360, hsl.s, hsl.l),
        hslToHex(hsl.h, hsl.s, Math.min(hsl.l + 0.2, 0.9)),
        hslToHex(hsl.h, hsl.s, Math.max(hsl.l - 0.2, 0.1))
      ];
    }
  },
  {
    id: 'analogous',
    name: 'Analogous',
    description: 'Warna bersebelahan di color wheel untuk harmoni natural',
    algorithm: (baseColor: string) => {
      const hsl = hexToHsl(baseColor);
      return [
        hslToHex((hsl.h - 30 + 360) % 360, hsl.s, hsl.l),
        hslToHex((hsl.h - 15 + 360) % 360, hsl.s, hsl.l),
        baseColor,
        hslToHex((hsl.h + 15) % 360, hsl.s, hsl.l),
        hslToHex((hsl.h + 30) % 360, hsl.s, hsl.l)
      ];
    }
  },
  {
    id: 'split-complementary',
    name: 'Split Complementary',
    description: 'Base color + 2 warna di samping complementary',
    algorithm: (baseColor: string) => {
      const hsl = hexToHsl(baseColor);
      const complement = (hsl.h + 180) % 360;
      return [
        baseColor,
        hslToHex((complement - 30 + 360) % 360, hsl.s, hsl.l),
        hslToHex((complement + 30) % 360, hsl.s, hsl.l),
        hslToHex(hsl.h, hsl.s, Math.min(hsl.l + 0.2, 0.9)),
        hslToHex(hsl.h, hsl.s, Math.max(hsl.l - 0.2, 0.1))
      ];
    }
  }
];

const industryPalettes: ColorPalette[] = [
  {
    id: 'tech',
    name: 'Technology',
    colors: ['#2563eb', '#3b82f6', '#60a5fa', '#93c5fd', '#dbeafe'],
    type: 'industry',
    description: 'Modern, trustworthy, innovative',
    industry: 'Technology'
  },
  {
    id: 'healthcare',
    name: 'Healthcare',
    colors: ['#059669', '#10b981', '#34d399', '#6ee7b7', '#a7f3d0'],
    type: 'industry',
    description: 'Clean, caring, professional',
    industry: 'Healthcare'
  },
  {
    id: 'finance',
    name: 'Finance',
    colors: ['#1e40af', '#3730a3', '#4338ca', '#6366f1', '#8b5cf6'],
    type: 'industry',
    description: 'Trustworthy, stable, premium',
    industry: 'Finance'
  },
  {
    id: 'creative',
    name: 'Creative',
    colors: ['#f59e0b', '#f97316', '#ef4444', '#ec4899', '#8b5cf6'],
    type: 'industry',
    description: 'Vibrant, artistic, expressive',
    industry: 'Creative'
  },
  {
    id: 'food',
    name: 'Food & Beverage',
    colors: ['#dc2626', '#ea580c', '#f59e0b', '#eab308', '#84cc16'],
    type: 'industry',
    description: 'Appetizing, warm, natural',
    industry: 'Food'
  },
  {
    id: 'education',
    name: 'Education',
    colors: ['#0891b2', '#0e7490', '#155e75', '#164e63', '#083344'],
    type: 'industry',
    description: 'Trustworthy, calm, professional',
    industry: 'Education'
  },
  {
    id: 'retail',
    name: 'Retail',
    colors: ['#e11d48', '#f43f5e', '#fb7185', '#fda4af', '#fecdd3'],
    type: 'industry',
    description: 'Energetic, attractive, engaging',
    industry: 'Retail'
  },
  {
    id: 'real-estate',
    name: 'Real Estate',
    colors: ['#92400e', '#b45309', '#d97706', '#f59e0b', '#fbbf24'],
    type: 'industry',
    description: 'Stable, warm, trustworthy',
    industry: 'Real Estate'
  }
];

const trendPalettes: ColorPalette[] = [
  {
    id: 'trend-2024-1',
    name: 'Digital Lime',
    colors: ['#32d74b', '#30d158', '#34c759', '#28cd41', '#20c933'],
    type: 'trend',
    description: '2024 trending - Fresh digital green'
  },
  {
    id: 'trend-2024-2',
    name: 'Cosmic Purple',
    colors: ['#5856d6', '#5ac8fa', '#af52de', '#bf5af2', '#da70d6'],
    type: 'trend',
    description: '2024 trending - Cosmic and mystical'
  },
  {
    id: 'trend-2024-3',
    name: 'Warm Coral',
    colors: ['#ff6b6b', '#ff8e53', '#ff6b35', '#ff5722', '#ff3d00'],
    type: 'trend',
    description: '2024 trending - Warm and inviting'
  },
  {
    id: 'trend-2024-4',
    name: 'Ocean Depth',
    colors: ['#006a6b', '#008b8b', '#20b2aa', '#48cae4', '#90e0ef'],
    type: 'trend',
    description: '2024 trending - Deep ocean vibes'
  },
  {
    id: 'trend-2024-5',
    name: 'Sunset Glow',
    colors: ['#ff9500', '#ff8c00', '#ff7f50', '#ff6347', '#ff4500'],
    type: 'trend',
    description: '2024 trending - Golden hour warmth'
  }
];

// Color utility functions
function hexToHsl(hex: string): { h: number; s: number; l: number } {
  const r = parseInt(hex.slice(1, 3), 16) / 255;
  const g = parseInt(hex.slice(3, 5), 16) / 255;
  const b = parseInt(hex.slice(5, 7), 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  return { h: h * 360, s, l };
}

function hslToHex(h: number, s: number, l: number): string {
  h = h / 360;
  const hue2rgb = (p: number, q: number, t: number) => {
    if (t < 0) t += 1;
    if (t > 1) t -= 1;
    if (t < 1/6) return p + (q - p) * 6 * t;
    if (t < 1/2) return q;
    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
    return p;
  };

  let r, g, b;
  if (s === 0) {
    r = g = b = l;
  } else {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    r = hue2rgb(p, q, h + 1/3);
    g = hue2rgb(p, q, h);
    b = hue2rgb(p, q, h - 1/3);
  }

  const toHex = (c: number) => {
    const hex = Math.round(c * 255).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };

  return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
}

function getContrastRatio(color1: string, color2: string): number {
  const getLuminance = (hex: string) => {
    const rgb = [
      parseInt(hex.slice(1, 3), 16),
      parseInt(hex.slice(3, 5), 16),
      parseInt(hex.slice(5, 7), 16)
    ].map(c => {
      c = c / 255;
      return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
    });
    return 0.2126 * rgb[0] + 0.7152 * rgb[1] + 0.0722 * rgb[2];
  };

  const lum1 = getLuminance(color1);
  const lum2 = getLuminance(color2);
  const brightest = Math.max(lum1, lum2);
  const darkest = Math.min(lum1, lum2);

  return (brightest + 0.05) / (darkest + 0.05);
}

function simulateColorBlindness(hex: string, type: string): string {
  if (type === 'normal') return hex;

  const r = parseInt(hex.slice(1, 3), 16) / 255;
  const g = parseInt(hex.slice(3, 5), 16) / 255;
  const b = parseInt(hex.slice(5, 7), 16) / 255;

  let newR = r, newG = g, newB = b;

  switch (type) {
    case 'protanopia': // Red-blind
      newR = 0.567 * r + 0.433 * g;
      newG = 0.558 * r + 0.442 * g;
      newB = 0.242 * g + 0.758 * b;
      break;
    case 'deuteranopia': // Green-blind
      newR = 0.625 * r + 0.375 * g;
      newG = 0.7 * r + 0.3 * g;
      newB = 0.3 * g + 0.7 * b;
      break;
    case 'tritanopia': // Blue-blind
      newR = 0.95 * r + 0.05 * g;
      newG = 0.433 * g + 0.567 * b;
      newB = 0.475 * g + 0.525 * b;
      break;
  }

  const toHex = (c: number) => {
    const hex = Math.round(Math.max(0, Math.min(255, c * 255))).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };

  return `#${toHex(newR)}${toHex(newG)}${toHex(newB)}`;
}

export const ColorPaletteGenerator: React.FC = () => {
  const [baseColor, setBaseColor] = useState('#3b82f6');
  const [selectedHarmony, setSelectedHarmony] = useState<ColorHarmony>(colorHarmonies[0]);
  const [generatedPalette, setGeneratedPalette] = useState<string[]>([]);
  const [selectedIndustry, setSelectedIndustry] = useState<string>('');
  const [customPaletteName, setCustomPaletteName] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [colorBlindnessType, setColorBlindnessType] = useState<string>('normal');
  const fileInputRef = useRef<HTMLInputElement>(null);

  React.useEffect(() => {
    generatePalette();
  }, [baseColor, selectedHarmony]);

  const generatePalette = () => {
    const colors = selectedHarmony.algorithm(baseColor);
    setGeneratedPalette(colors);
  };

  const generateRandomPalette = () => {
    setIsGenerating(true);
    
    setTimeout(() => {
      const randomHue = Math.floor(Math.random() * 360);
      const randomSaturation = 0.6 + Math.random() * 0.4;
      const randomLightness = 0.4 + Math.random() * 0.3;
      
      const newBaseColor = hslToHex(randomHue, randomSaturation, randomLightness);
      const randomHarmony = colorHarmonies[Math.floor(Math.random() * colorHarmonies.length)];
      
      setBaseColor(newBaseColor);
      setSelectedHarmony(randomHarmony);
      setIsGenerating(false);
      
      toast.success(`Palette ${randomHarmony.name} berhasil digenerate!`);
    }, 1000);
  };

  const generateAIPalette = () => {
    if (!selectedIndustry) {
      toast.error('Pilih industri terlebih dahulu');
      return;
    }

    setIsGenerating(true);
    
    setTimeout(() => {
      const industryPalette = industryPalettes.find(p => p.industry?.toLowerCase() === selectedIndustry.toLowerCase());
      if (industryPalette) {
        setGeneratedPalette(industryPalette.colors);
        setBaseColor(industryPalette.colors[0]);
        toast.success(`Palette ${industryPalette.name} berhasil digenerate!`);
      }
      setIsGenerating(false);
    }, 1500);
  };

  const copyColor = (color: string) => {
    navigator.clipboard.writeText(color);
    toast.success(`Warna ${color} disalin ke clipboard`);
  };

  const copyPalette = (format: 'hex' | 'css' | 'scss' | 'json' | 'figma') => {
    let content = '';

    switch (format) {
      case 'hex':
        content = generatedPalette.join(', ');
        break;
      case 'css':
        content = generatedPalette.map((color, index) =>
          `  --color-${index + 1}: ${color};`
        ).join('\n');
        content = `:root {\n${content}\n}`;
        break;
      case 'scss':
        content = generatedPalette.map((color, index) =>
          `$color-${index + 1}: ${color};`
        ).join('\n');
        break;
      case 'json':
        content = JSON.stringify({
          name: customPaletteName || 'Generated Palette',
          colors: generatedPalette,
          harmony: selectedHarmony.name,
          type: 'color-palette',
          created: new Date().toISOString()
        }, null, 2);
        break;
      case 'figma':
        content = generatedPalette.map((color, index) =>
          `Color ${index + 1}: ${color}`
        ).join('\n');
        content = `// Figma Color Palette\n// ${customPaletteName || 'Generated Palette'}\n\n${content}`;
        break;
    }

    navigator.clipboard.writeText(content);
    toast.success(`Palette disalin sebagai ${format.toUpperCase()}`);
  };

  const downloadPalette = () => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = 500;
    canvas.height = 100;

    const colorWidth = canvas.width / generatedPalette.length;
    
    generatedPalette.forEach((color, index) => {
      ctx.fillStyle = color;
      ctx.fillRect(index * colorWidth, 0, colorWidth, canvas.height);
    });

    // Add color labels
    ctx.fillStyle = '#000000';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    
    generatedPalette.forEach((color, index) => {
      const x = index * colorWidth + colorWidth / 2;
      const textColor = getContrastRatio(color, '#000000') > 4.5 ? '#000000' : '#ffffff';
      ctx.fillStyle = textColor;
      ctx.fillText(color, x, canvas.height / 2 + 5);
    });

    const link = document.createElement('a');
    link.download = `${customPaletteName || 'color-palette'}.png`;
    link.href = canvas.toDataURL();
    link.click();
    
    toast.success('Palette berhasil didownload sebagai PNG');
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="w-5 h-5" />
            Brand Color Palette Generator
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Controls */}
            <div className="space-y-6">
              <Tabs defaultValue="harmony" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="harmony">Harmony</TabsTrigger>
                  <TabsTrigger value="industry">Industry</TabsTrigger>
                  <TabsTrigger value="trends">Trends</TabsTrigger>
                  <TabsTrigger value="custom">Custom</TabsTrigger>
                </TabsList>
                
                <TabsContent value="harmony" className="space-y-4">
                  <div>
                    <Label htmlFor="base-color">Base Color</Label>
                    <div className="flex gap-2 mt-2">
                      <Input
                        id="base-color"
                        type="color"
                        value={baseColor}
                        onChange={(e) => setBaseColor(e.target.value)}
                        className="w-16 h-10 p-1 border rounded"
                      />
                      <Input
                        value={baseColor}
                        onChange={(e) => setBaseColor(e.target.value)}
                        placeholder="#3b82f6"
                        className="flex-1"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label>Color Harmony Type</Label>
                    <div className="grid grid-cols-1 gap-2 mt-2">
                      {colorHarmonies.map((harmony) => (
                        <Card
                          key={harmony.id}
                          className={`cursor-pointer transition-all ${
                            selectedHarmony.id === harmony.id
                              ? 'ring-2 ring-blue-500 bg-blue-50'
                              : 'hover:bg-gray-50'
                          }`}
                          onClick={() => setSelectedHarmony(harmony)}
                        >
                          <CardContent className="p-3">
                            <h4 className="font-medium">{harmony.name}</h4>
                            <p className="text-sm text-gray-600">{harmony.description}</p>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="industry" className="space-y-4">
                  <div>
                    <Label>Pilih Industri</Label>
                    <Select value={selectedIndustry} onValueChange={setSelectedIndustry}>
                      <SelectTrigger className="mt-2">
                        <SelectValue placeholder="Pilih industri bisnis" />
                      </SelectTrigger>
                      <SelectContent>
                        {industryPalettes.map((palette) => (
                          <SelectItem key={palette.id} value={palette.industry || ''}>
                            {palette.industry}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    {industryPalettes.map((palette) => (
                      <Card
                        key={palette.id}
                        className={`cursor-pointer transition-all ${
                          selectedIndustry === palette.industry
                            ? 'ring-2 ring-blue-500 bg-blue-50'
                            : 'hover:bg-gray-50'
                        }`}
                        onClick={() => setSelectedIndustry(palette.industry || '')}
                      >
                        <CardContent className="p-3">
                          <div className="flex justify-between items-center">
                            <div>
                              <h4 className="font-medium">{palette.name}</h4>
                              <p className="text-sm text-gray-600">{palette.description}</p>
                            </div>
                            <div className="flex gap-1">
                              {palette.colors.slice(0, 4).map((color, index) => (
                                <div
                                  key={index}
                                  className="w-6 h-6 rounded border"
                                  style={{ backgroundColor: color }}
                                />
                              ))}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  <Button
                    onClick={generateAIPalette}
                    disabled={isGenerating || !selectedIndustry}
                    className="w-full"
                  >
                    {isGenerating ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Sparkles className="w-4 h-4 mr-2" />
                        Generate Industry Palette
                      </>
                    )}
                  </Button>
                </TabsContent>

                <TabsContent value="trends" className="space-y-4">
                  <div>
                    <Label>2024 Trending Palettes</Label>
                    <p className="text-sm text-gray-600 mt-1">Palette warna yang sedang trending di tahun 2024</p>
                  </div>

                  <div className="space-y-2">
                    {trendPalettes.map((palette) => (
                      <Card
                        key={palette.id}
                        className="cursor-pointer transition-all hover:bg-gray-50"
                        onClick={() => {
                          setGeneratedPalette(palette.colors);
                          setBaseColor(palette.colors[0]);
                          toast.success(`Trend palette ${palette.name} dipilih!`);
                        }}
                      >
                        <CardContent className="p-3">
                          <div className="flex justify-between items-center">
                            <div>
                              <h4 className="font-medium">{palette.name}</h4>
                              <p className="text-sm text-gray-600">{palette.description}</p>
                            </div>
                            <div className="flex gap-1">
                              {palette.colors.map((color, index) => (
                                <div
                                  key={index}
                                  className="w-6 h-6 rounded border"
                                  style={{ backgroundColor: color }}
                                />
                              ))}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  <div className="p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg">
                    <h4 className="font-medium text-purple-900 mb-2">✨ Trending Colors 2024</h4>
                    <ul className="text-sm text-purple-800 space-y-1">
                      <li>• Digital Lime: Fresh dan futuristic</li>
                      <li>• Cosmic Purple: Mystical dan premium</li>
                      <li>• Warm Coral: Friendly dan approachable</li>
                      <li>• Ocean Depth: Calming dan professional</li>
                      <li>• Sunset Glow: Energetic dan optimistic</li>
                    </ul>
                  </div>
                </TabsContent>

                <TabsContent value="custom" className="space-y-4">
                  <div>
                    <Label htmlFor="palette-name">Nama Palette</Label>
                    <Input
                      id="palette-name"
                      value={customPaletteName}
                      onChange={(e) => setCustomPaletteName(e.target.value)}
                      placeholder="My Brand Palette"
                      className="mt-2"
                    />
                  </div>

                  <Button
                    onClick={generateRandomPalette}
                    disabled={isGenerating}
                    className="w-full"
                  >
                    {isGenerating ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Zap className="w-4 h-4 mr-2" />
                        Generate Random Palette
                      </>
                    )}
                  </Button>
                </TabsContent>
              </Tabs>
            </div>

            {/* Preview */}
            <div className="space-y-4">
              <div>
                <Label>Generated Palette</Label>
                <Card className="mt-2">
                  <CardContent className="p-4">
                    <div className="space-y-4">
                      {/* Color Swatches */}
                      <div className="grid grid-cols-5 gap-2">
                        {generatedPalette.map((color, index) => (
                          <div key={index} className="space-y-2">
                            <div
                              className="w-full h-16 rounded-lg border cursor-pointer hover:scale-105 transition-transform"
                              style={{ backgroundColor: color }}
                              onClick={() => copyColor(color)}
                              title={`Click to copy ${color}`}
                            />
                            <div className="text-center">
                              <p className="text-xs font-mono">{color}</p>
                            </div>
                          </div>
                        ))}
                      </div>

                      {/* Color Blindness Simulator */}
                      {generatedPalette.length > 0 && (
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium">Color Blindness Simulator</Label>
                            <Select value={colorBlindnessType} onValueChange={setColorBlindnessType}>
                              <SelectTrigger className="w-32">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="normal">Normal</SelectItem>
                                <SelectItem value="protanopia">Protanopia</SelectItem>
                                <SelectItem value="deuteranopia">Deuteranopia</SelectItem>
                                <SelectItem value="tritanopia">Tritanopia</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          {colorBlindnessType !== 'normal' && (
                            <div className="grid grid-cols-5 gap-2">
                              {generatedPalette.map((color, index) => (
                                <div key={index} className="space-y-1">
                                  <div
                                    className="w-full h-8 rounded border"
                                    style={{ backgroundColor: simulateColorBlindness(color, colorBlindnessType) }}
                                  />
                                  <p className="text-xs text-center text-gray-600">
                                    {colorBlindnessType}
                                  </p>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      )}

                      {/* Accessibility Info */}
                      {generatedPalette.length > 0 && (
                        <div className="p-3 bg-gray-50 rounded-lg">
                          <h4 className="font-medium text-sm mb-2">Accessibility Check</h4>
                          <div className="space-y-1 text-xs">
                            <div className="flex justify-between">
                              <span>Contrast with white:</span>
                              <Badge variant={getContrastRatio(generatedPalette[0], '#ffffff') >= 4.5 ? 'default' : 'destructive'}>
                                {getContrastRatio(generatedPalette[0], '#ffffff').toFixed(1)}:1
                              </Badge>
                            </div>
                            <div className="flex justify-between">
                              <span>Contrast with black:</span>
                              <Badge variant={getContrastRatio(generatedPalette[0], '#000000') >= 4.5 ? 'default' : 'destructive'}>
                                {getContrastRatio(generatedPalette[0], '#000000').toFixed(1)}:1
                              </Badge>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-2">
                <Label>Export Palette</Label>
                <div className="grid grid-cols-3 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyPalette('hex')}
                    disabled={generatedPalette.length === 0}
                  >
                    <Copy className="w-4 h-4 mr-1" />
                    HEX
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyPalette('css')}
                    disabled={generatedPalette.length === 0}
                  >
                    <Copy className="w-4 h-4 mr-1" />
                    CSS
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyPalette('scss')}
                    disabled={generatedPalette.length === 0}
                  >
                    <Copy className="w-4 h-4 mr-1" />
                    SCSS
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyPalette('json')}
                    disabled={generatedPalette.length === 0}
                  >
                    <Copy className="w-4 h-4 mr-1" />
                    JSON
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyPalette('figma')}
                    disabled={generatedPalette.length === 0}
                  >
                    <Copy className="w-4 h-4 mr-1" />
                    Figma
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={downloadPalette}
                    disabled={generatedPalette.length === 0}
                  >
                    <Download className="w-4 h-4 mr-1" />
                    PNG
                  </Button>
                </div>
              </div>

              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">🎨 Color Theory Tips</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Monochromatic: Harmonis dan tenang</li>
                  <li>• Complementary: Kontras tinggi, eye-catching</li>
                  <li>• Triadic: Seimbang dan vibrant</li>
                  <li>• Analogous: Natural dan pleasing</li>
                  <li>• Contrast ratio 4.5:1+ untuk accessibility</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

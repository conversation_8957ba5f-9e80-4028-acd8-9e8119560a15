import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { File, Download, Copy, RefreshCw, Sparkles, Mail, FileText, Table, Settings } from 'lucide-react';

interface WordVBATemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  complexity: 'Basic' | 'Intermediate' | 'Advanced';
  code: string;
  fields: ConfigField[];
  preview: string;
}

interface ConfigField {
  id: string;
  name: string;
  type: 'text' | 'number' | 'select' | 'textarea' | 'boolean';
  placeholder?: string;
  options?: string[];
  required: boolean;
  defaultValue?: any;
}

const wordVBATemplates: WordVBATemplate[] = [
  {
    id: 'mail-merge-automation',
    name: 'Mail Merge Automation',
    description: 'Automate mail merge process dengan data dari Excel',
    category: 'Mail Merge',
    complexity: 'Intermediate',
    code: `Sub AutoMailMerge()
    Dim doc As Document
    Dim dataSource As String
    Dim outputPath As String

    ' Configuration
    dataSource = "{{dataSourcePath}}"
    outputPath = "{{outputPath}}"

    ' Open main document
    Set doc = ActiveDocument

    ' Setup mail merge
    With doc.MailMerge
        .MainDocumentType = wdFormLetters
        .OpenDataSource Name:=dataSource, _
            ConfirmConversions:=False, _
            ReadOnly:=False, _
            LinkToSource:=True, _
            AddToRecentFiles:=False, _
            PasswordDocument:="", _
            PasswordTemplate:="", _
            WritePasswordDocument:="", _
            WritePasswordTemplate:="", _
            Revert:=False, _
            Format:=wdOpenFormatAuto, _
            Connection:="", _
            SQLStatement:="", _
            SQLStatement1:=""

        ' Execute merge
        .Destination = {{mergeDestination}}
        .SuppressBlankLines = True

        If {{mergeDestination}} = wdSendToNewDocument Then
            .Execute Pause:=False
            ' Save merged document
            ActiveDocument.SaveAs2 outputPath & "\\MergedDocument_" & Format(Now, "yyyymmdd_hhmmss") & ".docx"
        ElseIf {{mergeDestination}} = wdSendToPrinter Then
            .Execute Pause:=False
        End If
    End With

    MsgBox "Mail merge completed successfully!", vbInformation
End Sub`,
    fields: [
      { id: 'dataSourcePath', name: 'Data Source Path', type: 'text', placeholder: 'C:\\Data\\contacts.xlsx', required: true },
      { id: 'outputPath', name: 'Output Path', type: 'text', placeholder: 'C:\\Output', required: true },
      { id: 'mergeDestination', name: 'Merge Destination', type: 'select', options: ['wdSendToNewDocument', 'wdSendToPrinter'], required: true, defaultValue: 'wdSendToNewDocument' }
    ],
    preview: 'Automates mail merge process from Excel data source to Word documents'
  },
  {
    id: 'template-filler',
    name: 'Template Auto-Filler',
    description: 'Auto-fill Word templates dengan data dari berbagai sources',
    category: 'Templates',
    complexity: 'Basic',
    code: `Sub AutoFillTemplate()
    Dim doc As Document
    Dim bookmarkName As String
    Dim replacementText As String

    Set doc = ActiveDocument

    ' Define bookmark replacements
    Dim bookmarks As Object
    Set bookmarks = CreateObject("Scripting.Dictionary")

    ' Add your bookmark mappings
    bookmarks.Add "{{bookmarkName1}}", "{{replacementValue1}}"
    bookmarks.Add "{{bookmarkName2}}", "{{replacementValue2}}"
    bookmarks.Add "{{bookmarkName3}}", "{{replacementValue3}}"
    bookmarks.Add "{{bookmarkName4}}", "{{replacementValue4}}"

    ' Process each bookmark
    Dim key As Variant
    For Each key In bookmarks.Keys
        If doc.Bookmarks.Exists(key) Then
            doc.Bookmarks(key).Range.Text = bookmarks(key)
        End If
    Next key

    ' Also replace text placeholders
    With doc.Range.Find
        .ClearFormatting
        .Replacement.ClearFormatting

        ' Replace common placeholders
        .Text = "[DATE]"
        .Replacement.Text = Format(Date, "{{dateFormat}}")
        .Execute Replace:=wdReplaceAll

        .Text = "[TIME]"
        .Replacement.Text = Format(Time, "{{timeFormat}}")
        .Execute Replace:=wdReplaceAll

        .Text = "[COMPANY]"
        .Replacement.Text = "{{companyName}}"
        .Execute Replace:=wdReplaceAll
    End With

    MsgBox "Template filled successfully!", vbInformation
End Sub`,
    fields: [
      { id: 'bookmarkName1', name: 'Bookmark 1 Name', type: 'text', placeholder: 'ClientName', required: false },
      { id: 'replacementValue1', name: 'Replacement Value 1', type: 'text', placeholder: 'John Doe', required: false },
      { id: 'bookmarkName2', name: 'Bookmark 2 Name', type: 'text', placeholder: 'ProjectName', required: false },
      { id: 'replacementValue2', name: 'Replacement Value 2', type: 'text', placeholder: 'Website Development', required: false },
      { id: 'bookmarkName3', name: 'Bookmark 3 Name', type: 'text', placeholder: 'Amount', required: false },
      { id: 'replacementValue3', name: 'Replacement Value 3', type: 'text', placeholder: '$5,000', required: false },
      { id: 'bookmarkName4', name: 'Bookmark 4 Name', type: 'text', placeholder: 'DueDate', required: false },
      { id: 'replacementValue4', name: 'Replacement Value 4', type: 'text', placeholder: '2024-12-31', required: false },
      { id: 'dateFormat', name: 'Date Format', type: 'select', options: ['dd/mm/yyyy', 'mm/dd/yyyy', 'yyyy-mm-dd', 'dd-mmm-yyyy'], required: true, defaultValue: 'dd/mm/yyyy' },
      { id: 'timeFormat', name: 'Time Format', type: 'select', options: ['hh:mm', 'hh:mm:ss', 'h:mm AM/PM'], required: true, defaultValue: 'hh:mm' },
      { id: 'companyName', name: 'Company Name', type: 'text', placeholder: 'Your Company Name', required: true }
    ],
    preview: 'Automatically fills Word templates with predefined data and current date/time'
  },
  {
    id: 'document-merger',
    name: 'Document Merger',
    description: 'Merge multiple Word documents into one',
    category: 'Document Management',
    complexity: 'Intermediate',
    code: `Sub MergeDocuments()
    Dim mainDoc As Document
    Dim sourceDoc As Document
    Dim sourcePath As String
    Dim fileList As String
    Dim files() As String
    Dim i As Integer

    ' Configuration
    sourcePath = "{{sourcePath}}"
    fileList = "{{fileList}}"

    ' Create new document or use active
    If {{createNewDocument}} Then
        Set mainDoc = Documents.Add
    Else
        Set mainDoc = ActiveDocument
    End If

    ' Split file list
    files = Split(fileList, ";")

    ' Process each file
    For i = 0 To UBound(files)
        If Trim(files(i)) <> "" Then
            ' Open source document
            Set sourceDoc = Documents.Open(sourcePath & "\\" & Trim(files(i)))

            ' Copy content
            sourceDoc.Range.Copy

            ' Paste to main document
            mainDoc.Range.Collapse Direction:=wdCollapseEnd

            ' Add page break if not last document
            If i < UBound(files) And {{addPageBreaks}} Then
                mainDoc.Range.InsertBreak Type:=wdPageBreak
            End If

            ' Paste content
            mainDoc.Range.Paste

            ' Close source document
            sourceDoc.Close SaveChanges:=False
        End If
    Next i

    ' Save merged document
    If {{saveDocument}} Then
        mainDoc.SaveAs2 sourcePath & "\\MergedDocument_" & Format(Now, "yyyymmdd_hhmmss") & ".docx"
    End If

    MsgBox "Documents merged successfully!", vbInformation
End Sub`,
    fields: [
      { id: 'sourcePath', name: 'Source Path', type: 'text', placeholder: 'C:\\Documents', required: true },
      { id: 'fileList', name: 'File List (semicolon separated)', type: 'textarea', placeholder: 'doc1.docx;doc2.docx;doc3.docx', required: true },
      { id: 'createNewDocument', name: 'Create New Document', type: 'boolean', required: true, defaultValue: true },
      { id: 'addPageBreaks', name: 'Add Page Breaks', type: 'boolean', required: true, defaultValue: true },
      { id: 'saveDocument', name: 'Auto Save Result', type: 'boolean', required: true, defaultValue: true }
    ],
    preview: 'Merges multiple Word documents into a single document with optional page breaks'
  },
  {
    id: 'table-generator',
    name: 'Dynamic Table Generator',
    description: 'Generate tables dengan data dari Excel atau CSV',
    category: 'Tables',
    complexity: 'Advanced',
    code: `Sub GenerateTable()
    Dim doc As Document
    Dim tbl As Table
    Dim dataSource As String
    Dim xl As Object
    Dim wb As Object
    Dim ws As Object
    Dim lastRow As Long
    Dim lastCol As Long
    Dim i As Long, j As Long

    Set doc = ActiveDocument
    dataSource = "{{dataSource}}"

    ' Open Excel file
    Set xl = CreateObject("Excel.Application")
    xl.Visible = False
    Set wb = xl.Workbooks.Open(dataSource)
    Set ws = wb.Worksheets("{{worksheetName}}")

    ' Get data dimensions
    lastRow = ws.Cells(ws.Rows.Count, 1).End(-4162).Row ' xlUp = -4162
    lastCol = ws.Cells(1, ws.Columns.Count).End(-4159).Column ' xlToLeft = -4159

    ' Create table in Word
    Set tbl = doc.Tables.Add(Range:=Selection.Range, NumRows:=lastRow, NumColumns:=lastCol)

    ' Configure table style
    With tbl
        .Style = "{{tableStyle}}"
        .ApplyStyleHeadingRows = {{hasHeaders}}
        .ApplyStyleLastRow = False
        .ApplyStyleFirstColumn = False
        .ApplyStyleLastColumn = False
        .ApplyStyleRowBands = {{alternateRows}}
        .ApplyStyleColumnBands = False
    End With

    ' Fill table with data
    For i = 1 To lastRow
        For j = 1 To lastCol
            tbl.Cell(i, j).Range.Text = ws.Cells(i, j).Value
        Next j
    Next i

    ' Auto-fit table
    tbl.AutoFitBehavior ({{autoFitBehavior}})

    ' Close Excel
    wb.Close SaveChanges:=False
    xl.Quit
    Set xl = Nothing

    MsgBox "Table generated successfully!", vbInformation
End Sub`,
    fields: [
      { id: 'dataSource', name: 'Excel File Path', type: 'text', placeholder: 'C:\\Data\\table_data.xlsx', required: true },
      { id: 'worksheetName', name: 'Worksheet Name', type: 'text', placeholder: 'Sheet1', required: true, defaultValue: 'Sheet1' },
      { id: 'tableStyle', name: 'Table Style', type: 'select', options: ['Table Grid', 'Light Shading', 'Medium Shading 1', 'Medium Shading 2', 'Dark List'], required: true, defaultValue: 'Light Shading' },
      { id: 'hasHeaders', name: 'Has Header Row', type: 'boolean', required: true, defaultValue: true },
      { id: 'alternateRows', name: 'Alternate Row Colors', type: 'boolean', required: true, defaultValue: true },
      { id: 'autoFitBehavior', name: 'Auto Fit Behavior', type: 'select', options: ['wdAutoFitFixed', 'wdAutoFitContent', 'wdAutoFitWindow'], required: true, defaultValue: 'wdAutoFitContent' }
    ],
    preview: 'Creates formatted tables in Word from Excel data with customizable styling'
  },
  {
    id: 'header-footer-automation',
    name: 'Header/Footer Automation',
    description: 'Setup consistent headers dan footers across documents',
    category: 'Formatting',
    complexity: 'Basic',
    code: `Sub SetupHeaderFooter()
    Dim doc As Document
    Dim headerRange As Range
    Dim footerRange As Range

    Set doc = ActiveDocument

    ' Setup Header
    If {{includeHeader}} Then
        Set headerRange = doc.Sections(1).Headers(wdHeaderFooterPrimary).Range
        headerRange.Text = ""

        ' Add header content
        With headerRange
            .Text = "{{headerText}}"
            .Font.Name = "{{headerFont}}"
            .Font.Size = {{headerFontSize}}
            .Font.Bold = {{headerBold}}
            .ParagraphFormat.Alignment = {{headerAlignment}}
        End With

        ' Add logo if specified
        If "{{logoPath}}" <> "" Then
            headerRange.InlineShapes.AddPicture "{{logoPath}}", False, True
        End If
    End If

    ' Setup Footer
    If {{includeFooter}} Then
        Set footerRange = doc.Sections(1).Footers(wdHeaderFooterPrimary).Range
        footerRange.Text = ""

        ' Add footer content
        With footerRange
            .Text = "{{footerText}}"
            .Font.Name = "{{footerFont}}"
            .Font.Size = {{footerFontSize}}
            .Font.Bold = {{footerBold}}
            .ParagraphFormat.Alignment = {{footerAlignment}}
        End With

        ' Add page numbers if specified
        If {{includePageNumbers}} Then
            footerRange.Collapse Direction:=wdCollapseEnd
            footerRange.InsertAfter " - Page "
            footerRange.Fields.Add Range:=footerRange, Type:=wdFieldPage
            footerRange.InsertAfter " of "
            footerRange.Fields.Add Range:=footerRange, Type:=wdFieldNumPages
        End If
    End If

    MsgBox "Header and footer setup completed!", vbInformation
End Sub`,
    fields: [
      { id: 'includeHeader', name: 'Include Header', type: 'boolean', required: true, defaultValue: true },
      { id: 'headerText', name: 'Header Text', type: 'text', placeholder: 'Company Name - Document Title', required: false },
      { id: 'headerFont', name: 'Header Font', type: 'select', options: ['Arial', 'Calibri', 'Times New Roman', 'Helvetica'], required: true, defaultValue: 'Calibri' },
      { id: 'headerFontSize', name: 'Header Font Size', type: 'number', required: true, defaultValue: 12 },
      { id: 'headerBold', name: 'Header Bold', type: 'boolean', required: true, defaultValue: true },
      { id: 'headerAlignment', name: 'Header Alignment', type: 'select', options: ['wdAlignParagraphLeft', 'wdAlignParagraphCenter', 'wdAlignParagraphRight'], required: true, defaultValue: 'wdAlignParagraphCenter' },
      { id: 'logoPath', name: 'Logo Path (Optional)', type: 'text', placeholder: 'C:\\Images\\logo.png', required: false },
      { id: 'includeFooter', name: 'Include Footer', type: 'boolean', required: true, defaultValue: true },
      { id: 'footerText', name: 'Footer Text', type: 'text', placeholder: 'Confidential Document', required: false },
      { id: 'footerFont', name: 'Footer Font', type: 'select', options: ['Arial', 'Calibri', 'Times New Roman', 'Helvetica'], required: true, defaultValue: 'Calibri' },
      { id: 'footerFontSize', name: 'Footer Font Size', type: 'number', required: true, defaultValue: 10 },
      { id: 'footerBold', name: 'Footer Bold', type: 'boolean', required: true, defaultValue: false },
      { id: 'footerAlignment', name: 'Footer Alignment', type: 'select', options: ['wdAlignParagraphLeft', 'wdAlignParagraphCenter', 'wdAlignParagraphRight'], required: true, defaultValue: 'wdAlignParagraphCenter' },
      { id: 'includePageNumbers', name: 'Include Page Numbers', type: 'boolean', required: true, defaultValue: true }
    ],
    preview: 'Sets up consistent headers and footers with company branding and page numbers'
  }
];

export const WordVBAGenerator: React.FC = () => {
  const [selectedTemplate, setSelectedTemplate] = useState<WordVBATemplate>(wordVBATemplates[0]);
  const [fieldValues, setFieldValues] = useState<Record<string, any>>({});
  const [generatedCode, setGeneratedCode] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);

  React.useEffect(() => {
    // Initialize field values with defaults
    const initialValues: Record<string, any> = {};
    selectedTemplate.fields.forEach(field => {
      if (field.defaultValue !== undefined) {
        initialValues[field.id] = field.defaultValue;
      }
    });
    setFieldValues(initialValues);
  }, [selectedTemplate]);

  React.useEffect(() => {
    generateCode();
  }, [selectedTemplate, fieldValues]);

  const generateCode = () => {
    let code = selectedTemplate.code;

    // Replace placeholders with actual values
    selectedTemplate.fields.forEach(field => {
      const value = fieldValues[field.id] || field.defaultValue || '';
      const placeholder = `{{${field.id}}}`;

      // Handle different field types
      let replacementValue = value;
      if (field.type === 'boolean') {
        replacementValue = value ? 'True' : 'False';
      } else if (field.type === 'number') {
        replacementValue = value.toString();
      } else if (field.type === 'text' || field.type === 'textarea') {
        replacementValue = `"${value}"`;
      } else if (field.type === 'select') {
        replacementValue = value;
      }

      code = code.replace(new RegExp(placeholder, 'g'), replacementValue);
    });

    setGeneratedCode(code);
  };

  const handleFieldChange = (fieldId: string, value: any) => {
    setFieldValues(prev => ({
      ...prev,
      [fieldId]: value
    }));
  };

  const handleTemplateChange = (templateId: string) => {
    const template = wordVBATemplates.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(template);
    }
  };

  const generateAICode = () => {
    setIsGenerating(true);

    setTimeout(() => {
      // Simulate AI enhancement
      const enhancements = [
        '    \' AI Enhancement: Added error handling',
        '    On Error GoTo ErrorHandler',
        '    \' AI Enhancement: Added progress indication',
        '    Application.StatusBar = "Processing..."',
        '    \' AI Enhancement: Added cleanup',
        '    Application.StatusBar = False',
        '    Exit Sub',
        '',
        'ErrorHandler:',
        '    MsgBox "Error: " & Err.Description, vbCritical',
        '    Application.StatusBar = False'
      ];

      const enhancedCode = generatedCode.replace(
        'End Sub',
        enhancements.join('\n') + '\nEnd Sub'
      );

      setGeneratedCode(enhancedCode);
      setIsGenerating(false);
      toast.success('AI enhancements applied to VBA code!');
    }, 2000);
  };

  const copyCode = () => {
    navigator.clipboard.writeText(generatedCode);
    toast.success('VBA code copied to clipboard!');
  };

  const downloadCode = () => {
    const blob = new Blob([generatedCode], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${selectedTemplate.name.replace(/\s+/g, '_')}.bas`;
    link.click();
    URL.revokeObjectURL(url);
    toast.success('VBA code downloaded as .bas file!');
  };

  const renderField = (field: ConfigField) => {
    const value = fieldValues[field.id] || field.defaultValue || '';

    switch (field.type) {
      case 'text':
        return (
          <Input
            value={value}
            onChange={(e) => handleFieldChange(field.id, e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
          />
        );

      case 'number':
        return (
          <Input
            type="number"
            value={value}
            onChange={(e) => handleFieldChange(field.id, Number(e.target.value))}
            placeholder={field.placeholder}
            required={field.required}
          />
        );

      case 'textarea':
        return (
          <Textarea
            value={value}
            onChange={(e) => handleFieldChange(field.id, e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
            rows={3}
          />
        );

      case 'select':
        return (
          <Select value={value} onValueChange={(val) => handleFieldChange(field.id, val)}>
            <SelectTrigger>
              <SelectValue placeholder="Select option" />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'boolean':
        return (
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={value}
              onChange={(e) => handleFieldChange(field.id, e.target.checked)}
              className="rounded"
            />
            <span className="text-sm">Enable</span>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <File className="w-5 h-5" />
            Word VBA Generator
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Controls */}
            <div className="space-y-6">
              <Tabs defaultValue="templates" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="templates">Templates</TabsTrigger>
                  <TabsTrigger value="config">Configure</TabsTrigger>
                  <TabsTrigger value="advanced">Advanced</TabsTrigger>
                </TabsList>

                <TabsContent value="templates" className="space-y-4">
                  <div>
                    <Label>Select VBA Template</Label>
                    <Select value={selectedTemplate.id} onValueChange={handleTemplateChange}>
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {wordVBATemplates.map((template) => (
                          <SelectItem key={template.id} value={template.id}>
                            {template.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Card className="p-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{selectedTemplate.name}</h4>
                        <div className="flex gap-2">
                          <Badge variant="outline">{selectedTemplate.category}</Badge>
                          <Badge variant={
                            selectedTemplate.complexity === 'Basic' ? 'default' :
                            selectedTemplate.complexity === 'Intermediate' ? 'secondary' : 'destructive'
                          }>
                            {selectedTemplate.complexity}
                          </Badge>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600">{selectedTemplate.description}</p>
                      <p className="text-xs text-gray-500">{selectedTemplate.preview}</p>
                    </div>
                  </Card>

                  <div className="grid grid-cols-1 gap-3">
                    {wordVBATemplates.map((template) => (
                      <Card
                        key={template.id}
                        className={`cursor-pointer transition-all ${
                          selectedTemplate.id === template.id
                            ? 'ring-2 ring-blue-500 bg-blue-50'
                            : 'hover:bg-gray-50'
                        }`}
                        onClick={() => handleTemplateChange(template.id)}
                      >
                        <CardContent className="p-3">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h4 className="font-medium text-sm">{template.name}</h4>
                              <p className="text-xs text-gray-600 mt-1">{template.description}</p>
                            </div>
                            <div className="flex flex-col gap-1 ml-2">
                              <Badge variant="outline" className="text-xs">
                                {template.category}
                              </Badge>
                              <Badge
                                variant={
                                  template.complexity === 'Basic' ? 'default' :
                                  template.complexity === 'Intermediate' ? 'secondary' : 'destructive'
                                }
                                className="text-xs"
                              >
                                {template.complexity}
                              </Badge>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="config" className="space-y-4">
                  <div>
                    <Label className="text-base font-medium">Configuration Fields</Label>
                    <p className="text-sm text-gray-600 mt-1">
                      Configure the parameters for your VBA macro
                    </p>
                  </div>

                  <div className="space-y-4">
                    {selectedTemplate.fields.map((field) => (
                      <div key={field.id} className="space-y-2">
                        <Label htmlFor={field.id} className="flex items-center gap-2">
                          {field.name}
                          {field.required && <span className="text-red-500">*</span>}
                        </Label>
                        {renderField(field)}
                        {field.placeholder && (
                          <p className="text-xs text-gray-500">
                            Example: {field.placeholder}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="advanced" className="space-y-4">
                  <div>
                    <Label className="text-base font-medium">Advanced Options</Label>
                    <p className="text-sm text-gray-600 mt-1">
                      AI enhancements and advanced code generation options
                    </p>
                  </div>

                  <Button
                    onClick={generateAICode}
                    disabled={isGenerating}
                    className="w-full"
                  >
                    {isGenerating ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Applying AI Enhancements...
                      </>
                    ) : (
                      <>
                        <Sparkles className="w-4 h-4 mr-2" />
                        Apply AI Enhancements
                      </>
                    )}
                  </Button>

                  <div className="p-4 bg-purple-50 rounded-lg">
                    <h4 className="font-medium text-purple-900 mb-2">🤖 AI Enhancements</h4>
                    <ul className="text-sm text-purple-800 space-y-1">
                      <li>• Error handling dan exception management</li>
                      <li>• Progress indicators untuk user feedback</li>
                      <li>• Memory cleanup dan optimization</li>
                      <li>• Performance improvements</li>
                      <li>• Code documentation dan comments</li>
                    </ul>
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            {/* Preview */}
            <div className="space-y-4">
              <div>
                <Label className="text-base font-medium">Generated VBA Code</Label>
                <Card className="mt-2">
                  <CardContent className="p-0">
                    <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm overflow-auto max-h-96">
                      <pre className="whitespace-pre-wrap">{generatedCode}</pre>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-2">
                <Label>Export Options</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyCode}
                    disabled={!generatedCode}
                  >
                    <Copy className="w-4 h-4 mr-1" />
                    Copy Code
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={downloadCode}
                    disabled={!generatedCode}
                  >
                    <Download className="w-4 h-4 mr-1" />
                    Download .bas
                  </Button>
                </div>
              </div>

              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">📝 How to Use</h4>
                <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                  <li>Copy the generated VBA code</li>
                  <li>Open Microsoft Word</li>
                  <li>Press Alt+F11 to open VBA Editor</li>
                  <li>Insert → Module</li>
                  <li>Paste the code into the module</li>
                  <li>Press F5 to run the macro</li>
                </ol>
              </div>

              <div className="p-4 bg-amber-50 rounded-lg">
                <h4 className="font-medium text-amber-900 mb-2">⚠️ Important Notes</h4>
                <ul className="text-sm text-amber-800 space-y-1">
                  <li>• Enable macros in Word security settings</li>
                  <li>• Test macros on sample documents first</li>
                  <li>• Backup your documents before running macros</li>
                  <li>• Some features require specific Word versions</li>
                  <li>• Mail merge requires properly formatted data sources</li>
                </ul>
              </div>

              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">✨ Template Features</h4>
                <div className="text-sm text-green-800 space-y-2">
                  <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4" />
                    <span><strong>Mail Merge:</strong> Bulk document generation</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <FileText className="w-4 h-4" />
                    <span><strong>Templates:</strong> Auto-fill document templates</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Table className="w-4 h-4" />
                    <span><strong>Tables:</strong> Dynamic table generation</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Settings className="w-4 h-4" />
                    <span><strong>Formatting:</strong> Consistent styling automation</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
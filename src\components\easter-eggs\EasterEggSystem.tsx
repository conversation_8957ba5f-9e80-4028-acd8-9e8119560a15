import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { <PERSON><PERSON><PERSON>, Gift, Star, Zap } from 'lucide-react';

interface EasterEgg {
  id: string;
  trigger: string;
  title: string;
  message: string;
  emoji: string;
  action?: () => void;
  discovered: boolean;
}

interface EasterEggSystemProps {
  onEasterEggFound: (egg: EasterEgg) => void;
}

export const EasterEggSystem: React.FC<EasterEggSystemProps> = ({ onEasterEggFound }) => {
  const [discoveredEggs, setDiscoveredEggs] = useState<string[]>([]);
  const [konami, setKonami] = useState<string[]>([]);
  const [clickCount, setClickCount] = useState(0);
  const [lastClickTime, setLastClickTime] = useState(0);

  const easterEggs: EasterEgg[] = [
    {
      id: 'konami',
      trigger: 'konami_code',
      title: 'Konami Code Master!',
      message: 'You found the legendary Konami Code! 🎮 Here\'s a special surprise!',
      emoji: '🎮',
      discovered: false,
      action: () => {
        // Add special effects
        document.body.style.animation = 'rainbow 2s infinite';
        setTimeout(() => {
          document.body.style.animation = '';
        }, 5000);
      }
    },
    {
      id: 'triple_click',
      trigger: 'triple_click_logo',
      title: 'Speed Clicker!',
      message: 'Wow! You clicked the logo 3 times super fast! ⚡',
      emoji: '⚡',
      discovered: false,
      action: () => {
        // Add sparkle effect
        createSparkles();
      }
    },
    {
      id: 'secret_message',
      trigger: 'type_secret',
      title: 'Secret Message Found!',
      message: 'You typed the secret phrase! KIKAZE-AI loves you! 💖',
      emoji: '💖',
      discovered: false
    },
    {
      id: 'midnight_user',
      trigger: 'midnight_access',
      title: 'Midnight Warrior!',
      message: 'Using KIKAZE-AI at midnight? You\'re dedicated! 🌙',
      emoji: '🌙',
      discovered: false
    },
    {
      id: 'tool_master',
      trigger: 'use_all_tools',
      title: 'Tool Master!',
      message: 'You\'ve used all available tools! You\'re a true power user! 🛠️',
      emoji: '🛠️',
      discovered: false
    },
    {
      id: 'chat_marathon',
      trigger: 'long_conversation',
      title: 'Chat Marathon!',
      message: 'You\'ve been chatting for over 50 messages! Impressive! 💬',
      emoji: '💬',
      discovered: false
    },
    {
      id: 'early_bird',
      trigger: 'early_morning',
      title: 'Early Bird!',
      message: 'Good morning, early bird! Starting your day with AI! 🌅',
      emoji: '🌅',
      discovered: false
    },
    {
      id: 'weekend_warrior',
      trigger: 'weekend_usage',
      title: 'Weekend Warrior!',
      message: 'Working on weekends? You\'re unstoppable! 💪',
      emoji: '💪',
      discovered: false
    }
  ];

  const konamiCode = [
    'ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown',
    'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight',
    'KeyB', 'KeyA'
  ];

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      const newKonami = [...konami, event.code];
      
      if (newKonami.length > konamiCode.length) {
        newKonami.shift();
      }
      
      setKonami(newKonami);
      
      if (newKonami.length === konamiCode.length && 
          newKonami.every((key, index) => key === konamiCode[index])) {
        triggerEasterEgg('konami');
        setKonami([]);
      }
    };

    const handleTyping = (event: KeyboardEvent) => {
      // Check for secret phrases
      const secretPhrases = ['kikaze', 'secret', 'easter', 'surprise'];
      const target = event.target as HTMLInputElement;
      
      if (target && target.value) {
        const value = target.value.toLowerCase();
        secretPhrases.forEach(phrase => {
          if (value.includes(phrase) && !discoveredEggs.includes('secret_message')) {
            triggerEasterEgg('secret_message');
          }
        });
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('input', handleTyping);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('input', handleTyping);
    };
  }, [konami, discoveredEggs]);

  useEffect(() => {
    // Check time-based easter eggs
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay();

    if (hour >= 0 && hour < 6 && !discoveredEggs.includes('midnight_user')) {
      triggerEasterEgg('midnight_user');
    }

    if (hour >= 5 && hour < 8 && !discoveredEggs.includes('early_bird')) {
      triggerEasterEgg('early_bird');
    }

    if ((day === 0 || day === 6) && !discoveredEggs.includes('weekend_warrior')) {
      triggerEasterEgg('weekend_warrior');
    }
  }, [discoveredEggs]);

  const triggerEasterEgg = (eggId: string) => {
    if (discoveredEggs.includes(eggId)) return;

    const egg = easterEggs.find(e => e.id === eggId);
    if (!egg) return;

    setDiscoveredEggs(prev => [...prev, eggId]);
    
    // Execute egg action if exists
    if (egg.action) {
      egg.action();
    }

    // Show toast notification
    toast.success(
      <div className="flex items-center gap-2">
        <span className="text-2xl">{egg.emoji}</span>
        <div>
          <div className="font-bold">{egg.title}</div>
          <div className="text-sm">{egg.message}</div>
        </div>
      </div>,
      { duration: 5000 }
    );

    // Trigger callback
    onEasterEggFound({ ...egg, discovered: true });
  };

  const handleLogoClick = () => {
    const now = Date.now();
    
    if (now - lastClickTime < 500) {
      setClickCount(prev => prev + 1);
    } else {
      setClickCount(1);
    }
    
    setLastClickTime(now);
    
    if (clickCount >= 2 && !discoveredEggs.includes('triple_click')) {
      triggerEasterEgg('triple_click');
      setClickCount(0);
    }
  };

  const createSparkles = () => {
    for (let i = 0; i < 20; i++) {
      setTimeout(() => {
        const sparkle = document.createElement('div');
        sparkle.innerHTML = '✨';
        sparkle.style.position = 'fixed';
        sparkle.style.left = Math.random() * window.innerWidth + 'px';
        sparkle.style.top = Math.random() * window.innerHeight + 'px';
        sparkle.style.fontSize = '20px';
        sparkle.style.pointerEvents = 'none';
        sparkle.style.zIndex = '9999';
        sparkle.style.animation = 'sparkle 2s ease-out forwards';
        
        document.body.appendChild(sparkle);
        
        setTimeout(() => {
          document.body.removeChild(sparkle);
        }, 2000);
      }, i * 100);
    }
  };

  // Public methods for triggering easter eggs from other components
  const checkToolUsage = (toolsUsed: string[]) => {
    // Assuming we have 15+ tools available
    if (toolsUsed.length >= 10 && !discoveredEggs.includes('tool_master')) {
      triggerEasterEgg('tool_master');
    }
  };

  const checkChatLength = (messageCount: number) => {
    if (messageCount >= 50 && !discoveredEggs.includes('chat_marathon')) {
      triggerEasterEgg('chat_marathon');
    }
  };

  // Expose methods for external use
  useEffect(() => {
    (window as any).easterEggs = {
      checkToolUsage,
      checkChatLength,
      triggerEasterEgg
    };
  }, [discoveredEggs]);

  return (
    <>
      {/* Logo click handler */}
      <div 
        onClick={handleLogoClick}
        style={{ position: 'absolute', top: 0, left: 0, width: '100px', height: '50px', zIndex: 1000 }}
      />
      
      {/* CSS for animations */}
      <style>{`
        @keyframes rainbow {
          0% { filter: hue-rotate(0deg); }
          100% { filter: hue-rotate(360deg); }
        }

        @keyframes sparkle {
          0% {
            opacity: 1;
            transform: scale(0) rotate(0deg);
          }
          50% {
            opacity: 1;
            transform: scale(1) rotate(180deg);
          }
          100% {
            opacity: 0;
            transform: scale(0) rotate(360deg);
          }
        }
      `}</style>
    </>
  );
};

// Easter Egg Collection Component
export const EasterEggCollection: React.FC<{ discoveredEggs: EasterEgg[] }> = ({ discoveredEggs }) => {
  return (
    <div className="space-y-4">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">🥚 Easter Egg Collection</h2>
        <p className="text-gray-600">
          Discovered: {discoveredEggs.length} / 8 Easter Eggs
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {discoveredEggs.map((egg) => (
          <div key={egg.id} className="p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200">
            <div className="flex items-center gap-3 mb-2">
              <span className="text-3xl">{egg.emoji}</span>
              <div>
                <h3 className="font-bold text-yellow-800">{egg.title}</h3>
                <p className="text-sm text-yellow-600">Discovered!</p>
              </div>
            </div>
            <p className="text-sm text-gray-700">{egg.message}</p>
          </div>
        ))}
      </div>

      {discoveredEggs.length === 0 && (
        <div className="text-center p-8 bg-gray-50 rounded-lg">
          <div className="text-6xl mb-4">🔍</div>
          <h3 className="text-lg font-semibold text-gray-700 mb-2">No Easter Eggs Found Yet</h3>
          <p className="text-gray-500">
            Explore KIKAZE-AI to discover hidden surprises!
          </p>
          <div className="mt-4 text-sm text-gray-400">
            <p>💡 Hints:</p>
            <p>• Try using different tools</p>
            <p>• Click around the interface</p>
            <p>• Use the app at different times</p>
            <p>• Type secret words...</p>
          </div>
        </div>
      )}
    </div>
  );
};

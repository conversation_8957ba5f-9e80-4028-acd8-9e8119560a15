import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { Copy, RefreshCw } from 'lucide-react';

export const HashGenerator: React.FC = () => {
  const [input, setInput] = useState('');
  const [hashes, setHashes] = useState({
    md5: '',
    sha1: '',
    sha256: '',
    sha512: ''
  });

  // Function to generate hash using Web Crypto API
  const generateHash = async (algorithm: string, text: string): Promise<string> => {
    const encoder = new TextEncoder();
    const data = encoder.encode(text);
    
    try {
      const hashBuffer = await crypto.subtle.digest(algorithm, data);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      return hashHex;
    } catch (error) {
      console.error(`Error generating ${algorithm}:`, error);
      return 'Error generating hash';
    }
  };

  // Simple MD5 implementation (for demo purposes)
  const md5 = (str: string): string => {
    // This is a simplified version - in production, use a proper crypto library
    let hash = 0;
    if (str.length === 0) return hash.toString();
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(16).padStart(8, '0');
  };

  const generateAllHashes = async () => {
    if (!input.trim()) {
      toast.error('Masukkan teks terlebih dahulu!');
      return;
    }

    try {
      const [sha1Hash, sha256Hash, sha512Hash] = await Promise.all([
        generateHash('SHA-1', input),
        generateHash('SHA-256', input),
        generateHash('SHA-512', input)
      ]);

      setHashes({
        md5: md5(input), // Simple MD5 for demo
        sha1: sha1Hash,
        sha256: sha256Hash,
        sha512: sha512Hash
      });

      toast.success('Hash berhasil digenerate!');
    } catch (error) {
      toast.error('Gagal generate hash');
    }
  };

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(`${type} berhasil disalin ke clipboard!`);
    } catch (error) {
      toast.error('Gagal menyalin ke clipboard');
    }
  };

  const clearAll = () => {
    setInput('');
    setHashes({
      md5: '',
      sha1: '',
      sha256: '',
      sha512: ''
    });
    toast.success('Semua field berhasil dibersihkan!');
  };

  // Auto-generate hashes when input changes (debounced)
  useEffect(() => {
    const timer = setTimeout(() => {
      if (input.trim()) {
        generateAllHashes();
      } else {
        setHashes({
          md5: '',
          sha1: '',
          sha256: '',
          sha512: ''
        });
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [input]);

  const hashTypes = [
    { key: 'md5', label: 'MD5', description: '128-bit hash (tidak aman untuk keamanan)' },
    { key: 'sha1', label: 'SHA-1', description: '160-bit hash (deprecated untuk keamanan)' },
    { key: 'sha256', label: 'SHA-256', description: '256-bit hash (aman)' },
    { key: 'sha512', label: 'SHA-512', description: '512-bit hash (sangat aman)' }
  ];

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔐 Hash Generator
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Input Section */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Input Text:</label>
            <Textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="Masukkan teks yang ingin di-hash..."
              className="min-h-[100px]"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button onClick={generateAllHashes} disabled={!input.trim()}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Generate Hashes
            </Button>
            <Button variant="outline" onClick={clearAll}>
              🗑️ Clear All
            </Button>
          </div>

          {/* Hash Results */}
          <div className="space-y-4">
            {hashTypes.map(({ key, label, description }) => (
              <div key={key} className="space-y-2">
                <div className="flex justify-between items-center">
                  <div>
                    <label className="text-sm font-medium">{label}:</label>
                    <p className="text-xs text-gray-500">{description}</p>
                  </div>
                  {hashes[key as keyof typeof hashes] && (
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => copyToClipboard(hashes[key as keyof typeof hashes], label)}
                    >
                      <Copy className="w-4 h-4 mr-1" />
                      Copy
                    </Button>
                  )}
                </div>
                <Input
                  value={hashes[key as keyof typeof hashes]}
                  readOnly
                  className="font-mono text-sm bg-gray-50"
                  placeholder={`${label} hash akan muncul di sini...`}
                />
              </div>
            ))}
          </div>

          {/* Info Section */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">ℹ️ Informasi Hash:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• <strong>MD5</strong>: Cepat tapi tidak aman untuk keamanan</li>
              <li>• <strong>SHA-1</strong>: Deprecated, jangan gunakan untuk keamanan</li>
              <li>• <strong>SHA-256</strong>: Standar aman untuk kebanyakan aplikasi</li>
              <li>• <strong>SHA-512</strong>: Paling aman, cocok untuk data sensitif</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { <PERSON><PERSON>, ExternalLink, BarChart3, Link } from 'lucide-react';

interface ShortenedUrl {
  id: string;
  originalUrl: string;
  shortUrl: string;
  clicks: number;
  createdAt: Date;
}

export const UrlShortener: React.FC = () => {
  const [originalUrl, setOriginalUrl] = useState('');
  const [customAlias, setCustomAlias] = useState('');
  const [shortenedUrls, setShortenedUrls] = useState<ShortenedUrl[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const generateShortCode = (length: number = 6): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdef<PERSON>ijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  };

  const shortenUrl = async () => {
    if (!originalUrl.trim()) {
      toast.error('Masukkan URL yang ingin dipendekkan!');
      return;
    }

    if (!isValidUrl(originalUrl)) {
      toast.error('URL tidak valid! Pastikan URL dimulai dengan http:// atau https://');
      return;
    }

    setIsLoading(true);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      const shortCode = customAlias.trim() || generateShortCode();
      
      // Check if custom alias already exists
      if (customAlias.trim() && shortenedUrls.some(url => url.shortUrl.endsWith(shortCode))) {
        toast.error('Alias sudah digunakan! Pilih alias lain.');
        setIsLoading(false);
        return;
      }

      const newShortenedUrl: ShortenedUrl = {
        id: Date.now().toString(),
        originalUrl,
        shortUrl: `https://short.ly/${shortCode}`,
        clicks: 0,
        createdAt: new Date()
      };

      setShortenedUrls(prev => [newShortenedUrl, ...prev]);
      setOriginalUrl('');
      setCustomAlias('');
      toast.success('URL berhasil dipendekkan!');
    } catch (error) {
      toast.error('Gagal mempendekkan URL');
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('URL berhasil disalin ke clipboard!');
    } catch (error) {
      toast.error('Gagal menyalin ke clipboard');
    }
  };

  const simulateClick = (id: string) => {
    setShortenedUrls(prev => 
      prev.map(url => 
        url.id === id 
          ? { ...url, clicks: url.clicks + 1 }
          : url
      )
    );
    toast.success('Click recorded! (Simulasi)');
  };

  const deleteUrl = (id: string) => {
    setShortenedUrls(prev => prev.filter(url => url.id !== id));
    toast.success('URL berhasil dihapus!');
  };

  const clearAll = () => {
    setShortenedUrls([]);
    setOriginalUrl('');
    setCustomAlias('');
    toast.success('Semua URL berhasil dihapus!');
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔗 URL Shortener
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* URL Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium">URL yang ingin dipendekkan:</label>
            <Input
              value={originalUrl}
              onChange={(e) => setOriginalUrl(e.target.value)}
              placeholder="https://example.com/very/long/url/that/needs/shortening"
              className="w-full"
            />
          </div>

          {/* Custom Alias */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Custom Alias (opsional):</label>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">https://short.ly/</span>
              <Input
                value={customAlias}
                onChange={(e) => setCustomAlias(e.target.value.replace(/[^a-zA-Z0-9-_]/g, ''))}
                placeholder="my-custom-link"
                className="flex-1"
                maxLength={20}
              />
            </div>
            <p className="text-xs text-gray-500">
              Hanya huruf, angka, tanda hubung (-) dan underscore (_) yang diperbolehkan
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button 
              onClick={shortenUrl} 
              disabled={isLoading || !originalUrl.trim()}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Memproses...
                </>
              ) : (
                <>
                  <Link className="w-4 h-4 mr-2" />
                  Pendekkan URL
                </>
              )}
            </Button>
            {shortenedUrls.length > 0 && (
              <Button variant="outline" onClick={clearAll}>
                🗑️ Clear All
              </Button>
            )}
          </div>

          {/* Results */}
          {shortenedUrls.length > 0 && (
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">URL yang Dipendekkan:</h3>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {shortenedUrls.map((url) => (
                  <Card key={url.id} className="p-4">
                    <div className="space-y-3">
                      {/* Short URL */}
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-blue-600 truncate">
                            {url.shortUrl}
                          </p>
                          <p className="text-xs text-gray-500 truncate">
                            {url.originalUrl}
                          </p>
                        </div>
                        <div className="flex gap-1 ml-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard(url.shortUrl)}
                          >
                            <Copy className="w-3 h-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => simulateClick(url.id)}
                          >
                            <ExternalLink className="w-3 h-3" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => deleteUrl(url.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            🗑️
                          </Button>
                        </div>
                      </div>

                      {/* Analytics */}
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <div className="flex items-center gap-4">
                          <span className="flex items-center gap-1">
                            <BarChart3 className="w-3 h-3" />
                            {url.clicks} clicks
                          </span>
                          <span>
                            Created: {url.createdAt.toLocaleDateString('id-ID')}
                          </span>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Info */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">ℹ️ Fitur URL Shortener:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Pendekkan URL panjang menjadi lebih mudah dibagikan</li>
              <li>• Custom alias untuk URL yang mudah diingat</li>
              <li>• Tracking jumlah klik (simulasi)</li>
              <li>• Copy URL pendek dengan satu klik</li>
              <li>• Validasi URL otomatis</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

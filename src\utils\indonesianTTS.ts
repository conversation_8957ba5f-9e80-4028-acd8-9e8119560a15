// Advanced Indonesian Text-to-Speech System
export interface IndonesianVoice {
  id: string;
  name: string;
  displayName: string;
  gender: 'male' | 'female';
  age: 'young' | 'adult' | 'mature';
  region: string;
  accent: string;
  quality: 'standard' | 'premium' | 'neural';
  description: string;
  voice?: SpeechSynthesisVoice;
  isAvailable: boolean;
  priority: number;
}

export interface TTSSettings {
  rate: number;
  pitch: number;
  volume: number;
  voice: string;
  naturalPauses: boolean;
  emotionalTone: 'neutral' | 'friendly' | 'professional' | 'enthusiastic' | 'calm';
  pronunciation: 'standard' | 'formal' | 'casual';
}

export class IndonesianTTSManager {
  private static instance: IndonesianTTSManager;
  private voices: IndonesianVoice[] = [];
  private currentUtterance: SpeechSynthesisUtterance | null = null;
  private settings: TTSSettings = {
    rate: 1.0,
    pitch: 1.0,
    volume: 1.0,
    voice: '',
    naturalPauses: true,
    emotionalTone: 'friendly',
    pronunciation: 'standard'
  };

  // Indonesian voice profiles with fallbacks
  private indonesianVoiceProfiles: IndonesianVoice[] = [
    {
      id: 'id-female-young',
      name: 'Sari',
      displayName: '<PERSON><PERSON> (<PERSON><PERSON>)',
      gender: 'female',
      age: 'young',
      region: 'Jakarta',
      accent: 'Standard Jakarta',
      quality: 'neural',
      description: 'Suara wanita muda yang ceria dan natural dari Jakarta',
      isAvailable: false,
      priority: 1
    },
    {
      id: 'id-male-adult',
      name: 'Budi',
      displayName: 'Budi (Pria Dewasa)',
      gender: 'male',
      age: 'adult',
      region: 'Jakarta',
      accent: 'Standard Jakarta',
      quality: 'neural',
      description: 'Suara pria dewasa yang profesional dan jelas',
      isAvailable: false,
      priority: 2
    },
    {
      id: 'id-female-adult',
      name: 'Dewi',
      displayName: 'Dewi (Wanita Dewasa)',
      gender: 'female',
      age: 'adult',
      region: 'Bandung',
      accent: 'Sunda-Jakarta',
      quality: 'premium',
      description: 'Suara wanita dewasa yang hangat dan ramah',
      isAvailable: false,
      priority: 3
    },
    {
      id: 'id-male-mature',
      name: 'Pak Harto',
      displayName: 'Pak Harto (Pria Matang)',
      gender: 'male',
      age: 'mature',
      region: 'Yogyakarta',
      accent: 'Jawa-Jakarta',
      quality: 'premium',
      description: 'Suara pria matang yang berwibawa dan tenang',
      isAvailable: false,
      priority: 4
    },
    {
      id: 'id-female-mature',
      name: 'Bu Siti',
      displayName: 'Bu Siti (Wanita Matang)',
      gender: 'female',
      age: 'mature',
      region: 'Surabaya',
      accent: 'Jawa Timur',
      quality: 'standard',
      description: 'Suara wanita matang yang bijaksana dan motherly',
      isAvailable: false,
      priority: 5
    }
  ];

  private constructor() {
    this.initializeVoices();
  }

  public static getInstance(): IndonesianTTSManager {
    if (!IndonesianTTSManager.instance) {
      IndonesianTTSManager.instance = new IndonesianTTSManager();
    }
    return IndonesianTTSManager.instance;
  }

  private async initializeVoices(): Promise<void> {
    // Wait for voices to be loaded
    if (speechSynthesis.getVoices().length === 0) {
      await new Promise(resolve => {
        speechSynthesis.onvoiceschanged = resolve;
      });
    }

    const systemVoices = speechSynthesis.getVoices();
    this.mapSystemVoicesToProfiles(systemVoices);
    this.loadSettings();
  }

  private mapSystemVoicesToProfiles(systemVoices: SpeechSynthesisVoice[]): void {
    // Indonesian voice patterns to match
    const indonesianPatterns = [
      /indonesia/i,
      /id[-_]id/i,
      /bahasa/i,
      /jakarta/i,
      /java/i,
      /sunda/i
    ];

    // Female voice patterns
    const femalePatterns = [
      /female/i,
      /woman/i,
      /girl/i,
      /sari/i,
      /dewi/i,
      /siti/i,
      /rina/i,
      /maya/i,
      /indira/i
    ];

    // Male voice patterns
    const malePatterns = [
      /male/i,
      /man/i,
      /boy/i,
      /budi/i,
      /andi/i,
      /harto/i,
      /agus/i,
      /rudi/i
    ];

    // Find Indonesian voices
    const indonesianVoices = systemVoices.filter(voice => 
      voice.lang.startsWith('id') || 
      indonesianPatterns.some(pattern => pattern.test(voice.name))
    );

    // Map to profiles
    this.voices = [...this.indonesianVoiceProfiles];

    indonesianVoices.forEach(voice => {
      const isFemale = femalePatterns.some(pattern => pattern.test(voice.name));
      const isMale = malePatterns.some(pattern => pattern.test(voice.name));
      
      // Find best matching profile
      let bestMatch = this.voices.find(profile => 
        !profile.isAvailable && 
        ((isFemale && profile.gender === 'female') || 
         (isMale && profile.gender === 'male') ||
         (!isFemale && !isMale))
      );

      if (!bestMatch) {
        // Create new profile for unmatched voice
        bestMatch = {
          id: `custom-${voice.name.toLowerCase().replace(/\s+/g, '-')}`,
          name: voice.name,
          displayName: voice.name,
          gender: isFemale ? 'female' : isMale ? 'male' : 'female',
          age: 'adult',
          region: 'Indonesia',
          accent: 'Standard',
          quality: 'standard',
          description: `Suara ${isFemale ? 'wanita' : 'pria'} Indonesia`,
          isAvailable: false,
          priority: 10
        };
        this.voices.push(bestMatch);
      }

      bestMatch.voice = voice;
      bestMatch.isAvailable = true;
    });

    // Add fallback English voices with Indonesian accent simulation
    const englishVoices = systemVoices.filter(voice => 
      voice.lang.startsWith('en') && 
      (voice.name.includes('Google') || voice.name.includes('Microsoft'))
    );

    englishVoices.slice(0, 2).forEach((voice, index) => {
      const isFemale = femalePatterns.some(pattern => pattern.test(voice.name)) || index % 2 === 0;
      
      this.voices.push({
        id: `fallback-${index}`,
        name: `Indonesian ${isFemale ? 'Female' : 'Male'}`,
        displayName: `Suara ${isFemale ? 'Wanita' : 'Pria'} Indonesia (Fallback)`,
        gender: isFemale ? 'female' : 'male',
        age: 'adult',
        region: 'Indonesia',
        accent: 'Standard',
        quality: 'standard',
        description: `Suara ${isFemale ? 'wanita' : 'pria'} dengan aksen Indonesia`,
        voice: voice,
        isAvailable: true,
        priority: 15
      });
    });

    // Sort by priority and availability
    this.voices.sort((a, b) => {
      if (a.isAvailable !== b.isAvailable) {
        return a.isAvailable ? -1 : 1;
      }
      return a.priority - b.priority;
    });

    // Set default voice
    if (!this.settings.voice && this.voices.length > 0) {
      const defaultVoice = this.voices.find(v => v.isAvailable);
      if (defaultVoice) {
        this.settings.voice = defaultVoice.id;
      }
    }
  }

  public getAvailableVoices(): IndonesianVoice[] {
    return this.voices.filter(voice => voice.isAvailable);
  }

  public getVoiceById(id: string): IndonesianVoice | undefined {
    return this.voices.find(voice => voice.id === id);
  }

  public async speak(text: string, options?: Partial<TTSSettings>): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Stop current speech
        this.stop();

        // Merge settings
        const currentSettings = { ...this.settings, ...options };
        
        // Preprocess text for better Indonesian pronunciation
        const processedText = this.preprocessIndonesianText(text, currentSettings);
        
        // Create utterance
        const utterance = new SpeechSynthesisUtterance(processedText);
        
        // Set voice
        const selectedVoice = this.getVoiceById(currentSettings.voice);
        if (selectedVoice?.voice) {
          utterance.voice = selectedVoice.voice;
          utterance.lang = 'id-ID';
        }

        // Apply settings with emotional adjustments
        this.applyEmotionalSettings(utterance, currentSettings);
        
        // Set event handlers
        utterance.onstart = () => {
          console.log('TTS started');
        };

        utterance.onend = () => {
          this.currentUtterance = null;
          resolve();
        };

        utterance.onerror = (event) => {
          this.currentUtterance = null;
          reject(new Error(`TTS Error: ${event.error}`));
        };

        // Store current utterance
        this.currentUtterance = utterance;
        
        // Speak
        speechSynthesis.speak(utterance);
        
      } catch (error) {
        reject(error);
      }
    });
  }

  private preprocessIndonesianText(text: string, settings: TTSSettings): string {
    let processedText = text;

    // Add natural pauses if enabled
    if (settings.naturalPauses) {
      // Add pauses after punctuation
      processedText = processedText
        .replace(/\./g, '. ')
        .replace(/,/g, ', ')
        .replace(/;/g, '; ')
        .replace(/:/g, ': ')
        .replace(/\?/g, '? ')
        .replace(/!/g, '! ');

      // Add longer pauses for paragraph breaks
      processedText = processedText.replace(/\n\n/g, '. . . ');
      processedText = processedText.replace(/\n/g, '. ');
    }

    // Pronunciation adjustments based on setting
    if (settings.pronunciation === 'formal') {
      // More formal pronunciation
      processedText = processedText
        .replace(/\bgue\b/gi, 'gua')
        .replace(/\blo\b/gi, 'loh')
        .replace(/\bgak\b/gi, 'tidak')
        .replace(/\budah\b/gi, 'sudah');
    } else if (settings.pronunciation === 'casual') {
      // More casual pronunciation
      processedText = processedText
        .replace(/\btidak\b/gi, 'gak')
        .replace(/\bsudah\b/gi, 'udah')
        .replace(/\bsaya\b/gi, 'aku');
    }

    // Handle common Indonesian words that might be mispronounced
    const pronunciationMap: { [key: string]: string } = {
      'AI': 'A I',
      'API': 'A P I',
      'UI': 'U I',
      'UX': 'U X',
      'HTML': 'H T M L',
      'CSS': 'C S S',
      'JavaScript': 'Java Script',
      'React': 'Ri-akt',
      'Vue': 'Vyu',
      'Angular': 'Ang-gu-lar'
    };

    Object.entries(pronunciationMap).forEach(([word, pronunciation]) => {
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      processedText = processedText.replace(regex, pronunciation);
    });

    return processedText;
  }

  private applyEmotionalSettings(utterance: SpeechSynthesisUtterance, settings: TTSSettings): void {
    // Base settings
    utterance.rate = settings.rate;
    utterance.pitch = settings.pitch;
    utterance.volume = settings.volume;

    // Emotional tone adjustments
    switch (settings.emotionalTone) {
      case 'friendly':
        utterance.rate = Math.min(settings.rate * 1.1, 2.0);
        utterance.pitch = Math.min(settings.pitch * 1.1, 2.0);
        break;
      
      case 'professional':
        utterance.rate = settings.rate * 0.9;
        utterance.pitch = settings.pitch * 0.95;
        break;
      
      case 'enthusiastic':
        utterance.rate = Math.min(settings.rate * 1.2, 2.0);
        utterance.pitch = Math.min(settings.pitch * 1.2, 2.0);
        break;
      
      case 'calm':
        utterance.rate = settings.rate * 0.8;
        utterance.pitch = settings.pitch * 0.9;
        break;
      
      default: // neutral
        // Keep original settings
        break;
    }
  }

  public pause(): void {
    if (speechSynthesis.speaking && !speechSynthesis.paused) {
      speechSynthesis.pause();
    }
  }

  public resume(): void {
    if (speechSynthesis.paused) {
      speechSynthesis.resume();
    }
  }

  public stop(): void {
    speechSynthesis.cancel();
    this.currentUtterance = null;
  }

  public isPlaying(): boolean {
    return speechSynthesis.speaking;
  }

  public isPaused(): boolean {
    return speechSynthesis.paused;
  }

  public updateSettings(newSettings: Partial<TTSSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    this.saveSettings();
  }

  public getSettings(): TTSSettings {
    return { ...this.settings };
  }

  private loadSettings(): void {
    try {
      const saved = localStorage.getItem('kikaze_tts_settings');
      if (saved) {
        this.settings = { ...this.settings, ...JSON.parse(saved) };
      }
    } catch (error) {
      console.error('Error loading TTS settings:', error);
    }
  }

  private saveSettings(): void {
    try {
      localStorage.setItem('kikaze_tts_settings', JSON.stringify(this.settings));
    } catch (error) {
      console.error('Error saving TTS settings:', error);
    }
  }

  public testVoice(voiceId: string): void {
    const voice = this.getVoiceById(voiceId);
    if (voice) {
      const testText = `Halo, saya ${voice.name}. Ini adalah contoh suara saya. Bagaimana menurut Anda?`;
      this.speak(testText, { voice: voiceId });
    }
  }

  public getVoiceRecommendation(context: 'chat' | 'presentation' | 'reading' | 'notification'): string {
    const availableVoices = this.getAvailableVoices();
    
    switch (context) {
      case 'chat':
        // Prefer young, friendly voices for chat
        return availableVoices.find(v => v.age === 'young' && v.gender === 'female')?.id ||
               availableVoices.find(v => v.gender === 'female')?.id ||
               availableVoices[0]?.id || '';
      
      case 'presentation':
        // Prefer professional, adult voices
        return availableVoices.find(v => v.age === 'adult' && v.gender === 'male')?.id ||
               availableVoices.find(v => v.age === 'adult')?.id ||
               availableVoices[0]?.id || '';
      
      case 'reading':
        // Prefer calm, mature voices
        return availableVoices.find(v => v.age === 'mature')?.id ||
               availableVoices.find(v => v.age === 'adult')?.id ||
               availableVoices[0]?.id || '';
      
      case 'notification':
        // Prefer clear, young voices
        return availableVoices.find(v => v.age === 'young')?.id ||
               availableVoices[0]?.id || '';
      
      default:
        return availableVoices[0]?.id || '';
    }
  }
}

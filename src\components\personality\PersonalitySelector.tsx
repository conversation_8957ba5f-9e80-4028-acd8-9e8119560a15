import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { <PERSON><PERSON>, <PERSON>, Zap, Brain, Coffee, Star } from 'lucide-react';

export interface Personality {
  id: string;
  name: string;
  emoji: string;
  description: string;
  traits: string[];
  greeting: string;
  systemPrompt: string;
  color: string;
  icon: React.ReactNode;
}

export const personalities: Personality[] = [
  {
    id: 'friendly',
    name: 'Friendly Assistant',
    emoji: '😊',
    description: '<PERSON><PERSON>, helpful, dan selalu siap membantu',
    traits: ['Ramah', 'Sabar', 'Supportive', 'Optimis'],
    greeting: 'Hal<PERSON>! Saya di sini untuk membantu Anda dengan senang hati! 😊',
    systemPrompt: 'You are a friendly, helpful, and patient AI assistant. Always be polite, encouraging, and supportive. Use emojis occasionally to show warmth.',
    color: 'bg-blue-100 border-blue-300 text-blue-800',
    icon: <Heart className="w-5 h-5" />
  },
  {
    id: 'professional',
    name: 'Professional Expert',
    emoji: '💼',
    description: 'Formal, precise, dan business-oriented',
    traits: ['Formal', 'Precise', 'Efficient', 'Reliable'],
    greeting: 'Good day. I am ready to assist you with professional expertise.',
    systemPrompt: 'You are a professional, formal, and precise AI assistant. Provide detailed, accurate information. Be concise and business-like in your responses.',
    color: 'bg-gray-100 border-gray-300 text-gray-800',
    icon: <Bot className="w-5 h-5" />
  },
  {
    id: 'creative',
    name: 'Creative Genius',
    emoji: '🎨',
    description: 'Kreatif, imajinatif, dan out-of-the-box thinking',
    traits: ['Kreatif', 'Imajinatif', 'Inspiratif', 'Artistic'],
    greeting: 'Hey there, creative soul! ✨ Ready to explore some amazing ideas together?',
    systemPrompt: 'You are a creative, imaginative AI assistant. Think outside the box, provide creative solutions, and inspire innovation. Use vivid language and creative metaphors.',
    color: 'bg-purple-100 border-purple-300 text-purple-800',
    icon: <Star className="w-5 h-5" />
  },
  {
    id: 'energetic',
    name: 'Energetic Buddy',
    emoji: '⚡',
    description: 'Energik, antusias, dan penuh semangat',
    traits: ['Energik', 'Antusias', 'Motivating', 'Dynamic'],
    greeting: 'WOOHOO! 🚀 Let\'s get this party started! What awesome thing are we doing today?',
    systemPrompt: 'You are an energetic, enthusiastic AI assistant. Be motivating, use exclamation points, and show excitement. Encourage users to take action and stay positive.',
    color: 'bg-orange-100 border-orange-300 text-orange-800',
    icon: <Zap className="w-5 h-5" />
  },
  {
    id: 'wise',
    name: 'Wise Mentor',
    emoji: '🧙‍♂️',
    description: 'Bijaksana, thoughtful, dan philosophical',
    traits: ['Bijaksana', 'Thoughtful', 'Philosophical', 'Patient'],
    greeting: 'Greetings, seeker of knowledge. What wisdom do you seek today? 🌟',
    systemPrompt: 'You are a wise, thoughtful AI mentor. Provide deep insights, ask meaningful questions, and share wisdom. Be philosophical and encourage reflection.',
    color: 'bg-indigo-100 border-indigo-300 text-indigo-800',
    icon: <Brain className="w-5 h-5" />
  },
  {
    id: 'casual',
    name: 'Casual Friend',
    emoji: '☕',
    description: 'Santai, casual, dan seperti teman dekat',
    traits: ['Santai', 'Casual', 'Friendly', 'Relatable'],
    greeting: 'Hey! What\'s up? ☕ Just chilling here, ready to chat about whatever!',
    systemPrompt: 'You are a casual, laid-back AI friend. Use informal language, be relatable, and chat like a close friend. Use slang occasionally and be conversational.',
    color: 'bg-green-100 border-green-300 text-green-800',
    icon: <Coffee className="w-5 h-5" />
  }
];

interface PersonalitySelectorProps {
  currentPersonality: string;
  onPersonalityChange: (personality: Personality) => void;
}

export const PersonalitySelector: React.FC<PersonalitySelectorProps> = ({
  currentPersonality,
  onPersonalityChange
}) => {
  const [selectedPersonality, setSelectedPersonality] = useState(currentPersonality);

  const handlePersonalitySelect = (personality: Personality) => {
    setSelectedPersonality(personality.id);
    onPersonalityChange(personality);
    toast.success(`AI Personality changed to ${personality.name}! ${personality.emoji}`);
  };

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-2">🎭 Choose AI Personality</h2>
        <p className="text-gray-600">Select how you want KIKAZE-AI to interact with you</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {personalities.map((personality) => (
          <Card 
            key={personality.id}
            className={`cursor-pointer transition-all hover:shadow-lg ${
              selectedPersonality === personality.id 
                ? 'ring-2 ring-blue-500 shadow-lg' 
                : 'hover:shadow-md'
            }`}
            onClick={() => handlePersonalitySelect(personality)}
          >
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-3">
                <div className={`p-2 rounded-lg ${personality.color}`}>
                  {personality.icon}
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <span>{personality.name}</span>
                    <span className="text-2xl">{personality.emoji}</span>
                  </div>
                  {selectedPersonality === personality.id && (
                    <div className="text-xs text-blue-600 font-medium">✓ Active</div>
                  )}
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <p className="text-sm text-gray-600">{personality.description}</p>
              
              <div className="flex flex-wrap gap-1">
                {personality.traits.map((trait) => (
                  <span 
                    key={trait}
                    className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                  >
                    {trait}
                  </span>
                ))}
              </div>

              <div className="p-3 bg-gray-50 rounded-lg">
                <p className="text-xs text-gray-500 mb-1">Sample greeting:</p>
                <p className="text-sm italic">"{personality.greeting}"</p>
              </div>

              <Button 
                className="w-full" 
                variant={selectedPersonality === personality.id ? "default" : "outline"}
                onClick={(e) => {
                  e.stopPropagation();
                  handlePersonalitySelect(personality);
                }}
              >
                {selectedPersonality === personality.id ? 'Currently Active' : 'Select This Personality'}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="mt-6 p-4 bg-blue-50 rounded-lg">
        <h4 className="font-semibold text-blue-800 mb-2">ℹ️ How AI Personality Works:</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Each personality has unique communication style and approach</li>
          <li>• Personality affects how AI responds to your questions</li>
          <li>• You can change personality anytime during conversation</li>
          <li>• Personality is saved and remembered for future sessions</li>
          <li>• Try different personalities for different types of tasks!</li>
        </ul>
      </div>
    </div>
  );
};

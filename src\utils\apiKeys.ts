// utils/apiKeys.ts

export const API_KEYS = {
  GROQ: "VITE_GROQ_API_KEY",
  HUGGINGFACE: "VITE_HUGGINGFACE_API_KEY",
} as const;

export const getApiKey = (keyName: string): string | undefined => {
  // First try to get from localStorage (user-saved keys)
  const localStorageKey = localStorage.getItem(keyName);
  if (localStorageKey) {
    return localStorageKey;
  }

  // Fallback to environment variables
  switch (keyName) {
    case API_KEYS.GROQ:
      return import.meta.env.VITE_GROQ_API_KEY;
    case API_KEYS.HUGGINGFACE:
      return import.meta.env.VITE_HUGGINGFACE_API_KEY;
    default:
      return undefined;
  }
};

export const saveApiKey = (key: string, value: string) => {
  localStorage.setItem(key, value);
};

export const loadApiKeyFromLocalStorage = (key: string): string | null => {
  return localStorage.getItem(key);
};
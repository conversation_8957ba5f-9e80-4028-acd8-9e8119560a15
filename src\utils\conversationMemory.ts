// Advanced Conversation Memory System
export interface ConversationContext {
  userId: string;
  sessionId: string;
  startTime: number;
  lastActivity: number;
  messageCount: number;
  topics: string[];
  userPreferences: UserPreferences;
  conversationSummary: string;
  emotionalState: EmotionalState;
  personalInfo: PersonalInfo;
}

export interface UserPreferences {
  preferredLanguage: string;
  communicationStyle: 'formal' | 'casual' | 'friendly' | 'professional';
  interests: string[];
  expertise: string[];
  learningGoals: string[];
  responseLength: 'short' | 'medium' | 'detailed';
  useEmojis: boolean;
  preferredTopics: string[];
}

export interface EmotionalState {
  currentMood: 'happy' | 'neutral' | 'frustrated' | 'excited' | 'confused' | 'satisfied';
  engagement: number; // 0-1 scale
  satisfaction: number; // 0-1 scale
  lastEmotionUpdate: number;
}

export interface PersonalInfo {
  name?: string;
  profession?: string;
  location?: string;
  timezone?: string;
  age?: string;
  hobbies?: string[];
  goals?: string[];
  challenges?: string[];
}

export interface MemoryEntry {
  id: string;
  timestamp: number;
  type: 'fact' | 'preference' | 'goal' | 'problem' | 'achievement' | 'relationship';
  content: string;
  importance: number; // 0-1 scale
  lastAccessed: number;
  accessCount: number;
  tags: string[];
  relatedEntries: string[];
}

export class ConversationMemoryManager {
  private static instance: ConversationMemoryManager;
  private context: ConversationContext;
  private memories: Map<string, MemoryEntry> = new Map();
  private readonly STORAGE_KEY = 'kikaze_conversation_memory';
  private readonly CONTEXT_KEY = 'kikaze_conversation_context';

  private constructor() {
    this.loadFromStorage();
    this.initializeContext();
  }

  public static getInstance(): ConversationMemoryManager {
    if (!ConversationMemoryManager.instance) {
      ConversationMemoryManager.instance = new ConversationMemoryManager();
    }
    return ConversationMemoryManager.instance;
  }

  private initializeContext(): void {
    const savedContext = localStorage.getItem(this.CONTEXT_KEY);
    if (savedContext) {
      this.context = JSON.parse(savedContext);
      this.context.lastActivity = Date.now();
    } else {
      this.context = {
        userId: this.generateUserId(),
        sessionId: this.generateSessionId(),
        startTime: Date.now(),
        lastActivity: Date.now(),
        messageCount: 0,
        topics: [],
        userPreferences: {
          preferredLanguage: 'id',
          communicationStyle: 'friendly',
          interests: [],
          expertise: [],
          learningGoals: [],
          responseLength: 'medium',
          useEmojis: true,
          preferredTopics: []
        },
        conversationSummary: '',
        emotionalState: {
          currentMood: 'neutral',
          engagement: 0.5,
          satisfaction: 0.5,
          lastEmotionUpdate: Date.now()
        },
        personalInfo: {}
      };
    }
  }

  private generateUserId(): string {
    return 'user_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
  }

  private generateSessionId(): string {
    return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
  }

  public addMemory(type: MemoryEntry['type'], content: string, importance: number = 0.5, tags: string[] = []): string {
    const id = 'memory_' + Date.now() + '_' + Math.random().toString(36).substr(2, 5);
    const memory: MemoryEntry = {
      id,
      timestamp: Date.now(),
      type,
      content,
      importance,
      lastAccessed: Date.now(),
      accessCount: 0,
      tags,
      relatedEntries: []
    };

    this.memories.set(id, memory);
    this.saveToStorage();
    return id;
  }

  public getRelevantMemories(query: string, limit: number = 5): MemoryEntry[] {
    const queryLower = query.toLowerCase();
    const relevantMemories: Array<{ memory: MemoryEntry; score: number }> = [];

    this.memories.forEach(memory => {
      let score = 0;
      
      // Content relevance
      if (memory.content.toLowerCase().includes(queryLower)) {
        score += 0.4;
      }

      // Tag relevance
      memory.tags.forEach(tag => {
        if (queryLower.includes(tag.toLowerCase())) {
          score += 0.2;
        }
      });

      // Importance weight
      score += memory.importance * 0.3;

      // Recency weight (more recent = higher score)
      const daysSinceCreated = (Date.now() - memory.timestamp) / (1000 * 60 * 60 * 24);
      score += Math.max(0, (7 - daysSinceCreated) / 7) * 0.1;

      if (score > 0.1) {
        relevantMemories.push({ memory, score });
      }
    });

    return relevantMemories
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(item => {
        item.memory.accessCount++;
        item.memory.lastAccessed = Date.now();
        return item.memory;
      });
  }

  public updateUserPreference(key: keyof UserPreferences, value: any): void {
    (this.context.userPreferences[key] as any) = value;
    this.addMemory('preference', `User prefers ${key}: ${value}`, 0.7, ['preference', key]);
    this.saveToStorage();
  }

  public updatePersonalInfo(key: keyof PersonalInfo, value: any): void {
    this.context.personalInfo[key] = value;
    this.addMemory('fact', `User's ${key}: ${value}`, 0.8, ['personal', key]);
    this.saveToStorage();
  }

  public updateEmotionalState(mood: EmotionalState['currentMood'], engagement?: number, satisfaction?: number): void {
    this.context.emotionalState.currentMood = mood;
    if (engagement !== undefined) this.context.emotionalState.engagement = engagement;
    if (satisfaction !== undefined) this.context.emotionalState.satisfaction = satisfaction;
    this.context.emotionalState.lastEmotionUpdate = Date.now();
    this.saveToStorage();
  }

  public addTopic(topic: string): void {
    if (!this.context.topics.includes(topic)) {
      this.context.topics.push(topic);
      if (this.context.topics.length > 10) {
        this.context.topics = this.context.topics.slice(-10); // Keep last 10 topics
      }
    }
    this.saveToStorage();
  }

  public incrementMessageCount(): void {
    this.context.messageCount++;
    this.context.lastActivity = Date.now();
    this.saveToStorage();
  }

  public getContext(): ConversationContext {
    return { ...this.context };
  }

  public getPersonalizedGreeting(): string {
    const { personalInfo, emotionalState, userPreferences } = this.context;
    const timeOfDay = this.getTimeOfDay();
    const name = personalInfo.name || 'teman';
    
    let greeting = `${timeOfDay}, ${name}! `;
    
    switch (emotionalState.currentMood) {
      case 'happy':
        greeting += userPreferences.useEmojis ? '😊 ' : '';
        greeting += 'Senang bertemu lagi dengan Anda!';
        break;
      case 'excited':
        greeting += userPreferences.useEmojis ? '🚀 ' : '';
        greeting += 'Wah, ada yang menarik hari ini?';
        break;
      case 'frustrated':
        greeting += userPreferences.useEmojis ? '🤗 ' : '';
        greeting += 'Saya di sini untuk membantu mengatasi masalah Anda.';
        break;
      default:
        greeting += userPreferences.useEmojis ? '👋 ' : '';
        greeting += 'Ada yang bisa saya bantu hari ini?';
    }

    return greeting;
  }

  private getTimeOfDay(): string {
    const hour = new Date().getHours();
    if (hour < 12) return 'Selamat pagi';
    if (hour < 17) return 'Selamat siang';
    if (hour < 21) return 'Selamat sore';
    return 'Selamat malam';
  }

  public generateContextualPrompt(userMessage: string): string {
    const relevantMemories = this.getRelevantMemories(userMessage);
    const { personalInfo, userPreferences, emotionalState, topics } = this.context;

    let contextPrompt = `
CONVERSATION CONTEXT:
- User's name: ${personalInfo.name || 'Unknown'}
- Communication style: ${userPreferences.communicationStyle}
- Current mood: ${emotionalState.currentMood}
- Engagement level: ${Math.round(emotionalState.engagement * 100)}%
- Recent topics: ${topics.slice(-3).join(', ')}
- Message count in session: ${this.context.messageCount}

RELEVANT MEMORIES:
${relevantMemories.map(m => `- ${m.content} (${m.type})`).join('\n')}

USER PREFERENCES:
- Response length: ${userPreferences.responseLength}
- Use emojis: ${userPreferences.useEmojis}
- Interests: ${userPreferences.interests.join(', ')}

Please respond naturally, considering this context and maintaining conversation continuity.
`;

    return contextPrompt;
  }

  public analyzeUserMessage(message: string): void {
    // Extract potential personal information
    this.extractPersonalInfo(message);
    
    // Detect emotional state
    this.detectEmotionalState(message);
    
    // Extract topics
    this.extractTopics(message);
    
    // Update engagement based on message characteristics
    this.updateEngagement(message);
  }

  private extractPersonalInfo(message: string): void {
    const lowerMessage = message.toLowerCase();
    
    // Name extraction
    const namePatterns = [
      /nama saya (\w+)/i,
      /saya (\w+)/i,
      /panggil saya (\w+)/i,
      /i'm (\w+)/i,
      /my name is (\w+)/i
    ];
    
    namePatterns.forEach(pattern => {
      const match = message.match(pattern);
      if (match && match[1]) {
        this.updatePersonalInfo('name', match[1]);
      }
    });

    // Profession extraction
    if (lowerMessage.includes('kerja') || lowerMessage.includes('profesi') || lowerMessage.includes('job')) {
      const professionPatterns = [
        /saya bekerja sebagai (\w+)/i,
        /profesi saya (\w+)/i,
        /saya seorang (\w+)/i,
        /i work as (\w+)/i,
        /i'm a (\w+)/i
      ];
      
      professionPatterns.forEach(pattern => {
        const match = message.match(pattern);
        if (match && match[1]) {
          this.updatePersonalInfo('profession', match[1]);
        }
      });
    }
  }

  private detectEmotionalState(message: string): void {
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('senang') || lowerMessage.includes('bahagia') || lowerMessage.includes('happy')) {
      this.updateEmotionalState('happy', Math.min(1, this.context.emotionalState.engagement + 0.1));
    } else if (lowerMessage.includes('bingung') || lowerMessage.includes('confused') || lowerMessage.includes('tidak mengerti')) {
      this.updateEmotionalState('confused', Math.max(0, this.context.emotionalState.engagement - 0.1));
    } else if (lowerMessage.includes('frustasi') || lowerMessage.includes('kesal') || lowerMessage.includes('frustrated')) {
      this.updateEmotionalState('frustrated', Math.max(0, this.context.emotionalState.engagement - 0.2));
    } else if (lowerMessage.includes('excited') || lowerMessage.includes('antusias') || lowerMessage.includes('semangat')) {
      this.updateEmotionalState('excited', Math.min(1, this.context.emotionalState.engagement + 0.2));
    }
  }

  private extractTopics(message: string): void {
    const topicKeywords = [
      'programming', 'coding', 'javascript', 'python', 'react', 'ai', 'machine learning',
      'design', 'ui/ux', 'business', 'marketing', 'finance', 'health', 'fitness',
      'travel', 'food', 'music', 'art', 'science', 'technology', 'education'
    ];

    topicKeywords.forEach(keyword => {
      if (message.toLowerCase().includes(keyword)) {
        this.addTopic(keyword);
      }
    });
  }

  private updateEngagement(message: string): void {
    let engagementDelta = 0;
    
    // Longer messages indicate higher engagement
    if (message.length > 100) engagementDelta += 0.05;
    if (message.length > 200) engagementDelta += 0.05;
    
    // Questions indicate engagement
    if (message.includes('?')) engagementDelta += 0.1;
    
    // Exclamation points indicate excitement
    if (message.includes('!')) engagementDelta += 0.05;
    
    // Update engagement
    const newEngagement = Math.max(0, Math.min(1, this.context.emotionalState.engagement + engagementDelta));
    this.updateEmotionalState(this.context.emotionalState.currentMood, newEngagement);
  }

  private loadFromStorage(): void {
    try {
      const savedMemories = localStorage.getItem(this.STORAGE_KEY);
      if (savedMemories) {
        const memoriesArray: MemoryEntry[] = JSON.parse(savedMemories);
        memoriesArray.forEach(memory => {
          this.memories.set(memory.id, memory);
        });
      }
    } catch (error) {
      console.error('Error loading memories from storage:', error);
    }
  }

  private saveToStorage(): void {
    try {
      const memoriesArray = Array.from(this.memories.values());
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(memoriesArray));
      localStorage.setItem(this.CONTEXT_KEY, JSON.stringify(this.context));
    } catch (error) {
      console.error('Error saving memories to storage:', error);
    }
  }

  public clearMemory(): void {
    this.memories.clear();
    localStorage.removeItem(this.STORAGE_KEY);
    localStorage.removeItem(this.CONTEXT_KEY);
    this.initializeContext();
  }

  public exportMemory(): string {
    return JSON.stringify({
      context: this.context,
      memories: Array.from(this.memories.values())
    }, null, 2);
  }

  public importMemory(data: string): boolean {
    try {
      const parsed = JSON.parse(data);
      this.context = parsed.context;
      this.memories.clear();
      parsed.memories.forEach((memory: MemoryEntry) => {
        this.memories.set(memory.id, memory);
      });
      this.saveToStorage();
      return true;
    } catch (error) {
      console.error('Error importing memory:', error);
      return false;
    }
  }
}


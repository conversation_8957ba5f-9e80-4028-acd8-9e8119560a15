import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { FileSearch, Download, Copy, RefreshCw, Sparkles, Presentation, BarChart3, Image, Play } from 'lucide-react';

interface PowerPointVBATemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  complexity: 'Basic' | 'Intermediate' | 'Advanced';
  code: string;
  fields: ConfigField[];
  preview: string;
}

interface ConfigField {
  id: string;
  name: string;
  type: 'text' | 'number' | 'select' | 'textarea' | 'boolean';
  placeholder?: string;
  options?: string[];
  required: boolean;
  defaultValue?: any;
}

const powerPointVBATemplates: PowerPointVBATemplate[] = [
  {
    id: 'slide-generator',
    name: 'Bulk Slide Generator',
    description: 'Generate multiple slides dari data Excel dengan template',
    category: 'Slide Creation',
    complexity: 'Intermediate',
    code: `Sub GenerateSlidesFromData()
    Dim ppt As Presentation
    Dim slide As Slide
    Dim xl As Object
    Dim wb As Object
    Dim ws As Object
    Dim lastRow As Long
    Dim i As Long
    
    ' Configuration
    Dim dataSource As String
    Dim templatePath As String
    
    dataSource = "{{dataSource}}"
    templatePath = "{{templatePath}}"
    
    ' Open Excel data
    Set xl = CreateObject("Excel.Application")
    xl.Visible = False
    Set wb = xl.Workbooks.Open(dataSource)
    Set ws = wb.Worksheets("{{worksheetName}}")
    
    ' Get data range
    lastRow = ws.Cells(ws.Rows.Count, 1).End(-4162).Row
    
    ' Open or create presentation
    If templatePath <> "" Then
        Set ppt = Presentations.Open(templatePath)
    Else
        Set ppt = Presentations.Add
    End If
    
    ' Generate slides
    For i = {{startRow}} To lastRow
        ' Add new slide
        Set slide = ppt.Slides.Add(ppt.Slides.Count + 1, {{slideLayout}})
        
        ' Set slide title
        If slide.Shapes.HasTitle Then
            slide.Shapes.Title.TextFrame.TextRange.Text = ws.Cells(i, {{titleColumn}}).Value
        End If
        
        ' Set slide content
        If slide.Shapes.Count > 1 Then
            slide.Shapes(2).TextFrame.TextRange.Text = ws.Cells(i, {{contentColumn}}).Value
        End If
        
        ' Add image if specified
        If "{{imageColumn}}" <> "" And ws.Cells(i, {{imageColumn}}).Value <> "" Then
            Dim imagePath As String
            imagePath = ws.Cells(i, {{imageColumn}}).Value
            If Dir(imagePath) <> "" Then
                slide.Shapes.AddPicture imagePath, False, True, {{imageLeft}}, {{imageTop}}, {{imageWidth}}, {{imageHeight}}
            End If
        End If
    Next i
    
    ' Save presentation
    If "{{outputPath}}" <> "" Then
        ppt.SaveAs "{{outputPath}}"
    End If
    
    ' Cleanup
    wb.Close SaveChanges:=False
    xl.Quit
    Set xl = Nothing
    
    MsgBox "Generated " & (lastRow - {{startRow}} + 1) & " slides successfully!", vbInformation
End Sub`,
    fields: [
      { id: 'dataSource', name: 'Excel Data Source', type: 'text', placeholder: 'C:\\Data\\slides.xlsx', required: true },
      { id: 'worksheetName', name: 'Worksheet Name', type: 'text', placeholder: 'Sheet1', required: true, defaultValue: 'Sheet1' },
      { id: 'templatePath', name: 'Template Path (Optional)', type: 'text', placeholder: 'C:\\Templates\\template.pptx', required: false },
      { id: 'startRow', name: 'Start Row', type: 'number', required: true, defaultValue: 2 },
      { id: 'titleColumn', name: 'Title Column', type: 'number', required: true, defaultValue: 1 },
      { id: 'contentColumn', name: 'Content Column', type: 'number', required: true, defaultValue: 2 },
      { id: 'imageColumn', name: 'Image Column (Optional)', type: 'number', required: false },
      { id: 'slideLayout', name: 'Slide Layout', type: 'select', options: ['ppLayoutTitle', 'ppLayoutText', 'ppLayoutTitleOnly', 'ppLayoutBlank'], required: true, defaultValue: 'ppLayoutText' },
      { id: 'imageLeft', name: 'Image Left Position', type: 'number', required: false, defaultValue: 400 },
      { id: 'imageTop', name: 'Image Top Position', type: 'number', required: false, defaultValue: 200 },
      { id: 'imageWidth', name: 'Image Width', type: 'number', required: false, defaultValue: 300 },
      { id: 'imageHeight', name: 'Image Height', type: 'number', required: false, defaultValue: 200 },
      { id: 'outputPath', name: 'Output Path (Optional)', type: 'text', placeholder: 'C:\\Output\\presentation.pptx', required: false }
    ],
    preview: 'Generates multiple slides from Excel data with customizable layouts and images'
  },
  {
    id: 'chart-integration',
    name: 'Excel Chart Integration',
    description: 'Import charts dari Excel ke PowerPoint slides',
    category: 'Charts',
    complexity: 'Advanced',
    code: `Sub ImportExcelCharts()
    Dim ppt As Presentation
    Dim slide As Slide
    Dim xl As Object
    Dim wb As Object
    Dim ws As Object
    Dim chart As Object
    
    ' Configuration
    Dim excelFile As String
    Dim chartNames As String
    Dim chartArray() As String
    Dim i As Integer
    
    excelFile = "{{excelFile}}"
    chartNames = "{{chartNames}}"
    
    ' Split chart names
    chartArray = Split(chartNames, ";")
    
    ' Open Excel file
    Set xl = CreateObject("Excel.Application")
    xl.Visible = False
    Set wb = xl.Workbooks.Open(excelFile)
    Set ws = wb.Worksheets("{{worksheetName}}")
    
    ' Create or open presentation
    Set ppt = ActivePresentation
    
    ' Process each chart
    For i = 0 To UBound(chartArray)
        If Trim(chartArray(i)) <> "" Then
            ' Add new slide
            Set slide = ppt.Slides.Add(ppt.Slides.Count + 1, {{slideLayout}})
            
            ' Set slide title
            If slide.Shapes.HasTitle Then
                slide.Shapes.Title.TextFrame.TextRange.Text = "{{slideTitle}} " & (i + 1)
            End If
            
            ' Copy chart from Excel
            Set chart = ws.ChartObjects(Trim(chartArray(i)))
            chart.Copy
            
            ' Paste chart to slide
            slide.Shapes.Paste
            
            ' Position and resize chart
            With slide.Shapes(slide.Shapes.Count)
                .Left = {{chartLeft}}
                .Top = {{chartTop}}
                .Width = {{chartWidth}}
                .Height = {{chartHeight}}
            End With
            
            ' Add chart description if specified
            If "{{chartDescription}}" <> "" Then
                Dim textBox As Shape
                Set textBox = slide.Shapes.AddTextbox(msoTextOrientationHorizontal, {{descLeft}}, {{descTop}}, {{descWidth}}, {{descHeight}})
                textBox.TextFrame.TextRange.Text = "{{chartDescription}}"
                textBox.TextFrame.TextRange.Font.Size = {{descFontSize}}
            End If
        End If
    Next i
    
    ' Cleanup
    wb.Close SaveChanges:=False
    xl.Quit
    Set xl = Nothing
    
    MsgBox "Imported " & (UBound(chartArray) + 1) & " charts successfully!", vbInformation
End Sub`,
    fields: [
      { id: 'excelFile', name: 'Excel File Path', type: 'text', placeholder: 'C:\\Data\\charts.xlsx', required: true },
      { id: 'worksheetName', name: 'Worksheet Name', type: 'text', placeholder: 'Charts', required: true, defaultValue: 'Charts' },
      { id: 'chartNames', name: 'Chart Names (semicolon separated)', type: 'textarea', placeholder: 'Chart1;Chart2;Chart3', required: true },
      { id: 'slideTitle', name: 'Slide Title Prefix', type: 'text', placeholder: 'Chart Analysis', required: true, defaultValue: 'Chart' },
      { id: 'slideLayout', name: 'Slide Layout', type: 'select', options: ['ppLayoutTitle', 'ppLayoutText', 'ppLayoutTitleOnly', 'ppLayoutBlank'], required: true, defaultValue: 'ppLayoutTitleOnly' },
      { id: 'chartLeft', name: 'Chart Left Position', type: 'number', required: true, defaultValue: 50 },
      { id: 'chartTop', name: 'Chart Top Position', type: 'number', required: true, defaultValue: 100 },
      { id: 'chartWidth', name: 'Chart Width', type: 'number', required: true, defaultValue: 600 },
      { id: 'chartHeight', name: 'Chart Height', type: 'number', required: true, defaultValue: 400 },
      { id: 'chartDescription', name: 'Chart Description (Optional)', type: 'text', placeholder: 'Analysis summary', required: false },
      { id: 'descLeft', name: 'Description Left Position', type: 'number', required: false, defaultValue: 50 },
      { id: 'descTop', name: 'Description Top Position', type: 'number', required: false, defaultValue: 520 },
      { id: 'descWidth', name: 'Description Width', type: 'number', required: false, defaultValue: 600 },
      { id: 'descHeight', name: 'Description Height', type: 'number', required: false, defaultValue: 50 },
      { id: 'descFontSize', name: 'Description Font Size', type: 'number', required: false, defaultValue: 12 }
    ],
    preview: 'Imports Excel charts into PowerPoint slides with customizable positioning and descriptions'
  },
  {
    id: 'animation-automation',
    name: 'Animation Automation',
    description: 'Apply consistent animations across slides',
    category: 'Animation',
    complexity: 'Basic',
    code: `Sub ApplyAnimations()
    Dim ppt As Presentation
    Dim slide As Slide
    Dim shape As Shape
    Dim animEffect As Effect
    
    Set ppt = ActivePresentation
    
    ' Process each slide
    For Each slide In ppt.Slides
        ' Clear existing animations if specified
        If {{clearExisting}} Then
            slide.TimeLine.MainSequence.Effects.Clear
        End If
        
        ' Apply entrance animation to title
        If slide.Shapes.HasTitle Then
            Set animEffect = slide.TimeLine.MainSequence.AddEffect( _
                Shape:=slide.Shapes.Title, _
                effectId:={{titleAnimation}}, _
                Level:=msoAnimationLevelNone, _
                trigger:=msoAnimTriggerAfterPrevious)
            animEffect.Timing.Duration = {{titleDuration}}
        End If
        
        ' Apply animation to content shapes
        For Each shape In slide.Shapes
            If shape.Type = msoTextBox Or shape.Type = msoAutoShape Then
                If Not shape.HasTextFrame Then GoTo NextShape
                If shape.TextFrame.TextRange.Text = "" Then GoTo NextShape
                If shape.Name = slide.Shapes.Title.Name Then GoTo NextShape
                
                Set animEffect = slide.TimeLine.MainSequence.AddEffect( _
                    Shape:=shape, _
                    effectId:={{contentAnimation}}, _
                    Level:=msoAnimationLevelNone, _
                    trigger:=msoAnimTriggerAfterPrevious)
                animEffect.Timing.Duration = {{contentDuration}}
                animEffect.Timing.TriggerDelayTime = {{delayBetween}}
            End If
NextShape:
        Next shape
        
        ' Apply animation to images
        For Each shape In slide.Shapes
            If shape.Type = msoPicture Then
                Set animEffect = slide.TimeLine.MainSequence.AddEffect( _
                    Shape:=shape, _
                    effectId:={{imageAnimation}}, _
                    Level:=msoAnimationLevelNone, _
                    trigger:=msoAnimTriggerAfterPrevious)
                animEffect.Timing.Duration = {{imageDuration}}
            End If
        Next shape
    Next slide
    
    MsgBox "Animations applied to " & ppt.Slides.Count & " slides!", vbInformation
End Sub`,
    fields: [
      { id: 'clearExisting', name: 'Clear Existing Animations', type: 'boolean', required: true, defaultValue: true },
      { id: 'titleAnimation', name: 'Title Animation', type: 'select', options: ['msoAnimEffectFade', 'msoAnimEffectFlyInFromLeft', 'msoAnimEffectFlyInFromTop', 'msoAnimEffectWipe'], required: true, defaultValue: 'msoAnimEffectFade' },
      { id: 'contentAnimation', name: 'Content Animation', type: 'select', options: ['msoAnimEffectFade', 'msoAnimEffectFlyInFromLeft', 'msoAnimEffectFlyInFromBottom', 'msoAnimEffectAppear'], required: true, defaultValue: 'msoAnimEffectFlyInFromLeft' },
      { id: 'imageAnimation', name: 'Image Animation', type: 'select', options: ['msoAnimEffectFade', 'msoAnimEffectZoom', 'msoAnimEffectAppear', 'msoAnimEffectWipe'], required: true, defaultValue: 'msoAnimEffectFade' },
      { id: 'titleDuration', name: 'Title Duration (seconds)', type: 'number', required: true, defaultValue: 1 },
      { id: 'contentDuration', name: 'Content Duration (seconds)', type: 'number', required: true, defaultValue: 0.5 },
      { id: 'imageDuration', name: 'Image Duration (seconds)', type: 'number', required: true, defaultValue: 0.75 },
      { id: 'delayBetween', name: 'Delay Between Elements (seconds)', type: 'number', required: true, defaultValue: 0.25 }
    ],
    preview: 'Applies consistent entrance animations to titles, content, and images across all slides'
  },
  {
    id: 'template-applier',
    name: 'Template & Theme Applier',
    description: 'Apply consistent templates dan themes ke presentations',
    category: 'Formatting',
    complexity: 'Basic',
    code: `Sub ApplyTemplateAndTheme()
    Dim ppt As Presentation
    Dim slide As Slide
    Dim shape As Shape
    
    Set ppt = ActivePresentation
    
    ' Apply design template if specified
    If "{{templatePath}}" <> "" Then
        ppt.ApplyTemplate "{{templatePath}}"
    End If
    
    ' Set slide size
    With ppt.PageSetup
        .SlideSize = {{slideSize}}
        If {{slideSize}} = ppSlideSizeCustom Then
            .SlideWidth = {{customWidth}}
            .SlideHeight = {{customHeight}}
        End If
    End With
    
    ' Apply font formatting to all slides
    For Each slide In ppt.Slides
        For Each shape In slide.Shapes
            If shape.HasTextFrame Then
                With shape.TextFrame.TextRange.Font
                    .Name = "{{fontName}}"
                    .Size = {{fontSize}}
                    .Color.RGB = RGB({{fontColorR}}, {{fontColorG}}, {{fontColorB}})
                End With
            End If
        Next shape
        
        ' Set slide background if specified
        If {{useCustomBackground}} Then
            slide.Background.Fill.ForeColor.RGB = RGB({{bgColorR}}, {{bgColorG}}, {{bgColorB}})
        End If
    Next slide
    
    ' Apply master slide formatting
    With ppt.SlideMaster.Shapes.Title.TextFrame.TextRange.Font
        .Name = "{{titleFont}}"
        .Size = {{titleFontSize}}
        .Bold = {{titleBold}}
        .Color.RGB = RGB({{titleColorR}}, {{titleColorG}}, {{titleColorB}})
    End With
    
    MsgBox "Template and theme applied successfully!", vbInformation
End Sub`,
    fields: [
      { id: 'templatePath', name: 'Template Path (Optional)', type: 'text', placeholder: 'C:\\Templates\\corporate.potx', required: false },
      { id: 'slideSize', name: 'Slide Size', type: 'select', options: ['ppSlideSizeOnScreen', 'ppSlideSizeLetterPaper', 'ppSlideSizeA4Paper', 'ppSlideSizeCustom'], required: true, defaultValue: 'ppSlideSizeOnScreen' },
      { id: 'customWidth', name: 'Custom Width (if custom size)', type: 'number', required: false, defaultValue: 720 },
      { id: 'customHeight', name: 'Custom Height (if custom size)', type: 'number', required: false, defaultValue: 540 },
      { id: 'fontName', name: 'Default Font', type: 'select', options: ['Calibri', 'Arial', 'Times New Roman', 'Helvetica'], required: true, defaultValue: 'Calibri' },
      { id: 'fontSize', name: 'Default Font Size', type: 'number', required: true, defaultValue: 18 },
      { id: 'fontColorR', name: 'Font Color Red (0-255)', type: 'number', required: true, defaultValue: 0 },
      { id: 'fontColorG', name: 'Font Color Green (0-255)', type: 'number', required: true, defaultValue: 0 },
      { id: 'fontColorB', name: 'Font Color Blue (0-255)', type: 'number', required: true, defaultValue: 0 },
      { id: 'useCustomBackground', name: 'Use Custom Background', type: 'boolean', required: true, defaultValue: false },
      { id: 'bgColorR', name: 'Background Red (0-255)', type: 'number', required: false, defaultValue: 255 },
      { id: 'bgColorG', name: 'Background Green (0-255)', type: 'number', required: false, defaultValue: 255 },
      { id: 'bgColorB', name: 'Background Blue (0-255)', type: 'number', required: false, defaultValue: 255 },
      { id: 'titleFont', name: 'Title Font', type: 'select', options: ['Calibri', 'Arial', 'Times New Roman', 'Helvetica'], required: true, defaultValue: 'Calibri' },
      { id: 'titleFontSize', name: 'Title Font Size', type: 'number', required: true, defaultValue: 32 },
      { id: 'titleBold', name: 'Title Bold', type: 'boolean', required: true, defaultValue: true },
      { id: 'titleColorR', name: 'Title Color Red (0-255)', type: 'number', required: true, defaultValue: 0 },
      { id: 'titleColorG', name: 'Title Color Green (0-255)', type: 'number', required: true, defaultValue: 0 },
      { id: 'titleColorB', name: 'Title Color Blue (0-255)', type: 'number', required: true, defaultValue: 0 }
    ],
    preview: 'Applies consistent templates, themes, fonts, and colors across entire presentation'
  }
];

export const PowerPointVBAGenerator: React.FC = () => {
  const [selectedTemplate, setSelectedTemplate] = useState<PowerPointVBATemplate>(powerPointVBATemplates[0]);
  const [fieldValues, setFieldValues] = useState<Record<string, any>>({});
  const [generatedCode, setGeneratedCode] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);

  React.useEffect(() => {
    // Initialize field values with defaults
    const initialValues: Record<string, any> = {};
    selectedTemplate.fields.forEach(field => {
      if (field.defaultValue !== undefined) {
        initialValues[field.id] = field.defaultValue;
      }
    });
    setFieldValues(initialValues);
  }, [selectedTemplate]);

  React.useEffect(() => {
    generateCode();
  }, [selectedTemplate, fieldValues]);

  const generateCode = () => {
    let code = selectedTemplate.code;

    // Replace placeholders with actual values
    selectedTemplate.fields.forEach(field => {
      const value = fieldValues[field.id] || field.defaultValue || '';
      const placeholder = `{{${field.id}}}`;

      // Handle different field types
      let replacementValue = value;
      if (field.type === 'boolean') {
        replacementValue = value ? 'True' : 'False';
      } else if (field.type === 'number') {
        replacementValue = value.toString();
      } else if (field.type === 'text' || field.type === 'textarea') {
        replacementValue = `"${value}"`;
      } else if (field.type === 'select') {
        replacementValue = value;
      }

      code = code.replace(new RegExp(placeholder, 'g'), replacementValue);
    });

    setGeneratedCode(code);
  };

  const handleFieldChange = (fieldId: string, value: any) => {
    setFieldValues(prev => ({
      ...prev,
      [fieldId]: value
    }));
  };

  const handleTemplateChange = (templateId: string) => {
    const template = powerPointVBATemplates.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(template);
    }
  };

  const generateAICode = () => {
    setIsGenerating(true);

    setTimeout(() => {
      // Simulate AI enhancement
      const enhancements = [
        '    \' AI Enhancement: Added error handling',
        '    On Error GoTo ErrorHandler',
        '    \' AI Enhancement: Added progress indication',
        '    Application.StatusBar = "Processing slides..."',
        '    \' AI Enhancement: Added cleanup',
        '    Application.StatusBar = False',
        '    Exit Sub',
        '',
        'ErrorHandler:',
        '    MsgBox "Error: " & Err.Description, vbCritical',
        '    Application.StatusBar = False'
      ];

      const enhancedCode = generatedCode.replace(
        'End Sub',
        enhancements.join('\n') + '\nEnd Sub'
      );

      setGeneratedCode(enhancedCode);
      setIsGenerating(false);
      toast.success('AI enhancements applied to PowerPoint VBA code!');
    }, 2000);
  };

  const copyCode = () => {
    navigator.clipboard.writeText(generatedCode);
    toast.success('PowerPoint VBA code copied to clipboard!');
  };

  const downloadCode = () => {
    const blob = new Blob([generatedCode], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${selectedTemplate.name.replace(/\s+/g, '_')}_PowerPoint.bas`;
    link.click();
    URL.revokeObjectURL(url);
    toast.success('PowerPoint VBA code downloaded as .bas file!');
  };

  const renderField = (field: ConfigField) => {
    const value = fieldValues[field.id] || field.defaultValue || '';

    switch (field.type) {
      case 'text':
        return (
          <Input
            value={value}
            onChange={(e) => handleFieldChange(field.id, e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
          />
        );

      case 'number':
        return (
          <Input
            type="number"
            value={value}
            onChange={(e) => handleFieldChange(field.id, Number(e.target.value))}
            placeholder={field.placeholder}
            required={field.required}
          />
        );

      case 'textarea':
        return (
          <Textarea
            value={value}
            onChange={(e) => handleFieldChange(field.id, e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
            rows={3}
          />
        );

      case 'select':
        return (
          <Select value={value} onValueChange={(val) => handleFieldChange(field.id, val)}>
            <SelectTrigger>
              <SelectValue placeholder="Select option" />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case 'boolean':
        return (
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={value}
              onChange={(e) => handleFieldChange(field.id, e.target.checked)}
              className="rounded"
            />
            <span className="text-sm">Enable</span>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileSearch className="w-5 h-5" />
            PowerPoint VBA Generator
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Controls */}
            <div className="space-y-6">
              <Tabs defaultValue="templates" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="templates">Templates</TabsTrigger>
                  <TabsTrigger value="config">Configure</TabsTrigger>
                  <TabsTrigger value="advanced">Advanced</TabsTrigger>
                </TabsList>

                <TabsContent value="templates" className="space-y-4">
                  <div>
                    <Label>Select PowerPoint VBA Template</Label>
                    <Select value={selectedTemplate.id} onValueChange={handleTemplateChange}>
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {powerPointVBATemplates.map((template) => (
                          <SelectItem key={template.id} value={template.id}>
                            {template.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Card className="p-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">{selectedTemplate.name}</h4>
                        <div className="flex gap-2">
                          <Badge variant="outline">{selectedTemplate.category}</Badge>
                          <Badge variant={
                            selectedTemplate.complexity === 'Basic' ? 'default' :
                            selectedTemplate.complexity === 'Intermediate' ? 'secondary' : 'destructive'
                          }>
                            {selectedTemplate.complexity}
                          </Badge>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600">{selectedTemplate.description}</p>
                      <p className="text-xs text-gray-500">{selectedTemplate.preview}</p>
                    </div>
                  </Card>

                  <div className="grid grid-cols-1 gap-3">
                    {powerPointVBATemplates.map((template) => (
                      <Card
                        key={template.id}
                        className={`cursor-pointer transition-all ${
                          selectedTemplate.id === template.id
                            ? 'ring-2 ring-blue-500 bg-blue-50'
                            : 'hover:bg-gray-50'
                        }`}
                        onClick={() => handleTemplateChange(template.id)}
                      >
                        <CardContent className="p-3">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h4 className="font-medium text-sm">{template.name}</h4>
                              <p className="text-xs text-gray-600 mt-1">{template.description}</p>
                            </div>
                            <div className="flex flex-col gap-1 ml-2">
                              <Badge variant="outline" className="text-xs">
                                {template.category}
                              </Badge>
                              <Badge
                                variant={
                                  template.complexity === 'Basic' ? 'default' :
                                  template.complexity === 'Intermediate' ? 'secondary' : 'destructive'
                                }
                                className="text-xs"
                              >
                                {template.complexity}
                              </Badge>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="config" className="space-y-4">
                  <div>
                    <Label className="text-base font-medium">Configuration Fields</Label>
                    <p className="text-sm text-gray-600 mt-1">
                      Configure the parameters for your PowerPoint VBA macro
                    </p>
                  </div>

                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {selectedTemplate.fields.map((field) => (
                      <div key={field.id} className="space-y-2">
                        <Label htmlFor={field.id} className="flex items-center gap-2">
                          {field.name}
                          {field.required && <span className="text-red-500">*</span>}
                        </Label>
                        {renderField(field)}
                        {field.placeholder && (
                          <p className="text-xs text-gray-500">
                            Example: {field.placeholder}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="advanced" className="space-y-4">
                  <div>
                    <Label className="text-base font-medium">Advanced Options</Label>
                    <p className="text-sm text-gray-600 mt-1">
                      AI enhancements and advanced PowerPoint automation options
                    </p>
                  </div>

                  <Button
                    onClick={generateAICode}
                    disabled={isGenerating}
                    className="w-full"
                  >
                    {isGenerating ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Applying AI Enhancements...
                      </>
                    ) : (
                      <>
                        <Sparkles className="w-4 h-4 mr-2" />
                        Apply AI Enhancements
                      </>
                    )}
                  </Button>

                  <div className="p-4 bg-purple-50 rounded-lg">
                    <h4 className="font-medium text-purple-900 mb-2">🤖 AI Enhancements</h4>
                    <ul className="text-sm text-purple-800 space-y-1">
                      <li>• Error handling untuk PowerPoint operations</li>
                      <li>• Progress indicators untuk slide processing</li>
                      <li>• Memory cleanup dan optimization</li>
                      <li>• Performance improvements untuk large presentations</li>
                      <li>• Code documentation dan comments</li>
                    </ul>
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            {/* Preview */}
            <div className="space-y-4">
              <div>
                <Label className="text-base font-medium">Generated PowerPoint VBA Code</Label>
                <Card className="mt-2">
                  <CardContent className="p-0">
                    <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm overflow-auto max-h-96">
                      <pre className="whitespace-pre-wrap">{generatedCode}</pre>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-2">
                <Label>Export Options</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyCode}
                    disabled={!generatedCode}
                  >
                    <Copy className="w-4 h-4 mr-1" />
                    Copy Code
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={downloadCode}
                    disabled={!generatedCode}
                  >
                    <Download className="w-4 h-4 mr-1" />
                    Download .bas
                  </Button>
                </div>
              </div>

              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">📝 How to Use</h4>
                <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                  <li>Copy the generated VBA code</li>
                  <li>Open Microsoft PowerPoint</li>
                  <li>Press Alt+F11 to open VBA Editor</li>
                  <li>Insert → Module</li>
                  <li>Paste the code into the module</li>
                  <li>Press F5 to run the macro</li>
                </ol>
              </div>

              <div className="p-4 bg-amber-50 rounded-lg">
                <h4 className="font-medium text-amber-900 mb-2">⚠️ Important Notes</h4>
                <ul className="text-sm text-amber-800 space-y-1">
                  <li>• Enable macros in PowerPoint security settings</li>
                  <li>• Test macros on sample presentations first</li>
                  <li>• Backup your presentations before running macros</li>
                  <li>• Some features require specific PowerPoint versions</li>
                  <li>• Chart integration requires Excel to be installed</li>
                </ul>
              </div>

              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">✨ Template Features</h4>
                <div className="text-sm text-green-800 space-y-2">
                  <div className="flex items-center gap-2">
                    <Presentation className="w-4 h-4" />
                    <span><strong>Slide Generation:</strong> Bulk slide creation dari data</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <BarChart3 className="w-4 h-4" />
                    <span><strong>Chart Integration:</strong> Excel charts ke PowerPoint</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Play className="w-4 h-4" />
                    <span><strong>Animation:</strong> Consistent animation automation</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Image className="w-4 h-4" />
                    <span><strong>Formatting:</strong> Template dan theme automation</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

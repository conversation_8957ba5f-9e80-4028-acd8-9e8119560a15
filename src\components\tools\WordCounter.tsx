import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { Copy, Upload, Download, BarChart3 } from 'lucide-react';

export const WordCounter: React.FC = () => {
  const [text, setText] = useState('');
  const [stats, setStats] = useState({
    characters: 0,
    charactersNoSpaces: 0,
    words: 0,
    sentences: 0,
    paragraphs: 0,
    readingTime: 0,
    speakingTime: 0
  });

  const calculateStats = (inputText: string) => {
    const characters = inputText.length;
    const charactersNoSpaces = inputText.replace(/\s/g, '').length;
    
    // Words (split by whitespace, filter empty)
    const words = inputText.trim() === '' ? 0 : inputText.trim().split(/\s+/).length;
    
    // Sentences (split by .!?, filter empty)
    const sentences = inputText.trim() === '' ? 0 : inputText.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
    
    // Paragraphs (split by double newlines, filter empty)
    const paragraphs = inputText.trim() === '' ? 0 : inputText.split(/\n\s*\n/).filter(p => p.trim().length > 0).length;
    
    // Reading time (average 200 words per minute)
    const readingTime = Math.ceil(words / 200);
    
    // Speaking time (average 150 words per minute)
    const speakingTime = Math.ceil(words / 150);

    return {
      characters,
      charactersNoSpaces,
      words,
      sentences,
      paragraphs,
      readingTime,
      speakingTime
    };
  };

  useEffect(() => {
    setStats(calculateStats(text));
  }, [text]);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      setText(content);
      toast.success(`File ${file.name} berhasil dimuat!`);
    };
    reader.readAsText(file);
  };

  const copyStats = async () => {
    const statsText = `
📊 Statistik Teks:
• Karakter: ${stats.characters.toLocaleString()}
• Karakter (tanpa spasi): ${stats.charactersNoSpaces.toLocaleString()}
• Kata: ${stats.words.toLocaleString()}
• Kalimat: ${stats.sentences.toLocaleString()}
• Paragraf: ${stats.paragraphs.toLocaleString()}
• Waktu baca: ${stats.readingTime} menit
• Waktu bicara: ${stats.speakingTime} menit
    `.trim();

    try {
      await navigator.clipboard.writeText(statsText);
      toast.success('Statistik berhasil disalin!');
    } catch (error) {
      toast.error('Gagal menyalin statistik');
    }
  };

  const downloadReport = () => {
    const report = `LAPORAN ANALISIS TEKS
${new Date().toLocaleDateString('id-ID')}

STATISTIK DASAR:
• Total Karakter: ${stats.characters.toLocaleString()}
• Karakter (tanpa spasi): ${stats.charactersNoSpaces.toLocaleString()}
• Total Kata: ${stats.words.toLocaleString()}
• Total Kalimat: ${stats.sentences.toLocaleString()}
• Total Paragraf: ${stats.paragraphs.toLocaleString()}

ESTIMASI WAKTU:
• Waktu Membaca: ${stats.readingTime} menit (200 kata/menit)
• Waktu Berbicara: ${stats.speakingTime} menit (150 kata/menit)

ANALISIS TAMBAHAN:
• Rata-rata kata per kalimat: ${stats.sentences > 0 ? (stats.words / stats.sentences).toFixed(1) : 0}
• Rata-rata karakter per kata: ${stats.words > 0 ? (stats.charactersNoSpaces / stats.words).toFixed(1) : 0}
• Kepadatan teks: ${stats.characters > 0 ? ((stats.charactersNoSpaces / stats.characters) * 100).toFixed(1) : 0}%

TEKS ASLI:
${text}
`;

    const blob = new Blob([report], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `word-count-report-${new Date().toISOString().split('T')[0]}.txt`;
    a.click();
    URL.revokeObjectURL(url);
    toast.success('Laporan berhasil didownload!');
  };

  const clearText = () => {
    setText('');
    toast.success('Teks berhasil dihapus!');
  };

  const loadSample = () => {
    const sample = `Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris.

Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.`;
    setText(sample);
    toast.success('Sample text dimuat!');
  };

  const StatCard = ({ label, value, icon }: { label: string; value: string | number; icon?: string }) => (
    <div className="bg-gray-50 p-4 rounded-lg">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-600">{label}</p>
          <p className="text-2xl font-bold text-gray-900">{typeof value === 'number' ? value.toLocaleString() : value}</p>
        </div>
        {icon && <span className="text-2xl">{icon}</span>}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            📊 Word Counter & Text Analyzer
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Input Section */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium">Teks untuk dianalisis:</label>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={loadSample}>
                  📄 Sample
                </Button>
                <label className="cursor-pointer">
                  <Button variant="outline" size="sm" asChild>
                    <span>
                      <Upload className="w-4 h-4 mr-1" />
                      Upload File
                    </span>
                  </Button>
                  <Input
                    type="file"
                    accept=".txt,.md,.doc,.docx"
                    onChange={handleFileUpload}
                    className="hidden"
                  />
                </label>
                <Button variant="outline" size="sm" onClick={clearText}>
                  🗑️ Clear
                </Button>
              </div>
            </div>
            <Textarea
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="Ketik atau paste teks Anda di sini..."
              className="min-h-[300px] font-mono text-sm"
            />
          </div>

          {/* Statistics Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <StatCard label="Karakter" value={stats.characters} icon="🔤" />
            <StatCard label="Kata" value={stats.words} icon="📝" />
            <StatCard label="Kalimat" value={stats.sentences} icon="📄" />
            <StatCard label="Paragraf" value={stats.paragraphs} icon="📋" />
          </div>

          {/* Detailed Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <StatCard label="Karakter (tanpa spasi)" value={stats.charactersNoSpaces} />
            <StatCard label="Waktu Baca" value={`${stats.readingTime} menit`} icon="👁️" />
            <StatCard label="Waktu Bicara" value={`${stats.speakingTime} menit`} icon="🗣️" />
          </div>

          {/* Advanced Analytics */}
          {stats.words > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <StatCard 
                label="Rata-rata kata/kalimat" 
                value={stats.sentences > 0 ? (stats.words / stats.sentences).toFixed(1) : 0} 
              />
              <StatCard 
                label="Rata-rata karakter/kata" 
                value={(stats.charactersNoSpaces / stats.words).toFixed(1)} 
              />
              <StatCard 
                label="Kepadatan teks" 
                value={`${((stats.charactersNoSpaces / stats.characters) * 100).toFixed(1)}%`} 
              />
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4 border-t">
            <Button onClick={copyStats} disabled={!text.trim()}>
              <Copy className="w-4 h-4 mr-2" />
              Copy Statistik
            </Button>
            <Button variant="outline" onClick={downloadReport} disabled={!text.trim()}>
              <Download className="w-4 h-4 mr-2" />
              Download Laporan
            </Button>
          </div>

          {/* Info */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">ℹ️ Informasi:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• <strong>Waktu Baca</strong>: Berdasarkan rata-rata 200 kata per menit</li>
              <li>• <strong>Waktu Bicara</strong>: Berdasarkan rata-rata 150 kata per menit</li>
              <li>• <strong>Kepadatan Teks</strong>: Persentase karakter non-spasi</li>
              <li>• Support file: TXT, MD, DOC, DOCX</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

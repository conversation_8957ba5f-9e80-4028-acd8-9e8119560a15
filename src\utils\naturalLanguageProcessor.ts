// Natural Language Understanding & Processing System
export interface IntentAnalysis {
  intent: string;
  confidence: number;
  entities: Entity[];
  sentiment: SentimentAnalysis;
  context: ContextAnalysis;
  responseType: ResponseType;
}

export interface Entity {
  type: string;
  value: string;
  confidence: number;
  start: number;
  end: number;
}

export interface SentimentAnalysis {
  polarity: 'positive' | 'negative' | 'neutral';
  intensity: number; // 0-1 scale
  emotions: string[];
}

export interface ContextAnalysis {
  isQuestion: boolean;
  isRequest: boolean;
  isComplaint: boolean;
  isCompliment: boolean;
  isPersonal: boolean;
  requiresAction: boolean;
  urgency: 'low' | 'medium' | 'high';
}

export type ResponseType = 'informative' | 'conversational' | 'supportive' | 'action' | 'creative' | 'technical';

export class NaturalLanguageProcessor {
  private static instance: NaturalLanguageProcessor;
  
  private intentPatterns = new Map<string, RegExp[]>([
    ['greeting', [
      /^(hai|halo|hello|hi|selamat)/i,
      /^(good morning|good afternoon|good evening)/i,
      /^(pagi|siang|sore|malam)/i
    ]],
    ['question', [
      /\?$/,
      /^(apa|bagaimana|mengapa|kenapa|dimana|kapan|siapa)/i,
      /^(what|how|why|where|when|who)/i,
      /^(bisa|could|can)/i
    ]],
    ['request', [
      /^(tolong|please|mohon)/i,
      /^(buatkan|create|generate)/i,
      /^(bantu|help)/i,
      /^(jelaskan|explain)/i
    ]],
    ['complaint', [
      /(tidak bisa|can't|cannot|error|gagal|failed)/i,
      /(lambat|slow|lag)/i,
      /(rusak|broken|bug)/i
    ]],
    ['compliment', [
      /(bagus|good|great|excellent|amazing)/i,
      /(terima kasih|thank you|thanks)/i,
      /(hebat|wonderful|fantastic)/i
    ]],
    ['personal', [
      /(nama saya|my name|i am|saya)/i,
      /(umur|age|tahun)/i,
      /(kerja|work|job|profesi)/i,
      /(hobi|hobby|suka)/i
    ]],
    ['technical', [
      /(code|coding|programming|javascript|python|react)/i,
      /(algorithm|database|api|framework)/i,
      /(bug|debug|error|syntax)/i
    ]],
    ['creative', [
      /(gambar|image|draw|design)/i,
      /(cerita|story|artikel|article)/i,
      /(kreatif|creative|ide|idea)/i
    ]]
  ]);

  private emotionKeywords = new Map<string, string[]>([
    ['happy', ['senang', 'bahagia', 'gembira', 'happy', 'joy', 'excited', 'antusias']],
    ['sad', ['sedih', 'kecewa', 'sad', 'disappointed', 'upset', 'down']],
    ['angry', ['marah', 'kesal', 'angry', 'frustrated', 'annoyed', 'mad']],
    ['confused', ['bingung', 'confused', 'tidak mengerti', 'unclear', 'lost']],
    ['surprised', ['kaget', 'surprised', 'shocked', 'amazed', 'wow']],
    ['worried', ['khawatir', 'worried', 'anxious', 'nervous', 'concerned']]
  ]);

  private entityPatterns = new Map<string, RegExp[]>([
    ['person', [
      /\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b/g,
      /\b(saya|aku|nama saya|my name)\s+([A-Za-z]+)/gi
    ]],
    ['location', [
      /\b(Jakarta|Bandung|Surabaya|Medan|Yogyakarta|Bali|Indonesia)\b/gi,
      /\b(di|in|from)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)/gi
    ]],
    ['time', [
      /\b(\d{1,2}:\d{2}|\d{1,2}\s*(am|pm))\b/gi,
      /\b(hari ini|today|besok|tomorrow|kemarin|yesterday)\b/gi,
      /\b(pagi|siang|sore|malam|morning|afternoon|evening|night)\b/gi
    ]],
    ['number', [
      /\b(\d+)\b/g,
      /\b(satu|dua|tiga|empat|lima|enam|tujuh|delapan|sembilan|sepuluh)\b/gi
    ]],
    ['technology', [
      /\b(JavaScript|Python|React|Node\.js|HTML|CSS|SQL|API)\b/gi,
      /\b(programming|coding|development|software|website|app)\b/gi
    ]]
  ]);

  private constructor() {}

  public static getInstance(): NaturalLanguageProcessor {
    if (!NaturalLanguageProcessor.instance) {
      NaturalLanguageProcessor.instance = new NaturalLanguageProcessor();
    }
    return NaturalLanguageProcessor.instance;
  }

  public analyzeMessage(message: string): IntentAnalysis {
    const intent = this.detectIntent(message);
    const entities = this.extractEntities(message);
    const sentiment = this.analyzeSentiment(message);
    const context = this.analyzeContext(message);
    const responseType = this.determineResponseType(intent.intent, context, sentiment);

    return {
      intent: intent.intent,
      confidence: intent.confidence,
      entities,
      sentiment,
      context,
      responseType
    };
  }

  private detectIntent(message: string): { intent: string; confidence: number } {
    let bestMatch = { intent: 'general', confidence: 0.3 };

    for (const [intent, patterns] of this.intentPatterns) {
      for (const pattern of patterns) {
        if (pattern.test(message)) {
          const confidence = this.calculatePatternConfidence(message, pattern);
          if (confidence > bestMatch.confidence) {
            bestMatch = { intent, confidence };
          }
        }
      }
    }

    return bestMatch;
  }

  private calculatePatternConfidence(message: string, pattern: RegExp): number {
    const matches = message.match(pattern);
    if (!matches) return 0;

    // Base confidence from pattern match
    let confidence = 0.6;

    // Increase confidence based on match length relative to message length
    const matchLength = matches[0].length;
    const messageLength = message.length;
    confidence += (matchLength / messageLength) * 0.3;

    // Increase confidence if match is at the beginning
    if (message.toLowerCase().startsWith(matches[0].toLowerCase())) {
      confidence += 0.1;
    }

    return Math.min(confidence, 1.0);
  }

  private extractEntities(message: string): Entity[] {
    const entities: Entity[] = [];

    for (const [type, patterns] of this.entityPatterns) {
      for (const pattern of patterns) {
        let match;
        while ((match = pattern.exec(message)) !== null) {
          entities.push({
            type,
            value: match[1] || match[0],
            confidence: 0.8,
            start: match.index,
            end: match.index + match[0].length
          });
        }
      }
    }

    return entities;
  }

  private analyzeSentiment(message: string): SentimentAnalysis {
    const lowerMessage = message.toLowerCase();
    let positiveScore = 0;
    let negativeScore = 0;
    const detectedEmotions: string[] = [];

    // Positive indicators
    const positiveWords = ['bagus', 'baik', 'senang', 'suka', 'terima kasih', 'hebat', 'amazing', 'good', 'great', 'excellent', 'love', 'like'];
    const negativeWords = ['buruk', 'jelek', 'tidak suka', 'benci', 'marah', 'kesal', 'bad', 'terrible', 'hate', 'angry', 'frustrated'];

    positiveWords.forEach(word => {
      if (lowerMessage.includes(word)) positiveScore += 1;
    });

    negativeWords.forEach(word => {
      if (lowerMessage.includes(word)) negativeScore += 1;
    });

    // Detect emotions
    for (const [emotion, keywords] of this.emotionKeywords) {
      for (const keyword of keywords) {
        if (lowerMessage.includes(keyword)) {
          detectedEmotions.push(emotion);
          break;
        }
      }
    }

    // Determine polarity
    let polarity: 'positive' | 'negative' | 'neutral' = 'neutral';
    let intensity = 0.5;

    if (positiveScore > negativeScore) {
      polarity = 'positive';
      intensity = Math.min(0.5 + (positiveScore * 0.2), 1.0);
    } else if (negativeScore > positiveScore) {
      polarity = 'negative';
      intensity = Math.min(0.5 + (negativeScore * 0.2), 1.0);
    }

    return {
      polarity,
      intensity,
      emotions: detectedEmotions
    };
  }

  private analyzeContext(message: string): ContextAnalysis {
    const lowerMessage = message.toLowerCase();

    return {
      isQuestion: /\?/.test(message) || /^(apa|bagaimana|mengapa|kenapa|dimana|kapan|siapa|what|how|why|where|when|who)/i.test(message),
      isRequest: /^(tolong|please|mohon|buatkan|create|bantu|help)/i.test(message),
      isComplaint: /(tidak bisa|can't|error|gagal|lambat|rusak)/i.test(message),
      isCompliment: /(bagus|good|terima kasih|thank you|hebat|amazing)/i.test(message),
      isPersonal: /(nama saya|my name|saya|umur|kerja|hobi)/i.test(message),
      requiresAction: /(buatkan|create|generate|download|export|save)/i.test(message),
      urgency: this.determineUrgency(message)
    };
  }

  private determineUrgency(message: string): 'low' | 'medium' | 'high' {
    const lowerMessage = message.toLowerCase();
    
    if (/(urgent|penting|segera|cepat|emergency|asap)/i.test(message)) {
      return 'high';
    } else if (/(tolong|please|mohon|butuh|need)/i.test(message)) {
      return 'medium';
    }
    
    return 'low';
  }

  private determineResponseType(intent: string, context: ContextAnalysis, sentiment: SentimentAnalysis): ResponseType {
    if (context.isComplaint || sentiment.polarity === 'negative') {
      return 'supportive';
    } else if (context.requiresAction || intent === 'request') {
      return 'action';
    } else if (intent === 'technical' || context.isQuestion) {
      return 'technical';
    } else if (intent === 'creative') {
      return 'creative';
    } else if (context.isPersonal || intent === 'greeting') {
      return 'conversational';
    } else {
      return 'informative';
    }
  }

  public generateResponseGuidelines(analysis: IntentAnalysis): string {
    let guidelines = '';

    // Response type guidelines
    switch (analysis.responseType) {
      case 'supportive':
        guidelines += 'Be empathetic, understanding, and offer helpful solutions. ';
        break;
      case 'action':
        guidelines += 'Provide clear, actionable steps and be direct. ';
        break;
      case 'technical':
        guidelines += 'Be precise, detailed, and use appropriate technical language. ';
        break;
      case 'creative':
        guidelines += 'Be imaginative, inspiring, and think outside the box. ';
        break;
      case 'conversational':
        guidelines += 'Be friendly, personal, and engaging in conversation. ';
        break;
      case 'informative':
        guidelines += 'Provide comprehensive, accurate information. ';
        break;
    }

    // Sentiment-based guidelines
    if (analysis.sentiment.polarity === 'positive') {
      guidelines += 'Match the positive energy and enthusiasm. ';
    } else if (analysis.sentiment.polarity === 'negative') {
      guidelines += 'Be extra supportive and focus on solutions. ';
    }

    // Context-based guidelines
    if (analysis.context.isQuestion) {
      guidelines += 'Provide a clear, comprehensive answer. ';
    }
    if (analysis.context.urgency === 'high') {
      guidelines += 'Prioritize immediate, practical help. ';
    }
    if (analysis.context.isPersonal) {
      guidelines += 'Be warm and show genuine interest. ';
    }

    // Entity-based guidelines
    const hasPersonEntity = analysis.entities.some(e => e.type === 'person');
    if (hasPersonEntity) {
      guidelines += 'Use the person\'s name when appropriate. ';
    }

    return guidelines;
  }

  public extractKeyTopics(message: string): string[] {
    const topics: string[] = [];
    const lowerMessage = message.toLowerCase();

    // Technology topics
    const techTopics = ['programming', 'javascript', 'python', 'react', 'ai', 'machine learning', 'web development', 'mobile app', 'database', 'api'];
    techTopics.forEach(topic => {
      if (lowerMessage.includes(topic)) topics.push(topic);
    });

    // Business topics
    const businessTopics = ['marketing', 'business', 'startup', 'finance', 'investment', 'strategy', 'management'];
    businessTopics.forEach(topic => {
      if (lowerMessage.includes(topic)) topics.push(topic);
    });

    // Creative topics
    const creativeTopics = ['design', 'art', 'music', 'writing', 'photography', 'video', 'creative'];
    creativeTopics.forEach(topic => {
      if (lowerMessage.includes(topic)) topics.push(topic);
    });

    // Personal topics
    const personalTopics = ['health', 'fitness', 'travel', 'food', 'hobby', 'education', 'career'];
    personalTopics.forEach(topic => {
      if (lowerMessage.includes(topic)) topics.push(topic);
    });

    return topics;
  }

  public generateSmartResponse(message: string, context: string): string {
    const analysis = this.analyzeMessage(message);
    const guidelines = this.generateResponseGuidelines(analysis);
    
    return `
ANALYSIS:
- Intent: ${analysis.intent} (${Math.round(analysis.confidence * 100)}% confidence)
- Sentiment: ${analysis.sentiment.polarity} (${Math.round(analysis.sentiment.intensity * 100)}% intensity)
- Response Type: ${analysis.responseType}
- Emotions Detected: ${analysis.sentiment.emotions.join(', ') || 'none'}
- Key Context: ${Object.entries(analysis.context).filter(([_, value]) => value).map(([key]) => key).join(', ')}

RESPONSE GUIDELINES:
${guidelines}

CONTEXT:
${context}

Please respond naturally while following these guidelines and maintaining conversation flow.
`;
  }
}


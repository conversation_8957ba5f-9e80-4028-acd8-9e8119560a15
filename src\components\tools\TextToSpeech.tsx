import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { Play, Pause, Square, Download, Volume2, Settings } from 'lucide-react';

interface Voice {
  voice: SpeechSynthesisVoice;
  name: string;
  lang: string;
  gender: string;
}

export const TextToSpeech: React.FC = () => {
  const [text, setText] = useState('');
  const [voices, setVoices] = useState<Voice[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<string>('');
  const [rate, setRate] = useState([1]);
  const [pitch, setPitch] = useState([1]);
  const [volume, setVolume] = useState([1]);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [currentUtterance, setCurrentUtterance] = useState<SpeechSynthesisUtterance | null>(null);

  useEffect(() => {
    const loadVoices = () => {
      const availableVoices = speechSynthesis.getVoices();
      const processedVoices: Voice[] = availableVoices.map(voice => ({
        voice,
        name: voice.name,
        lang: voice.lang,
        gender: voice.name.toLowerCase().includes('female') || voice.name.toLowerCase().includes('woman') ? 'Female' : 
               voice.name.toLowerCase().includes('male') || voice.name.toLowerCase().includes('man') ? 'Male' : 'Unknown'
      }));

      setVoices(processedVoices);
      
      // Set default voice (prefer Indonesian or English)
      const defaultVoice = processedVoices.find(v => 
        v.lang.startsWith('id') || v.lang.startsWith('en')
      );
      if (defaultVoice && !selectedVoice) {
        setSelectedVoice(defaultVoice.voice.name);
      }
    };

    loadVoices();
    speechSynthesis.addEventListener('voiceschanged', loadVoices);

    return () => {
      speechSynthesis.removeEventListener('voiceschanged', loadVoices);
    };
  }, [selectedVoice]);

  const speak = () => {
    if (!text.trim()) {
      toast.error('Masukkan teks untuk dibacakan!');
      return;
    }

    // Stop current speech
    speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    const voice = voices.find(v => v.voice.name === selectedVoice)?.voice;
    
    if (voice) {
      utterance.voice = voice;
    }
    
    utterance.rate = rate[0];
    utterance.pitch = pitch[0];
    utterance.volume = volume[0];

    utterance.onstart = () => {
      setIsPlaying(true);
      setIsPaused(false);
      toast.success('Mulai membacakan teks...');
    };

    utterance.onend = () => {
      setIsPlaying(false);
      setIsPaused(false);
      setCurrentUtterance(null);
      toast.success('Selesai membacakan teks');
    };

    utterance.onerror = (event) => {
      setIsPlaying(false);
      setIsPaused(false);
      setCurrentUtterance(null);
      toast.error(`Error: ${event.error}`);
    };

    setCurrentUtterance(utterance);
    speechSynthesis.speak(utterance);
  };

  const pause = () => {
    if (speechSynthesis.speaking && !speechSynthesis.paused) {
      speechSynthesis.pause();
      setIsPaused(true);
      toast.success('Dijeda');
    }
  };

  const resume = () => {
    if (speechSynthesis.paused) {
      speechSynthesis.resume();
      setIsPaused(false);
      toast.success('Dilanjutkan');
    }
  };

  const stop = () => {
    speechSynthesis.cancel();
    setIsPlaying(false);
    setIsPaused(false);
    setCurrentUtterance(null);
    toast.success('Dihentikan');
  };

  const downloadAudio = async () => {
    if (!text.trim()) {
      toast.error('Masukkan teks terlebih dahulu!');
      return;
    }

    toast.info('Fitur download audio akan tersedia di versi mendatang');
    // Note: Web Speech API doesn't support audio download directly
    // Would need server-side TTS service for this feature
  };

  const loadSample = (type: string) => {
    const samples = {
      greeting: 'Halo! Selamat datang di KIKAZE-AI. Saya adalah asisten AI yang siap membantu Anda.',
      news: 'Berita terkini hari ini: Teknologi artificial intelligence terus berkembang pesat dan memberikan dampak positif bagi kehidupan manusia.',
      story: 'Pada suatu hari di sebuah desa kecil, hiduplah seorang anak yang memiliki mimpi besar untuk menjadi seorang ilmuwan.',
      technical: 'Artificial Intelligence atau kecerdasan buatan adalah simulasi kecerdasan manusia yang diprogram dalam mesin.',
      multilingual: 'Hello, this is a test in English. Halo, ini adalah tes dalam bahasa Indonesia. 你好，这是中文测试。'
    };

    setText(samples[type as keyof typeof samples] || '');
    toast.success(`Sample ${type} dimuat!`);
  };

  const getVoicesByLanguage = () => {
    const grouped: { [key: string]: Voice[] } = {};
    voices.forEach(voice => {
      const lang = voice.lang.split('-')[0];
      if (!grouped[lang]) grouped[lang] = [];
      grouped[lang].push(voice);
    });
    return grouped;
  };

  const languageNames: { [key: string]: string } = {
    'en': 'English',
    'id': 'Indonesian',
    'es': 'Spanish',
    'fr': 'French',
    'de': 'German',
    'it': 'Italian',
    'pt': 'Portuguese',
    'ru': 'Russian',
    'ja': 'Japanese',
    'ko': 'Korean',
    'zh': 'Chinese',
    'ar': 'Arabic',
    'hi': 'Hindi'
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔊 Enhanced Text-to-Speech
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Text Input */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium">Teks untuk dibacakan:</label>
              <div className="flex gap-1 flex-wrap">
                <Button variant="outline" size="sm" onClick={() => loadSample('greeting')}>
                  👋 Greeting
                </Button>
                <Button variant="outline" size="sm" onClick={() => loadSample('news')}>
                  📰 News
                </Button>
                <Button variant="outline" size="sm" onClick={() => loadSample('story')}>
                  📚 Story
                </Button>
                <Button variant="outline" size="sm" onClick={() => loadSample('technical')}>
                  🔬 Technical
                </Button>
              </div>
            </div>
            <Textarea
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="Ketik atau paste teks yang ingin dibacakan..."
              className="min-h-[150px]"
            />
            <div className="text-sm text-gray-500">
              Karakter: {text.length} | Kata: {text.split(/\s+/).filter(word => word.length > 0).length}
            </div>
          </div>

          {/* Voice Selection */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Pilih Voice:</label>
            <Select value={selectedVoice} onValueChange={setSelectedVoice}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih voice..." />
              </SelectTrigger>
              <SelectContent className="max-h-60">
                {Object.entries(getVoicesByLanguage()).map(([lang, voiceList]) => (
                  <div key={lang}>
                    <div className="px-2 py-1 text-xs font-semibold text-gray-500 bg-gray-100">
                      {languageNames[lang] || lang.toUpperCase()}
                    </div>
                    {voiceList.map((voice) => (
                      <SelectItem key={voice.voice.name} value={voice.voice.name}>
                        <div className="flex justify-between items-center w-full">
                          <span>{voice.name}</span>
                          <span className="text-xs text-gray-500 ml-2">
                            {voice.gender} • {voice.lang}
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </div>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Voice Settings */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Speed: {rate[0].toFixed(1)}x</label>
              <Slider
                value={rate}
                onValueChange={setRate}
                max={2}
                min={0.1}
                step={0.1}
                className="w-full"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Pitch: {pitch[0].toFixed(1)}</label>
              <Slider
                value={pitch}
                onValueChange={setPitch}
                max={2}
                min={0}
                step={0.1}
                className="w-full"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Volume: {(volume[0] * 100).toFixed(0)}%</label>
              <Slider
                value={volume}
                onValueChange={setVolume}
                max={1}
                min={0}
                step={0.1}
                className="w-full"
              />
            </div>
          </div>

          {/* Control Buttons */}
          <div className="flex gap-2 justify-center">
            {!isPlaying ? (
              <Button onClick={speak} disabled={!text.trim()}>
                <Play className="w-4 h-4 mr-2" />
                Speak
              </Button>
            ) : (
              <>
                {!isPaused ? (
                  <Button onClick={pause} variant="outline">
                    <Pause className="w-4 h-4 mr-2" />
                    Pause
                  </Button>
                ) : (
                  <Button onClick={resume} variant="outline">
                    <Play className="w-4 h-4 mr-2" />
                    Resume
                  </Button>
                )}
                <Button onClick={stop} variant="outline">
                  <Square className="w-4 h-4 mr-2" />
                  Stop
                </Button>
              </>
            )}
            <Button variant="outline" onClick={downloadAudio} disabled={!text.trim()}>
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
          </div>

          {/* Status */}
          {isPlaying && (
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <Volume2 className="w-8 h-8 mx-auto text-blue-600 mb-2" />
              <p className="text-blue-800 font-medium">
                {isPaused ? '⏸️ Dijeda' : '🔊 Sedang membacakan...'}
              </p>
              <p className="text-sm text-blue-600">
                Voice: {voices.find(v => v.voice.name === selectedVoice)?.name || 'Default'}
              </p>
            </div>
          )}

          {/* Presets */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Voice Presets:</label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => {
                  setRate([0.8]);
                  setPitch([1]);
                  setVolume([1]);
                  toast.success('Slow & Clear preset applied');
                }}
              >
                🐌 Slow & Clear
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => {
                  setRate([1]);
                  setPitch([1]);
                  setVolume([1]);
                  toast.success('Normal preset applied');
                }}
              >
                😊 Normal
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => {
                  setRate([1.3]);
                  setPitch([1.1]);
                  setVolume([1]);
                  toast.success('Fast & Energetic preset applied');
                }}
              >
                ⚡ Fast & Energetic
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => {
                  setRate([0.9]);
                  setPitch([0.8]);
                  setVolume([0.8]);
                  toast.success('Deep Voice preset applied');
                }}
              >
                🎭 Deep Voice
              </Button>
            </div>
          </div>

          {/* Info */}
          <div className="bg-purple-50 p-4 rounded-lg">
            <h4 className="font-semibold text-purple-800 mb-2">ℹ️ Fitur Text-to-Speech:</h4>
            <ul className="text-sm text-purple-700 space-y-1">
              <li>• <strong>Multiple Voices</strong>: Pilih dari berbagai voice yang tersedia</li>
              <li>• <strong>Voice Control</strong>: Atur speed, pitch, dan volume</li>
              <li>• <strong>Multi-language</strong>: Support berbagai bahasa</li>
              <li>• <strong>Playback Control</strong>: Play, pause, resume, stop</li>
              <li>• <strong>Voice Presets</strong>: Quick settings untuk berbagai gaya</li>
              <li>• <strong>Real-time</strong>: Menggunakan Web Speech API browser</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

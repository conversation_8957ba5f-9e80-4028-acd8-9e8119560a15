# 🖥️ KIKAZE-AI DESKTOP BUILD GUIDE

## 🎯 **Overview**
Panduan lengkap untuk membangun KIKAZE-AI menjadi aplikasi desktop yang bisa diinstall sebagai file .exe (Windows), .dmg (Mac), atau .AppImage (Linux).

---

## 📋 **Prerequisites**

### **System Requirements:**
- **Node.js**: Version 18.0.0 atau lebih baru
- **NPM**: Version 8.0.0 atau lebih baru
- **Git**: Untuk clone repository
- **Windows**: Windows 10/11 (untuk build .exe)
- **Memory**: Minimal 4GB RAM
- **Storage**: Minimal 2GB free space

### **Development Tools:**
- **Code Editor**: VS Code (recommended)
- **Terminal**: PowerShell, Command Prompt, atau Git Bash
- **Optional**: Electron Fiddle untuk testing

---

## 🚀 **Quick Build (Recommended)**

### **1. One-Command Build:**
```bash
npm run build-exe
```

Ini akan:
- ✅ Clean previous builds
- ✅ Install dependencies
- ✅ Build React app
- ✅ Create Electron app
- ✅ Generate installer (.exe)

### **2. Output Location:**
```
📁 dist-electron/
  ├── 📄 KIKAZE-AI Setup 1.0.0.exe    (Windows Installer)
  ├── 📄 KIKAZE-AI-1.0.0.dmg          (Mac Installer)
  └── 📄 KIKAZE-AI-1.0.0.AppImage     (Linux Installer)
```

---

## 🔧 **Manual Build Process**

### **Step 1: Install Dependencies**
```bash
# Install all dependencies
npm install

# Install Electron-specific dependencies
npm install --save-dev electron electron-builder concurrently wait-on
```

### **Step 2: Build React App**
```bash
# Build optimized React app
npm run build
```

### **Step 3: Test Electron in Development**
```bash
# Run Electron with hot reload
npm run electron-dev
```

### **Step 4: Build Desktop App**
```bash
# Build for current platform
npm run dist

# Or build for specific platform:
npm run dist-win    # Windows
npm run dist-mac    # macOS
npm run dist-linux  # Linux
```

---

## 📦 **Build Configuration**

### **Electron Builder Settings:**
```json
{
  "build": {
    "appId": "com.kikaze.ai.desktop",
    "productName": "KIKAZE-AI",
    "directories": {
      "output": "dist-electron"
    },
    "win": {
      "target": "nsis",
      "icon": "electron/assets/icon.ico"
    },
    "nsis": {
      "oneClick": false,
      "allowToChangeInstallationDirectory": true,
      "createDesktopShortcut": true,
      "createStartMenuShortcut": true
    }
  }
}
```

### **App Features:**
- **Window Management**: Resizable, minimizable, closable
- **Menu Bar**: File, Edit, View, Tools, Help menus
- **Keyboard Shortcuts**: Ctrl+N (New Chat), Ctrl+E (Export), etc.
- **Auto Updates**: Built-in update mechanism
- **Native Notifications**: Desktop notifications
- **File Associations**: Open .json chat exports

---

## 🎨 **Customization**

### **App Icons:**
Replace icons in `electron/assets/`:
- **icon.png**: 512x512 PNG (Linux)
- **icon.ico**: 256x256 ICO (Windows)
- **icon.icns**: 512x512 ICNS (macOS)

### **App Information:**
Edit `package.json`:
```json
{
  "name": "kikaze-ai-desktop",
  "productName": "KIKAZE-AI",
  "description": "Advanced Indonesian AI Assistant",
  "version": "1.0.0",
  "author": "KIKAZE-AI Team"
}
```

### **Splash Screen:**
Customize `electron/splash.html`:
- Change logo and branding
- Modify loading animation
- Update app description

---

## 🔍 **Troubleshooting**

### **Common Issues:**

#### **1. Build Fails - Missing Dependencies**
```bash
# Solution: Clean install
rm -rf node_modules package-lock.json
npm install
```

#### **2. Electron Won't Start**
```bash
# Solution: Check Node version
node --version  # Should be 18+
npm run electron-dev  # Test in development
```

#### **3. Icon Not Showing**
```bash
# Solution: Add proper icon files
# Place icon.ico in electron/assets/
# Rebuild the app
npm run build-exe
```

#### **4. App Crashes on Startup**
```bash
# Solution: Check console for errors
npm run electron-dev  # Run in dev mode
# Check Developer Tools for errors
```

#### **5. Build Size Too Large**
```bash
# Solution: Optimize build
# Edit vite.config.ts to exclude unnecessary modules
# Use tree shaking and code splitting
```

---

## 📊 **Build Output Details**

### **File Sizes (Approximate):**
- **Windows .exe**: ~150-200 MB
- **macOS .dmg**: ~160-210 MB  
- **Linux .AppImage**: ~140-190 MB

### **Installation Size:**
- **Installed App**: ~300-400 MB
- **User Data**: ~10-50 MB (chats, settings, memory)

### **Performance:**
- **Startup Time**: 2-4 seconds
- **Memory Usage**: 100-200 MB
- **CPU Usage**: Low (1-5% idle)

---

## 🚀 **Distribution**

### **Windows Distribution:**
1. **NSIS Installer**: `KIKAZE-AI Setup 1.0.0.exe`
   - One-click installation
   - Desktop shortcut creation
   - Start menu integration
   - Uninstaller included

### **macOS Distribution:**
1. **DMG Package**: `KIKAZE-AI-1.0.0.dmg`
   - Drag-and-drop installation
   - Code signing (if configured)
   - Notarization support

### **Linux Distribution:**
1. **AppImage**: `KIKAZE-AI-1.0.0.AppImage`
   - Portable executable
   - No installation required
   - Universal Linux compatibility

---

## 🔐 **Security & Signing**

### **Code Signing (Optional):**
```bash
# Windows: Add certificate to build config
"win": {
  "certificateFile": "path/to/certificate.p12",
  "certificatePassword": "password"
}

# macOS: Configure developer certificate
"mac": {
  "identity": "Developer ID Application: Your Name"
}
```

### **Security Features:**
- **Context Isolation**: Enabled for security
- **Node Integration**: Disabled in renderer
- **Content Security Policy**: Implemented
- **External Link Protection**: Opens in default browser

---

## 📈 **Performance Optimization**

### **Build Optimizations:**
```javascript
// vite.config.ts optimizations
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-dialog']
        }
      }
    }
  }
});
```

### **Runtime Optimizations:**
- **Lazy Loading**: Components loaded on demand
- **Memory Management**: Automatic cleanup
- **Resource Caching**: Efficient asset loading
- **Background Processing**: Non-blocking operations

---

## 🎉 **Success Checklist**

### **Before Distribution:**
- [ ] App starts without errors
- [ ] All features work correctly
- [ ] Memory and TTS systems functional
- [ ] Tools and utilities accessible
- [ ] Settings persist between sessions
- [ ] Export/import functions work
- [ ] Auto-updates configured (optional)

### **Installation Test:**
- [ ] Installer runs without errors
- [ ] Desktop shortcut created
- [ ] Start menu entry added
- [ ] App launches from shortcuts
- [ ] Uninstaller works properly

---

## 🚀 **Final Steps**

### **1. Build the App:**
```bash
npm run build-exe
```

### **2. Test Installation:**
```bash
# Navigate to dist-electron folder
cd dist-electron

# Run the installer
./KIKAZE-AI Setup 1.0.0.exe  # Windows
```

### **3. Distribute:**
- Upload to website/GitHub releases
- Share installer with users
- Provide installation instructions

---

## 🎊 **Congratulations!**

**KIKAZE-AI Desktop App is ready for distribution! 🎉**

### **What You've Built:**
- ✅ **Professional Desktop App** dengan native OS integration
- ✅ **Easy Installation** dengan one-click installer
- ✅ **Full Feature Set** semua fitur web app tersedia
- ✅ **Optimized Performance** untuk desktop environment
- ✅ **Cross-Platform** support untuk Windows, Mac, Linux

### **Users Can Now:**
- 🖥️ **Install KIKAZE-AI** seperti aplikasi desktop biasa
- 🚀 **Launch from Desktop** atau Start Menu
- 💾 **Offline Functionality** (sebagian fitur)
- 🔄 **Auto Updates** (jika dikonfigurasi)
- 📁 **File Integration** dengan OS file system

**KIKAZE-AI is now a complete desktop application ready for professional use! 🇮🇩🚀**

import React, { useState, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { Workflow, Play, Plus, Trash2, Download, Copy, Settings, FileText, Mail, Database, Clock } from 'lucide-react';

interface WorkflowStep {
  id: string;
  type: 'Excel' | 'Word' | 'PDF' | 'Email' | 'File' | 'Condition' | 'Delay';
  name: string;
  action: string;
  inputs: Record<string, any>;
  outputs: Record<string, any>;
  position: { x: number; y: number };
  connections: string[];
}

interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  steps: WorkflowStep[];
  triggers: string[];
}

const workflowTemplates: WorkflowTemplate[] = [
  {
    id: 'invoice-automation',
    name: 'Invoice Generation Workflow',
    description: 'Automated invoice generation dari Excel data ke Word template',
    category: 'Business',
    steps: [
      {
        id: 'step1',
        type: 'Excel',
        name: 'Read Invoice Data',
        action: 'ReadExcelData',
        inputs: { filePath: 'C:\\Data\\invoices.xlsx', worksheet: 'Sheet1' },
        outputs: { data: 'invoiceData' },
        position: { x: 100, y: 100 },
        connections: ['step2']
      },
      {
        id: 'step2',
        type: 'Word',
        name: 'Generate Invoice',
        action: 'FillTemplate',
        inputs: { template: 'C:\\Templates\\invoice.docx', data: 'invoiceData' },
        outputs: { document: 'invoiceDoc' },
        position: { x: 300, y: 100 },
        connections: ['step3']
      },
      {
        id: 'step3',
        type: 'PDF',
        name: 'Convert to PDF',
        action: 'ConvertToPDF',
        inputs: { document: 'invoiceDoc' },
        outputs: { pdf: 'invoicePDF' },
        position: { x: 500, y: 100 },
        connections: ['step4']
      },
      {
        id: 'step4',
        type: 'Email',
        name: 'Send Invoice',
        action: 'SendEmail',
        inputs: { attachment: 'invoicePDF', recipient: '<EMAIL>' },
        outputs: { status: 'emailSent' },
        position: { x: 700, y: 100 },
        connections: []
      }
    ],
    triggers: ['Manual', 'Schedule', 'FileWatch']
  },
  {
    id: 'report-automation',
    name: 'Monthly Report Workflow',
    description: 'Generate monthly reports dari multiple data sources',
    category: 'Reporting',
    steps: [
      {
        id: 'step1',
        type: 'Excel',
        name: 'Collect Sales Data',
        action: 'ReadExcelData',
        inputs: { filePath: 'C:\\Data\\sales.xlsx' },
        outputs: { salesData: 'salesData' },
        position: { x: 100, y: 100 },
        connections: ['step3']
      },
      {
        id: 'step2',
        type: 'Database',
        name: 'Get Customer Data',
        action: 'QueryDatabase',
        inputs: { query: 'SELECT * FROM customers' },
        outputs: { customerData: 'customerData' },
        position: { x: 100, y: 250 },
        connections: ['step3']
      },
      {
        id: 'step3',
        type: 'Excel',
        name: 'Create Dashboard',
        action: 'CreatePivotTable',
        inputs: { salesData: 'salesData', customerData: 'customerData' },
        outputs: { dashboard: 'dashboard' },
        position: { x: 400, y: 175 },
        connections: ['step4']
      },
      {
        id: 'step4',
        type: 'Email',
        name: 'Send Report',
        action: 'SendEmail',
        inputs: { attachment: 'dashboard', recipients: '<EMAIL>' },
        outputs: { status: 'sent' },
        position: { x: 700, y: 175 },
        connections: []
      }
    ],
    triggers: ['Schedule']
  }
];

const stepTypes = [
  { id: 'Excel', name: 'Excel Operation', icon: <FileText className="w-4 h-4" />, color: 'bg-green-100 text-green-800' },
  { id: 'Word', name: 'Word Operation', icon: <FileText className="w-4 h-4" />, color: 'bg-blue-100 text-blue-800' },
  { id: 'PDF', name: 'PDF Operation', icon: <FileText className="w-4 h-4" />, color: 'bg-red-100 text-red-800' },
  { id: 'Email', name: 'Email Operation', icon: <Mail className="w-4 h-4" />, color: 'bg-purple-100 text-purple-800' },
  { id: 'File', name: 'File Operation', icon: <FileText className="w-4 h-4" />, color: 'bg-gray-100 text-gray-800' },
  { id: 'Database', name: 'Database Operation', icon: <Database className="w-4 h-4" />, color: 'bg-yellow-100 text-yellow-800' },
  { id: 'Condition', name: 'Conditional Logic', icon: <Settings className="w-4 h-4" />, color: 'bg-orange-100 text-orange-800' },
  { id: 'Delay', name: 'Delay/Wait', icon: <Clock className="w-4 h-4" />, color: 'bg-indigo-100 text-indigo-800' }
];

export const WorkflowBuilder: React.FC = () => {
  const [selectedTemplate, setSelectedTemplate] = useState<WorkflowTemplate>(workflowTemplates[0]);
  const [workflowSteps, setWorkflowSteps] = useState<WorkflowStep[]>(workflowTemplates[0].steps);
  const [selectedStep, setSelectedStep] = useState<WorkflowStep | null>(null);
  const [workflowName, setWorkflowName] = useState('My Workflow');
  const [isRunning, setIsRunning] = useState(false);

  const handleTemplateChange = (templateId: string) => {
    const template = workflowTemplates.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(template);
      setWorkflowSteps([...template.steps]);
      setWorkflowName(template.name);
    }
  };

  const addStep = (stepType: string) => {
    const newStep: WorkflowStep = {
      id: `step${Date.now()}`,
      type: stepType as any,
      name: `New ${stepType} Step`,
      action: 'DefaultAction',
      inputs: {},
      outputs: {},
      position: { x: 200 + workflowSteps.length * 150, y: 200 },
      connections: []
    };
    
    setWorkflowSteps([...workflowSteps, newStep]);
    toast.success(`${stepType} step added to workflow`);
  };

  const removeStep = (stepId: string) => {
    setWorkflowSteps(workflowSteps.filter(step => step.id !== stepId));
    if (selectedStep?.id === stepId) {
      setSelectedStep(null);
    }
    toast.success('Step removed from workflow');
  };

  const updateStep = (stepId: string, updates: Partial<WorkflowStep>) => {
    setWorkflowSteps(workflowSteps.map(step => 
      step.id === stepId ? { ...step, ...updates } : step
    ));
  };

  const runWorkflow = async () => {
    setIsRunning(true);
    
    // Simulate workflow execution
    for (let i = 0; i < workflowSteps.length; i++) {
      const step = workflowSteps[i];
      toast.info(`Executing: ${step.name}`);
      
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    setIsRunning(false);
    toast.success('Workflow completed successfully!');
  };

  const exportWorkflow = () => {
    const workflowData = {
      name: workflowName,
      steps: workflowSteps,
      created: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(workflowData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${workflowName.replace(/\s+/g, '_')}.json`;
    link.click();
    URL.revokeObjectURL(url);
    
    toast.success('Workflow exported successfully!');
  };

  const generateCode = () => {
    let code = `' Generated Workflow: ${workflowName}\n`;
    code += `' Created: ${new Date().toISOString()}\n\n`;
    
    workflowSteps.forEach((step, index) => {
      code += `' Step ${index + 1}: ${step.name}\n`;
      code += `Sub Execute${step.type}Step${index + 1}()\n`;
      code += `    ' ${step.action}\n`;
      
      Object.entries(step.inputs).forEach(([key, value]) => {
        code += `    Dim ${key} As String\n`;
        code += `    ${key} = "${value}"\n`;
      });
      
      code += `    ' TODO: Implement ${step.action} logic\n`;
      code += `    MsgBox "Executing ${step.name}", vbInformation\n`;
      code += `End Sub\n\n`;
    });
    
    code += `' Main workflow execution\n`;
    code += `Sub RunWorkflow()\n`;
    workflowSteps.forEach((step, index) => {
      code += `    Execute${step.type}Step${index + 1}\n`;
    });
    code += `End Sub\n`;
    
    navigator.clipboard.writeText(code);
    toast.success('Workflow VBA code copied to clipboard!');
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Workflow className="w-5 h-5" />
            Visual Workflow Builder
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Controls */}
            <div className="lg:col-span-1 space-y-6">
              <Tabs defaultValue="templates" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="templates">Templates</TabsTrigger>
                  <TabsTrigger value="steps">Steps</TabsTrigger>
                  <TabsTrigger value="settings">Settings</TabsTrigger>
                </TabsList>
                
                <TabsContent value="templates" className="space-y-4">
                  <div>
                    <Label>Workflow Templates</Label>
                    <Select value={selectedTemplate.id} onValueChange={handleTemplateChange}>
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {workflowTemplates.map((template) => (
                          <SelectItem key={template.id} value={template.id}>
                            {template.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <Card className="p-3">
                    <h4 className="font-medium">{selectedTemplate.name}</h4>
                    <p className="text-sm text-gray-600 mt-1">{selectedTemplate.description}</p>
                    <Badge variant="outline" className="mt-2">{selectedTemplate.category}</Badge>
                  </Card>

                  <div>
                    <Label>Add Step Type</Label>
                    <div className="grid grid-cols-1 gap-2 mt-2">
                      {stepTypes.map((stepType) => (
                        <Button
                          key={stepType.id}
                          variant="outline"
                          size="sm"
                          onClick={() => addStep(stepType.id)}
                          className="justify-start"
                        >
                          {stepType.icon}
                          <span className="ml-2">{stepType.name}</span>
                        </Button>
                      ))}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="steps" className="space-y-4">
                  <div>
                    <Label>Workflow Steps ({workflowSteps.length})</Label>
                    <div className="space-y-2 mt-2 max-h-64 overflow-y-auto">
                      {workflowSteps.map((step, index) => (
                        <Card
                          key={step.id}
                          className={`cursor-pointer transition-all ${
                            selectedStep?.id === step.id
                              ? 'ring-2 ring-blue-500 bg-blue-50'
                              : 'hover:bg-gray-50'
                          }`}
                          onClick={() => setSelectedStep(step)}
                        >
                          <CardContent className="p-3">
                            <div className="flex justify-between items-center">
                              <div className="flex items-center gap-2">
                                <Badge className={stepTypes.find(t => t.id === step.type)?.color}>
                                  {step.type}
                                </Badge>
                                <span className="text-sm font-medium">{step.name}</span>
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  removeStep(step.id);
                                }}
                              >
                                <Trash2 className="w-4 h-4" />
                              </Button>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">Step {index + 1}: {step.action}</p>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>

                  {selectedStep && (
                    <Card className="p-4">
                      <h4 className="font-medium mb-3">Edit Step: {selectedStep.name}</h4>
                      <div className="space-y-3">
                        <div>
                          <Label>Step Name</Label>
                          <Input
                            value={selectedStep.name}
                            onChange={(e) => updateStep(selectedStep.id, { name: e.target.value })}
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label>Action</Label>
                          <Input
                            value={selectedStep.action}
                            onChange={(e) => updateStep(selectedStep.id, { action: e.target.value })}
                            className="mt-1"
                          />
                        </div>
                      </div>
                    </Card>
                  )}
                </TabsContent>

                <TabsContent value="settings" className="space-y-4">
                  <div>
                    <Label htmlFor="workflow-name">Workflow Name</Label>
                    <Input
                      id="workflow-name"
                      value={workflowName}
                      onChange={(e) => setWorkflowName(e.target.value)}
                      className="mt-2"
                    />
                  </div>

                  <div className="space-y-2">
                    <Button
                      onClick={runWorkflow}
                      disabled={isRunning || workflowSteps.length === 0}
                      className="w-full"
                    >
                      {isRunning ? (
                        <>
                          <Play className="w-4 h-4 mr-2 animate-pulse" />
                          Running Workflow...
                        </>
                      ) : (
                        <>
                          <Play className="w-4 h-4 mr-2" />
                          Run Workflow
                        </>
                      )}
                    </Button>

                    <div className="grid grid-cols-2 gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={generateCode}
                        disabled={workflowSteps.length === 0}
                      >
                        <Copy className="w-4 h-4 mr-1" />
                        Copy VBA
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={exportWorkflow}
                        disabled={workflowSteps.length === 0}
                      >
                        <Download className="w-4 h-4 mr-1" />
                        Export
                      </Button>
                    </div>
                  </div>

                  <div className="p-3 bg-green-50 rounded-lg">
                    <h4 className="font-medium text-green-900 mb-2">🔄 Workflow Features</h4>
                    <ul className="text-sm text-green-800 space-y-1">
                      <li>• Visual drag-drop interface</li>
                      <li>• Multi-step automation</li>
                      <li>• Conditional logic support</li>
                      <li>• VBA code generation</li>
                      <li>• Template library</li>
                    </ul>
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            {/* Visual Workflow Canvas */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Workflow Canvas</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="relative bg-gray-50 rounded-lg p-4 min-h-96 overflow-auto">
                    {workflowSteps.map((step, index) => (
                      <div
                        key={step.id}
                        className={`absolute bg-white border-2 rounded-lg p-3 cursor-pointer transition-all ${
                          selectedStep?.id === step.id
                            ? 'border-blue-500 shadow-lg'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        style={{
                          left: `${step.position.x}px`,
                          top: `${step.position.y}px`,
                          width: '140px'
                        }}
                        onClick={() => setSelectedStep(step)}
                      >
                        <div className="text-center">
                          <Badge className={`${stepTypes.find(t => t.id === step.type)?.color} mb-2`}>
                            {step.type}
                          </Badge>
                          <p className="text-xs font-medium">{step.name}</p>
                          <p className="text-xs text-gray-500 mt-1">{step.action}</p>
                        </div>
                        
                        {/* Connection lines */}
                        {index < workflowSteps.length - 1 && (
                          <div className="absolute top-1/2 -right-6 w-6 h-0.5 bg-gray-400"></div>
                        )}
                      </div>
                    ))}
                    
                    {workflowSteps.length === 0 && (
                      <div className="flex items-center justify-center h-full text-gray-500">
                        <div className="text-center">
                          <Workflow className="w-12 h-12 mx-auto mb-4 opacity-50" />
                          <p>No workflow steps yet</p>
                          <p className="text-sm">Add steps from the left panel</p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

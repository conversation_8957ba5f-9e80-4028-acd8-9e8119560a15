import React, { useState, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Upload, Download, Scissors, RefreshCw } from 'lucide-react';
import { removeBackground } from '@imgly/background-removal';

interface ProcessedImage {
  id: string;
  originalFile: File;
  originalUrl: string;
  processedUrl: string;
  processedBlob: Blob;
}

export const BackgroundRemoval: React.FC = () => {
  const [images, setImages] = useState<ProcessedImage[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedBackground, setSelectedBackground] = useState<string>('transparent');
  
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    setIsProcessing(true);

    for (const file of files) {
      if (!file.type.startsWith('image/')) {
        toast.error(`${file.name} bukan file gambar!`);
        continue;
      }

      try {
        toast.info(`Processing ${file.name}...`);
        
        // Remove background using @imgly/background-removal
        const imageBlob = await removeBackground(file);
        
        let finalBlob = imageBlob;
        
        // If user wants colored background instead of transparent
        if (selectedBackground !== 'transparent') {
          finalBlob = await addColoredBackground(imageBlob, selectedBackground);
        }
        
        const originalUrl = URL.createObjectURL(file);
        const processedUrl = URL.createObjectURL(finalBlob);

        const newImage: ProcessedImage = {
          id: Date.now().toString() + Math.random(),
          originalFile: file,
          originalUrl,
          processedUrl,
          processedBlob: finalBlob
        };

        setImages(prev => [...prev, newImage]);
        toast.success(`${file.name} background removed successfully!`);
      } catch (error) {
        console.error('Background removal error:', error);
        toast.error(`Failed to process ${file.name}`);
      }
    }

    setIsProcessing(false);
    event.target.value = '';
  };

  const addColoredBackground = async (imageBlob: Blob, color: string): Promise<Blob> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        if (!ctx) return;

        canvas.width = img.width;
        canvas.height = img.height;

        // Fill background with color
        ctx.fillStyle = color;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Draw the image with removed background on top
        ctx.drawImage(img, 0, 0);

        canvas.toBlob((blob) => {
          if (blob) resolve(blob);
        }, 'image/png');
      };

      img.src = URL.createObjectURL(imageBlob);
    });
  };

  const downloadImage = (image: ProcessedImage) => {
    const link = document.createElement('a');
    link.href = image.processedUrl;
    const fileName = image.originalFile.name.replace(/\.[^/.]+$/, '') + '_no_bg.png';
    link.download = fileName;
    link.click();
    toast.success('Image downloaded!');
  };

  const downloadAll = () => {
    images.forEach((image, index) => {
      setTimeout(() => {
        downloadImage(image);
      }, index * 500);
    });
    toast.success(`${images.length} images will be downloaded!`);
  };

  const removeImage = (id: string) => {
    setImages(prev => {
      const imageToRemove = prev.find(img => img.id === id);
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.originalUrl);
        URL.revokeObjectURL(imageToRemove.processedUrl);
      }
      return prev.filter(img => img.id !== id);
    });
    toast.success('Image removed!');
  };

  const clearAll = () => {
    images.forEach(image => {
      URL.revokeObjectURL(image.originalUrl);
      URL.revokeObjectURL(image.processedUrl);
    });
    setImages([]);
    toast.success('All images cleared!');
  };

  const backgroundOptions = [
    { value: 'transparent', label: 'Transparent', color: 'transparent' },
    { value: '#FFFFFF', label: 'White', color: '#FFFFFF' },
    { value: '#000000', label: 'Black', color: '#000000' },
    { value: '#FF0000', label: 'Red', color: '#FF0000' },
    { value: '#00FF00', label: 'Green', color: '#00FF00' },
    { value: '#0000FF', label: 'Blue', color: '#0000FF' },
    { value: '#FFFF00', label: 'Yellow', color: '#FFFF00' },
    { value: '#FF00FF', label: 'Magenta', color: '#FF00FF' }
  ];

  const loadSampleImage = () => {
    // Create a sample image URL for testing
    const sampleUrl = 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400';
    fetch(sampleUrl)
      .then(response => response.blob())
      .then(blob => {
        const file = new File([blob], 'sample-person.jpg', { type: 'image/jpeg' });
        const event = {
          target: { files: [file], value: '' }
        } as any;
        handleImageUpload(event);
      })
      .catch(() => {
        toast.error('Failed to load sample image');
      });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            ✂️ Background Removal
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Background Options */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Background Type:</label>
            <div className="grid grid-cols-4 md:grid-cols-8 gap-2">
              {backgroundOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={selectedBackground === option.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedBackground(option.value)}
                  className="h-12 flex flex-col items-center justify-center"
                >
                  <div 
                    className="w-6 h-6 rounded border-2 border-gray-300 mb-1"
                    style={{ 
                      backgroundColor: option.color,
                      backgroundImage: option.value === 'transparent' 
                        ? 'linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%)'
                        : 'none',
                      backgroundSize: option.value === 'transparent' ? '8px 8px' : 'auto',
                      backgroundPosition: option.value === 'transparent' ? '0 0, 0 4px, 4px -4px, -4px 0px' : 'auto'
                    }}
                  />
                  <span className="text-xs">{option.label}</span>
                </Button>
              ))}
            </div>
          </div>

          {/* Upload Section */}
          <div className="space-y-4">
            <label className="cursor-pointer">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors">
                <Upload className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <p className="text-lg font-medium text-gray-700">
                  {isProcessing ? 'Removing backgrounds...' : 'Upload images to remove background'}
                </p>
                <p className="text-sm text-gray-500">
                  Support: JPG, PNG, WebP (Multiple files)
                </p>
                <p className="text-xs text-gray-400 mt-2">
                  Best results with clear subject separation
                </p>
                {isProcessing && (
                  <div className="mt-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  </div>
                )}
              </div>
              <Input
                type="file"
                multiple
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
                disabled={isProcessing}
              />
            </label>
          </div>

          {/* Sample Button */}
          <div className="text-center">
            <Button variant="outline" onClick={loadSampleImage} disabled={isProcessing}>
              📷 Try Sample Image
            </Button>
          </div>

          {/* Action Buttons */}
          {images.length > 0 && (
            <div className="flex gap-2">
              <Button onClick={downloadAll}>
                <Download className="w-4 h-4 mr-2" />
                Download All
              </Button>
              <Button variant="outline" onClick={clearAll}>
                🗑️ Clear All
              </Button>
            </div>
          )}

          {/* Results */}
          {images.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Processed Images:</h3>
              <div className="grid gap-6">
                {images.map((image) => (
                  <Card key={image.id} className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
                      {/* Original Image */}
                      <div className="text-center">
                        <h4 className="text-sm font-medium mb-2">Original</h4>
                        <img 
                          src={image.originalUrl} 
                          alt="Original" 
                          className="w-full h-40 object-cover rounded border"
                        />
                      </div>

                      {/* Arrow */}
                      <div className="text-center">
                        <Scissors className="w-8 h-8 mx-auto text-blue-500" />
                        <p className="text-xs text-gray-500 mt-1">Background Removed</p>
                      </div>

                      {/* Processed Image */}
                      <div className="text-center">
                        <h4 className="text-sm font-medium mb-2">Result</h4>
                        <div 
                          className="w-full h-40 rounded border flex items-center justify-center"
                          style={{
                            backgroundImage: selectedBackground === 'transparent' 
                              ? 'linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%)'
                              : 'none',
                            backgroundSize: selectedBackground === 'transparent' ? '20px 20px' : 'auto',
                            backgroundPosition: selectedBackground === 'transparent' ? '0 0, 0 10px, 10px -10px, -10px 0px' : 'auto',
                            backgroundColor: selectedBackground !== 'transparent' ? selectedBackground : 'transparent'
                          }}
                        >
                          <img 
                            src={image.processedUrl} 
                            alt="Processed" 
                            className="max-w-full max-h-full object-contain"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 justify-center mt-4">
                      <Button size="sm" onClick={() => downloadImage(image)}>
                        <Download className="w-4 h-4 mr-1" />
                        Download
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        onClick={() => removeImage(image.id)}
                        className="text-red-600"
                      >
                        🗑️ Remove
                      </Button>
                    </div>

                    <div className="mt-2 text-center">
                      <p className="text-sm font-medium truncate">{image.originalFile.name}</p>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Tips */}
          <div className="bg-yellow-50 p-4 rounded-lg">
            <h4 className="font-semibold text-yellow-800 mb-2">💡 Tips for Best Results:</h4>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• Use images with clear subject-background separation</li>
              <li>• Avoid complex backgrounds or similar colors to subject</li>
              <li>• Higher resolution images generally work better</li>
              <li>• Portrait photos and product images work best</li>
              <li>• Processing may take a few seconds per image</li>
            </ul>
          </div>

          {/* Info */}
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-semibold text-green-800 mb-2">ℹ️ Background Removal Features:</h4>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• <strong>AI-Powered</strong>: Advanced machine learning for accurate removal</li>
              <li>• <strong>Batch Processing</strong>: Process multiple images at once</li>
              <li>• <strong>Custom Backgrounds</strong>: Transparent or colored backgrounds</li>
              <li>• <strong>High Quality</strong>: Preserves image quality and details</li>
              <li>• <strong>Privacy First</strong>: All processing done locally in browser</li>
              <li>• <strong>No Limits</strong>: Process unlimited images for free</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

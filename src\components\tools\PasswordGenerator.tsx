import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { toast } from 'sonner';
import { Copy, RefreshCw, Shield, Eye, EyeOff } from 'lucide-react';

export const PasswordGenerator: React.FC = () => {
  const [password, setPassword] = useState('');
  const [length, setLength] = useState([12]);
  const [showPassword, setShowPassword] = useState(true);
  const [options, setOptions] = useState({
    uppercase: true,
    lowercase: true,
    numbers: true,
    symbols: true,
    excludeSimilar: false,
    excludeAmbiguous: false
  });

  const charSets = {
    uppercase: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
    lowercase: 'abcdefghijklmnopqrstuvwxyz',
    numbers: '0123456789',
    symbols: '!@#$%^&*()_+-=[]{}|;:,.<>?',
    similar: 'il1Lo0O', // Characters that look similar
    ambiguous: '{}[]()/\\\'"`~,;.<>' // Ambiguous characters
  };

  const generatePassword = () => {
    let charset = '';
    
    if (options.uppercase) charset += charSets.uppercase;
    if (options.lowercase) charset += charSets.lowercase;
    if (options.numbers) charset += charSets.numbers;
    if (options.symbols) charset += charSets.symbols;

    if (options.excludeSimilar) {
      charset = charset.split('').filter(char => !charSets.similar.includes(char)).join('');
    }

    if (options.excludeAmbiguous) {
      charset = charset.split('').filter(char => !charSets.ambiguous.includes(char)).join('');
    }

    if (charset === '') {
      toast.error('Pilih minimal satu jenis karakter!');
      return;
    }

    let result = '';
    const passwordLength = length[0];

    // Ensure at least one character from each selected type
    if (options.uppercase && charset.includes('A')) {
      result += charSets.uppercase[Math.floor(Math.random() * charSets.uppercase.length)];
    }
    if (options.lowercase && charset.includes('a')) {
      result += charSets.lowercase[Math.floor(Math.random() * charSets.lowercase.length)];
    }
    if (options.numbers && charset.includes('0')) {
      result += charSets.numbers[Math.floor(Math.random() * charSets.numbers.length)];
    }
    if (options.symbols && charset.includes('!')) {
      result += charSets.symbols[Math.floor(Math.random() * charSets.symbols.length)];
    }

    // Fill the rest randomly
    for (let i = result.length; i < passwordLength; i++) {
      result += charset[Math.floor(Math.random() * charset.length)];
    }

    // Shuffle the password
    result = result.split('').sort(() => Math.random() - 0.5).join('');

    setPassword(result);
    toast.success('Password berhasil digenerate!');
  };

  const calculateStrength = (pwd: string): { score: number; label: string; color: string } => {
    let score = 0;
    
    if (pwd.length >= 8) score += 1;
    if (pwd.length >= 12) score += 1;
    if (/[a-z]/.test(pwd)) score += 1;
    if (/[A-Z]/.test(pwd)) score += 1;
    if (/[0-9]/.test(pwd)) score += 1;
    if (/[^A-Za-z0-9]/.test(pwd)) score += 1;
    if (pwd.length >= 16) score += 1;

    if (score <= 2) return { score, label: 'Lemah', color: 'text-red-600' };
    if (score <= 4) return { score, label: 'Sedang', color: 'text-yellow-600' };
    if (score <= 5) return { score, label: 'Kuat', color: 'text-green-600' };
    return { score, label: 'Sangat Kuat', color: 'text-green-800' };
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(password);
      toast.success('Password berhasil disalin ke clipboard!');
    } catch (error) {
      toast.error('Gagal menyalin ke clipboard');
    }
  };

  const presets = [
    { name: 'Default', length: 12, options: { uppercase: true, lowercase: true, numbers: true, symbols: true, excludeSimilar: false, excludeAmbiguous: false } },
    { name: 'Simple', length: 8, options: { uppercase: true, lowercase: true, numbers: true, symbols: false, excludeSimilar: true, excludeAmbiguous: true } },
    { name: 'Complex', length: 16, options: { uppercase: true, lowercase: true, numbers: true, symbols: true, excludeSimilar: true, excludeAmbiguous: false } },
    { name: 'PIN', length: 6, options: { uppercase: false, lowercase: false, numbers: true, symbols: false, excludeSimilar: false, excludeAmbiguous: false } }
  ];

  const applyPreset = (preset: typeof presets[0]) => {
    setLength([preset.length]);
    setOptions(preset.options);
    toast.success(`Preset "${preset.name}" diterapkan!`);
  };

  // Auto-generate password when options change
  useEffect(() => {
    generatePassword();
  }, [length, options]);

  const strength = password ? calculateStrength(password) : { score: 0, label: '', color: '' };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔐 Password Generator
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Generated Password */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium">Generated Password:</label>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={() => setShowPassword(!showPassword)}>
                  {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </Button>
                <Button variant="outline" size="sm" onClick={copyToClipboard} disabled={!password}>
                  <Copy className="w-4 h-4 mr-1" />
                  Copy
                </Button>
                <Button size="sm" onClick={generatePassword}>
                  <RefreshCw className="w-4 h-4 mr-1" />
                  Generate
                </Button>
              </div>
            </div>
            <Input
              value={password}
              readOnly
              type={showPassword ? 'text' : 'password'}
              className="font-mono text-lg bg-gray-50"
              placeholder="Password akan muncul di sini..."
            />
            {password && (
              <div className="flex items-center gap-2">
                <Shield className="w-4 h-4" />
                <span className="text-sm">Kekuatan:</span>
                <span className={`text-sm font-semibold ${strength.color}`}>
                  {strength.label}
                </span>
                <div className="flex-1 bg-gray-200 rounded-full h-2 ml-2">
                  <div 
                    className={`h-2 rounded-full transition-all ${
                      strength.score <= 2 ? 'bg-red-500' :
                      strength.score <= 4 ? 'bg-yellow-500' :
                      strength.score <= 5 ? 'bg-green-500' : 'bg-green-700'
                    }`}
                    style={{ width: `${(strength.score / 7) * 100}%` }}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Password Length */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Panjang Password: {length[0]}</label>
            <Slider
              value={length}
              onValueChange={setLength}
              max={50}
              min={4}
              step={1}
              className="w-full"
            />
          </div>

          {/* Character Options */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Jenis Karakter:</label>
            <div className="grid grid-cols-2 gap-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="uppercase"
                  checked={options.uppercase}
                  onCheckedChange={(checked) => setOptions(prev => ({ ...prev, uppercase: !!checked }))}
                />
                <label htmlFor="uppercase" className="text-sm">Huruf Besar (A-Z)</label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="lowercase"
                  checked={options.lowercase}
                  onCheckedChange={(checked) => setOptions(prev => ({ ...prev, lowercase: !!checked }))}
                />
                <label htmlFor="lowercase" className="text-sm">Huruf Kecil (a-z)</label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="numbers"
                  checked={options.numbers}
                  onCheckedChange={(checked) => setOptions(prev => ({ ...prev, numbers: !!checked }))}
                />
                <label htmlFor="numbers" className="text-sm">Angka (0-9)</label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="symbols"
                  checked={options.symbols}
                  onCheckedChange={(checked) => setOptions(prev => ({ ...prev, symbols: !!checked }))}
                />
                <label htmlFor="symbols" className="text-sm">Simbol (!@#$%)</label>
              </div>
            </div>
          </div>

          {/* Advanced Options */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Opsi Lanjutan:</label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="excludeSimilar"
                  checked={options.excludeSimilar}
                  onCheckedChange={(checked) => setOptions(prev => ({ ...prev, excludeSimilar: !!checked }))}
                />
                <label htmlFor="excludeSimilar" className="text-sm">Hindari karakter mirip (i, l, 1, L, o, 0, O)</label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="excludeAmbiguous"
                  checked={options.excludeAmbiguous}
                  onCheckedChange={(checked) => setOptions(prev => ({ ...prev, excludeAmbiguous: !!checked }))}
                />
                <label htmlFor="excludeAmbiguous" className="text-sm">Hindari karakter ambigu ({`{}[]()/\\'"~,;.<>`})</label>
              </div>
            </div>
          </div>

          {/* Presets */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Preset:</label>
            <div className="grid grid-cols-2 gap-2">
              {presets.map((preset) => (
                <Button
                  key={preset.name}
                  variant="outline"
                  size="sm"
                  onClick={() => applyPreset(preset)}
                  className="text-xs"
                >
                  {preset.name}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

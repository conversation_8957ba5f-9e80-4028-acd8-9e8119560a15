@echo off
echo ========================================
echo KIKAZE-AI Simple Build (No File Locks)
echo ========================================
echo.

echo 1. Killing all Node/Electron processes...
taskkill /f /im node.exe >nul 2>&1
taskkill /f /im electron.exe >nul 2>&1
taskkill /f /im KIKAZE-AI.exe >nul 2>&1
echo    Done!

echo.
echo 2. Waiting for processes to close...
timeout /t 3 >nul

echo.
echo 3. Trying to remove old build (if exists)...
if exist "dist-electron" (
    echo    Removing dist-electron...
    rmdir /s /q "dist-electron" >nul 2>&1
    if exist "dist-electron" (
        echo    WARNING: Some files still locked, continuing anyway...
    ) else (
        echo    Successfully removed!
    )
) else (
    echo    No previous build found.
)

echo.
echo 4. Building React app...
call npm run build
if %errorlevel% neq 0 (
    echo ERROR: React build failed!
    pause
    exit /b 1
)
echo    React build successful!

echo.
echo 5. Building Electron app with fresh start...
call npm run dist-win
if %errorlevel% neq 0 (
    echo ERROR: Electron build failed!
    echo.
    echo TROUBLESHOOTING:
    echo - Try restarting your computer
    echo - Run as Administrator
    echo - Close all file explorers
    echo - Disable antivirus temporarily
    pause
    exit /b 1
)

echo.
echo ========================================
echo SUCCESS! Build completed!
echo ========================================
echo.
echo Your installer is ready:
if exist "dist-electron\KIKAZE-AI Setup 1.0.0.exe" (
    echo ✅ dist-electron\KIKAZE-AI Setup 1.0.0.exe
) else (
    echo ❌ Installer not found, but build may have succeeded
    echo    Check dist-electron folder manually
)
echo.
echo You can now:
echo 1. Test: Double-click the installer
echo 2. Distribute: Share the .exe file
echo.
pause

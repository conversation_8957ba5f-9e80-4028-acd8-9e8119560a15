var qc=e=>{throw TypeError(e)};var Rl=(e,t,n)=>t.has(e)||qc("Cannot "+n);var N=(e,t,n)=>(Rl(e,t,"read from private field"),n?n.call(e):t.get(e)),q=(e,t,n)=>t.has(e)?qc("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),K=(e,t,n,r)=>(Rl(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),Te=(e,t,n)=>(Rl(e,t,"access private method"),n);var ki=(e,t,n,r)=>({set _(o){K(e,t,o,n)},get _(){return N(e,t,r)}});function Bg(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const i=Object.getOwnPropertyDescriptor(r,o);i&&Object.defineProperty(e,o,i.get?i:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();function ap(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var up={exports:{}},Gs={},cp={exports:{}},G={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var pi=Symbol.for("react.element"),Wg=Symbol.for("react.portal"),Hg=Symbol.for("react.fragment"),Vg=Symbol.for("react.strict_mode"),Qg=Symbol.for("react.profiler"),Kg=Symbol.for("react.provider"),Gg=Symbol.for("react.context"),Yg=Symbol.for("react.forward_ref"),Xg=Symbol.for("react.suspense"),qg=Symbol.for("react.memo"),Zg=Symbol.for("react.lazy"),Zc=Symbol.iterator;function Jg(e){return e===null||typeof e!="object"?null:(e=Zc&&e[Zc]||e["@@iterator"],typeof e=="function"?e:null)}var dp={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},fp=Object.assign,pp={};function ro(e,t,n){this.props=e,this.context=t,this.refs=pp,this.updater=n||dp}ro.prototype.isReactComponent={};ro.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};ro.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function hp(){}hp.prototype=ro.prototype;function Ru(e,t,n){this.props=e,this.context=t,this.refs=pp,this.updater=n||dp}var Ou=Ru.prototype=new hp;Ou.constructor=Ru;fp(Ou,ro.prototype);Ou.isPureReactComponent=!0;var Jc=Array.isArray,mp=Object.prototype.hasOwnProperty,_u={current:null},vp={key:!0,ref:!0,__self:!0,__source:!0};function gp(e,t,n){var r,o={},i=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)mp.call(t,r)&&!vp.hasOwnProperty(r)&&(o[r]=t[r]);var l=arguments.length-2;if(l===1)o.children=n;else if(1<l){for(var a=Array(l),u=0;u<l;u++)a[u]=arguments[u+2];o.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)o[r]===void 0&&(o[r]=l[r]);return{$$typeof:pi,type:e,key:i,ref:s,props:o,_owner:_u.current}}function ey(e,t){return{$$typeof:pi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Au(e){return typeof e=="object"&&e!==null&&e.$$typeof===pi}function ty(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ed=/\/+/g;function Ol(e,t){return typeof e=="object"&&e!==null&&e.key!=null?ty(""+e.key):t.toString(36)}function Zi(e,t,n,r,o){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case pi:case Wg:s=!0}}if(s)return s=e,o=o(s),e=r===""?"."+Ol(s,0):r,Jc(o)?(n="",e!=null&&(n=e.replace(ed,"$&/")+"/"),Zi(o,t,n,"",function(u){return u})):o!=null&&(Au(o)&&(o=ey(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(ed,"$&/")+"/")+e)),t.push(o)),1;if(s=0,r=r===""?".":r+":",Jc(e))for(var l=0;l<e.length;l++){i=e[l];var a=r+Ol(i,l);s+=Zi(i,t,n,a,o)}else if(a=Jg(e),typeof a=="function")for(e=a.call(e),l=0;!(i=e.next()).done;)i=i.value,a=r+Ol(i,l++),s+=Zi(i,t,n,a,o);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Pi(e,t,n){if(e==null)return e;var r=[],o=0;return Zi(e,r,"","",function(i){return t.call(n,i,o++)}),r}function ny(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Fe={current:null},Ji={transition:null},ry={ReactCurrentDispatcher:Fe,ReactCurrentBatchConfig:Ji,ReactCurrentOwner:_u};function yp(){throw Error("act(...) is not supported in production builds of React.")}G.Children={map:Pi,forEach:function(e,t,n){Pi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Pi(e,function(){t++}),t},toArray:function(e){return Pi(e,function(t){return t})||[]},only:function(e){if(!Au(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};G.Component=ro;G.Fragment=Hg;G.Profiler=Qg;G.PureComponent=Ru;G.StrictMode=Vg;G.Suspense=Xg;G.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ry;G.act=yp;G.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=fp({},e.props),o=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=_u.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)mp.call(t,a)&&!vp.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var u=0;u<a;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:pi,type:e.type,key:o,ref:i,props:r,_owner:s}};G.createContext=function(e){return e={$$typeof:Gg,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Kg,_context:e},e.Consumer=e};G.createElement=gp;G.createFactory=function(e){var t=gp.bind(null,e);return t.type=e,t};G.createRef=function(){return{current:null}};G.forwardRef=function(e){return{$$typeof:Yg,render:e}};G.isValidElement=Au;G.lazy=function(e){return{$$typeof:Zg,_payload:{_status:-1,_result:e},_init:ny}};G.memo=function(e,t){return{$$typeof:qg,type:e,compare:t===void 0?null:t}};G.startTransition=function(e){var t=Ji.transition;Ji.transition={};try{e()}finally{Ji.transition=t}};G.unstable_act=yp;G.useCallback=function(e,t){return Fe.current.useCallback(e,t)};G.useContext=function(e){return Fe.current.useContext(e)};G.useDebugValue=function(){};G.useDeferredValue=function(e){return Fe.current.useDeferredValue(e)};G.useEffect=function(e,t){return Fe.current.useEffect(e,t)};G.useId=function(){return Fe.current.useId()};G.useImperativeHandle=function(e,t,n){return Fe.current.useImperativeHandle(e,t,n)};G.useInsertionEffect=function(e,t){return Fe.current.useInsertionEffect(e,t)};G.useLayoutEffect=function(e,t){return Fe.current.useLayoutEffect(e,t)};G.useMemo=function(e,t){return Fe.current.useMemo(e,t)};G.useReducer=function(e,t,n){return Fe.current.useReducer(e,t,n)};G.useRef=function(e){return Fe.current.useRef(e)};G.useState=function(e){return Fe.current.useState(e)};G.useSyncExternalStore=function(e,t,n){return Fe.current.useSyncExternalStore(e,t,n)};G.useTransition=function(){return Fe.current.useTransition()};G.version="18.3.1";cp.exports=G;var m=cp.exports;const A=ap(m),wp=Bg({__proto__:null,default:A},[m]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var oy=m,iy=Symbol.for("react.element"),sy=Symbol.for("react.fragment"),ly=Object.prototype.hasOwnProperty,ay=oy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,uy={key:!0,ref:!0,__self:!0,__source:!0};function xp(e,t,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)ly.call(t,r)&&!uy.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:iy,type:e,key:i,ref:s,props:o,_owner:ay.current}}Gs.Fragment=sy;Gs.jsx=xp;Gs.jsxs=xp;up.exports=Gs;var S=up.exports,Sp={exports:{}},tt={},Ep={exports:{}},Cp={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(k,_){var I=k.length;k.push(_);e:for(;0<I;){var F=I-1>>>1,$=k[F];if(0<o($,_))k[F]=_,k[I]=$,I=F;else break e}}function n(k){return k.length===0?null:k[0]}function r(k){if(k.length===0)return null;var _=k[0],I=k.pop();if(I!==_){k[0]=I;e:for(var F=0,$=k.length,Y=$>>>1;F<Y;){var ae=2*(F+1)-1,Qe=k[ae],Z=ae+1,dt=k[Z];if(0>o(Qe,I))Z<$&&0>o(dt,Qe)?(k[F]=dt,k[Z]=I,F=Z):(k[F]=Qe,k[ae]=I,F=ae);else if(Z<$&&0>o(dt,I))k[F]=dt,k[Z]=I,F=Z;else break e}}return _}function o(k,_){var I=k.sortIndex-_.sortIndex;return I!==0?I:k.id-_.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,l=s.now();e.unstable_now=function(){return s.now()-l}}var a=[],u=[],f=1,d=null,c=3,y=!1,x=!1,v=!1,w=typeof setTimeout=="function"?setTimeout:null,h=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(k){for(var _=n(u);_!==null;){if(_.callback===null)r(u);else if(_.startTime<=k)r(u),_.sortIndex=_.expirationTime,t(a,_);else break;_=n(u)}}function E(k){if(v=!1,g(k),!x)if(n(a)!==null)x=!0,U(C);else{var _=n(u);_!==null&&W(E,_.startTime-k)}}function C(k,_){x=!1,v&&(v=!1,h(T),T=-1),y=!0;var I=c;try{for(g(_),d=n(a);d!==null&&(!(d.expirationTime>_)||k&&!j());){var F=d.callback;if(typeof F=="function"){d.callback=null,c=d.priorityLevel;var $=F(d.expirationTime<=_);_=e.unstable_now(),typeof $=="function"?d.callback=$:d===n(a)&&r(a),g(_)}else r(a);d=n(a)}if(d!==null)var Y=!0;else{var ae=n(u);ae!==null&&W(E,ae.startTime-_),Y=!1}return Y}finally{d=null,c=I,y=!1}}var P=!1,b=null,T=-1,D=5,R=-1;function j(){return!(e.unstable_now()-R<D)}function L(){if(b!==null){var k=e.unstable_now();R=k;var _=!0;try{_=b(!0,k)}finally{_?B():(P=!1,b=null)}}else P=!1}var B;if(typeof p=="function")B=function(){p(L)};else if(typeof MessageChannel<"u"){var M=new MessageChannel,H=M.port2;M.port1.onmessage=L,B=function(){H.postMessage(null)}}else B=function(){w(L,0)};function U(k){b=k,P||(P=!0,B())}function W(k,_){T=w(function(){k(e.unstable_now())},_)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(k){k.callback=null},e.unstable_continueExecution=function(){x||y||(x=!0,U(C))},e.unstable_forceFrameRate=function(k){0>k||125<k?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):D=0<k?Math.floor(1e3/k):5},e.unstable_getCurrentPriorityLevel=function(){return c},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(k){switch(c){case 1:case 2:case 3:var _=3;break;default:_=c}var I=c;c=_;try{return k()}finally{c=I}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(k,_){switch(k){case 1:case 2:case 3:case 4:case 5:break;default:k=3}var I=c;c=k;try{return _()}finally{c=I}},e.unstable_scheduleCallback=function(k,_,I){var F=e.unstable_now();switch(typeof I=="object"&&I!==null?(I=I.delay,I=typeof I=="number"&&0<I?F+I:F):I=F,k){case 1:var $=-1;break;case 2:$=250;break;case 5:$=**********;break;case 4:$=1e4;break;default:$=5e3}return $=I+$,k={id:f++,callback:_,priorityLevel:k,startTime:I,expirationTime:$,sortIndex:-1},I>F?(k.sortIndex=I,t(u,k),n(a)===null&&k===n(u)&&(v?(h(T),T=-1):v=!0,W(E,I-F))):(k.sortIndex=$,t(a,k),x||y||(x=!0,U(C))),k},e.unstable_shouldYield=j,e.unstable_wrapCallback=function(k){var _=c;return function(){var I=c;c=_;try{return k.apply(this,arguments)}finally{c=I}}}})(Cp);Ep.exports=Cp;var cy=Ep.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var dy=m,et=cy;function O(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var kp=new Set,Bo={};function sr(e,t){Kr(e,t),Kr(e+"Capture",t)}function Kr(e,t){for(Bo[e]=t,e=0;e<t.length;e++)kp.add(t[e])}var Vt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),wa=Object.prototype.hasOwnProperty,fy=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,td={},nd={};function py(e){return wa.call(nd,e)?!0:wa.call(td,e)?!1:fy.test(e)?nd[e]=!0:(td[e]=!0,!1)}function hy(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function my(e,t,n,r){if(t===null||typeof t>"u"||hy(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ze(e,t,n,r,o,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var be={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){be[e]=new ze(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];be[t]=new ze(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){be[e]=new ze(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){be[e]=new ze(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){be[e]=new ze(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){be[e]=new ze(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){be[e]=new ze(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){be[e]=new ze(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){be[e]=new ze(e,5,!1,e.toLowerCase(),null,!1,!1)});var Mu=/[\-:]([a-z])/g;function ju(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Mu,ju);be[t]=new ze(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Mu,ju);be[t]=new ze(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Mu,ju);be[t]=new ze(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){be[e]=new ze(e,1,!1,e.toLowerCase(),null,!1,!1)});be.xlinkHref=new ze("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){be[e]=new ze(e,1,!1,e.toLowerCase(),null,!0,!0)});function Lu(e,t,n,r){var o=be.hasOwnProperty(t)?be[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(my(t,n,o,r)&&(n=null),r||o===null?py(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var qt=dy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,bi=Symbol.for("react.element"),gr=Symbol.for("react.portal"),yr=Symbol.for("react.fragment"),Iu=Symbol.for("react.strict_mode"),xa=Symbol.for("react.profiler"),Pp=Symbol.for("react.provider"),bp=Symbol.for("react.context"),Du=Symbol.for("react.forward_ref"),Sa=Symbol.for("react.suspense"),Ea=Symbol.for("react.suspense_list"),Fu=Symbol.for("react.memo"),un=Symbol.for("react.lazy"),Tp=Symbol.for("react.offscreen"),rd=Symbol.iterator;function ho(e){return e===null||typeof e!="object"?null:(e=rd&&e[rd]||e["@@iterator"],typeof e=="function"?e:null)}var de=Object.assign,_l;function ko(e){if(_l===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);_l=t&&t[1]||""}return`
`+_l+e}var Al=!1;function Ml(e,t){if(!e||Al)return"";Al=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,l=i.length-1;1<=s&&0<=l&&o[s]!==i[l];)l--;for(;1<=s&&0<=l;s--,l--)if(o[s]!==i[l]){if(s!==1||l!==1)do if(s--,l--,0>l||o[s]!==i[l]){var a=`
`+o[s].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=s&&0<=l);break}}}finally{Al=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?ko(e):""}function vy(e){switch(e.tag){case 5:return ko(e.type);case 16:return ko("Lazy");case 13:return ko("Suspense");case 19:return ko("SuspenseList");case 0:case 2:case 15:return e=Ml(e.type,!1),e;case 11:return e=Ml(e.type.render,!1),e;case 1:return e=Ml(e.type,!0),e;default:return""}}function Ca(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case yr:return"Fragment";case gr:return"Portal";case xa:return"Profiler";case Iu:return"StrictMode";case Sa:return"Suspense";case Ea:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case bp:return(e.displayName||"Context")+".Consumer";case Pp:return(e._context.displayName||"Context")+".Provider";case Du:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Fu:return t=e.displayName||null,t!==null?t:Ca(e.type)||"Memo";case un:t=e._payload,e=e._init;try{return Ca(e(t))}catch{}}return null}function gy(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ca(t);case 8:return t===Iu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function On(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Np(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function yy(e){var t=Np(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ti(e){e._valueTracker||(e._valueTracker=yy(e))}function Rp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Np(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function ms(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function ka(e,t){var n=t.checked;return de({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function od(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=On(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Op(e,t){t=t.checked,t!=null&&Lu(e,"checked",t,!1)}function Pa(e,t){Op(e,t);var n=On(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?ba(e,t.type,n):t.hasOwnProperty("defaultValue")&&ba(e,t.type,On(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function id(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function ba(e,t,n){(t!=="number"||ms(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Po=Array.isArray;function Rr(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+On(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Ta(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(O(91));return de({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function sd(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(O(92));if(Po(n)){if(1<n.length)throw Error(O(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:On(n)}}function _p(e,t){var n=On(t.value),r=On(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function ld(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Ap(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Na(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Ap(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ni,Mp=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ni=Ni||document.createElement("div"),Ni.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ni.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Wo(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Ao={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},wy=["Webkit","ms","Moz","O"];Object.keys(Ao).forEach(function(e){wy.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Ao[t]=Ao[e]})});function jp(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Ao.hasOwnProperty(e)&&Ao[e]?(""+t).trim():t+"px"}function Lp(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=jp(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var xy=de({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ra(e,t){if(t){if(xy[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(O(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(O(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(O(61))}if(t.style!=null&&typeof t.style!="object")throw Error(O(62))}}function Oa(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var _a=null;function zu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Aa=null,Or=null,_r=null;function ad(e){if(e=vi(e)){if(typeof Aa!="function")throw Error(O(280));var t=e.stateNode;t&&(t=Js(t),Aa(e.stateNode,e.type,t))}}function Ip(e){Or?_r?_r.push(e):_r=[e]:Or=e}function Dp(){if(Or){var e=Or,t=_r;if(_r=Or=null,ad(e),t)for(e=0;e<t.length;e++)ad(t[e])}}function Fp(e,t){return e(t)}function zp(){}var jl=!1;function $p(e,t,n){if(jl)return e(t,n);jl=!0;try{return Fp(e,t,n)}finally{jl=!1,(Or!==null||_r!==null)&&(zp(),Dp())}}function Ho(e,t){var n=e.stateNode;if(n===null)return null;var r=Js(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(O(231,t,typeof n));return n}var Ma=!1;if(Vt)try{var mo={};Object.defineProperty(mo,"passive",{get:function(){Ma=!0}}),window.addEventListener("test",mo,mo),window.removeEventListener("test",mo,mo)}catch{Ma=!1}function Sy(e,t,n,r,o,i,s,l,a){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(f){this.onError(f)}}var Mo=!1,vs=null,gs=!1,ja=null,Ey={onError:function(e){Mo=!0,vs=e}};function Cy(e,t,n,r,o,i,s,l,a){Mo=!1,vs=null,Sy.apply(Ey,arguments)}function ky(e,t,n,r,o,i,s,l,a){if(Cy.apply(this,arguments),Mo){if(Mo){var u=vs;Mo=!1,vs=null}else throw Error(O(198));gs||(gs=!0,ja=u)}}function lr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Up(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function ud(e){if(lr(e)!==e)throw Error(O(188))}function Py(e){var t=e.alternate;if(!t){if(t=lr(e),t===null)throw Error(O(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return ud(o),e;if(i===r)return ud(o),t;i=i.sibling}throw Error(O(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s){for(l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s)throw Error(O(189))}}if(n.alternate!==r)throw Error(O(190))}if(n.tag!==3)throw Error(O(188));return n.stateNode.current===n?e:t}function Bp(e){return e=Py(e),e!==null?Wp(e):null}function Wp(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Wp(e);if(t!==null)return t;e=e.sibling}return null}var Hp=et.unstable_scheduleCallback,cd=et.unstable_cancelCallback,by=et.unstable_shouldYield,Ty=et.unstable_requestPaint,he=et.unstable_now,Ny=et.unstable_getCurrentPriorityLevel,$u=et.unstable_ImmediatePriority,Vp=et.unstable_UserBlockingPriority,ys=et.unstable_NormalPriority,Ry=et.unstable_LowPriority,Qp=et.unstable_IdlePriority,Ys=null,jt=null;function Oy(e){if(jt&&typeof jt.onCommitFiberRoot=="function")try{jt.onCommitFiberRoot(Ys,e,void 0,(e.current.flags&128)===128)}catch{}}var wt=Math.clz32?Math.clz32:My,_y=Math.log,Ay=Math.LN2;function My(e){return e>>>=0,e===0?32:31-(_y(e)/Ay|0)|0}var Ri=64,Oi=4194304;function bo(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ws(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var l=s&~o;l!==0?r=bo(l):(i&=s,i!==0&&(r=bo(i)))}else s=n&~o,s!==0?r=bo(s):i!==0&&(r=bo(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,i=t&-t,o>=i||o===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-wt(t),o=1<<n,r|=e[n],t&=~o;return r}function jy(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Ly(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-wt(i),l=1<<s,a=o[s];a===-1?(!(l&n)||l&r)&&(o[s]=jy(l,t)):a<=t&&(e.expiredLanes|=l),i&=~l}}function La(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Kp(){var e=Ri;return Ri<<=1,!(Ri&4194240)&&(Ri=64),e}function Ll(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function hi(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-wt(t),e[t]=n}function Iy(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-wt(n),i=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~i}}function Uu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-wt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var J=0;function Gp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Yp,Bu,Xp,qp,Zp,Ia=!1,_i=[],En=null,Cn=null,kn=null,Vo=new Map,Qo=new Map,dn=[],Dy="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function dd(e,t){switch(e){case"focusin":case"focusout":En=null;break;case"dragenter":case"dragleave":Cn=null;break;case"mouseover":case"mouseout":kn=null;break;case"pointerover":case"pointerout":Vo.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Qo.delete(t.pointerId)}}function vo(e,t,n,r,o,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},t!==null&&(t=vi(t),t!==null&&Bu(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Fy(e,t,n,r,o){switch(t){case"focusin":return En=vo(En,e,t,n,r,o),!0;case"dragenter":return Cn=vo(Cn,e,t,n,r,o),!0;case"mouseover":return kn=vo(kn,e,t,n,r,o),!0;case"pointerover":var i=o.pointerId;return Vo.set(i,vo(Vo.get(i)||null,e,t,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,Qo.set(i,vo(Qo.get(i)||null,e,t,n,r,o)),!0}return!1}function Jp(e){var t=Wn(e.target);if(t!==null){var n=lr(t);if(n!==null){if(t=n.tag,t===13){if(t=Up(n),t!==null){e.blockedOn=t,Zp(e.priority,function(){Xp(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function es(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Da(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);_a=r,n.target.dispatchEvent(r),_a=null}else return t=vi(n),t!==null&&Bu(t),e.blockedOn=n,!1;t.shift()}return!0}function fd(e,t,n){es(e)&&n.delete(t)}function zy(){Ia=!1,En!==null&&es(En)&&(En=null),Cn!==null&&es(Cn)&&(Cn=null),kn!==null&&es(kn)&&(kn=null),Vo.forEach(fd),Qo.forEach(fd)}function go(e,t){e.blockedOn===t&&(e.blockedOn=null,Ia||(Ia=!0,et.unstable_scheduleCallback(et.unstable_NormalPriority,zy)))}function Ko(e){function t(o){return go(o,e)}if(0<_i.length){go(_i[0],e);for(var n=1;n<_i.length;n++){var r=_i[n];r.blockedOn===e&&(r.blockedOn=null)}}for(En!==null&&go(En,e),Cn!==null&&go(Cn,e),kn!==null&&go(kn,e),Vo.forEach(t),Qo.forEach(t),n=0;n<dn.length;n++)r=dn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<dn.length&&(n=dn[0],n.blockedOn===null);)Jp(n),n.blockedOn===null&&dn.shift()}var Ar=qt.ReactCurrentBatchConfig,xs=!0;function $y(e,t,n,r){var o=J,i=Ar.transition;Ar.transition=null;try{J=1,Wu(e,t,n,r)}finally{J=o,Ar.transition=i}}function Uy(e,t,n,r){var o=J,i=Ar.transition;Ar.transition=null;try{J=4,Wu(e,t,n,r)}finally{J=o,Ar.transition=i}}function Wu(e,t,n,r){if(xs){var o=Da(e,t,n,r);if(o===null)Vl(e,t,r,Ss,n),dd(e,r);else if(Fy(o,e,t,n,r))r.stopPropagation();else if(dd(e,r),t&4&&-1<Dy.indexOf(e)){for(;o!==null;){var i=vi(o);if(i!==null&&Yp(i),i=Da(e,t,n,r),i===null&&Vl(e,t,r,Ss,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Vl(e,t,r,null,n)}}var Ss=null;function Da(e,t,n,r){if(Ss=null,e=zu(r),e=Wn(e),e!==null)if(t=lr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Up(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ss=e,null}function eh(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ny()){case $u:return 1;case Vp:return 4;case ys:case Ry:return 16;case Qp:return 536870912;default:return 16}default:return 16}}var yn=null,Hu=null,ts=null;function th(){if(ts)return ts;var e,t=Hu,n=t.length,r,o="value"in yn?yn.value:yn.textContent,i=o.length;for(e=0;e<n&&t[e]===o[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===o[i-r];r++);return ts=o.slice(e,1<r?1-r:void 0)}function ns(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ai(){return!0}function pd(){return!1}function nt(e){function t(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(i):i[l]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Ai:pd,this.isPropagationStopped=pd,this}return de(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ai)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ai)},persist:function(){},isPersistent:Ai}),t}var oo={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Vu=nt(oo),mi=de({},oo,{view:0,detail:0}),By=nt(mi),Il,Dl,yo,Xs=de({},mi,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Qu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==yo&&(yo&&e.type==="mousemove"?(Il=e.screenX-yo.screenX,Dl=e.screenY-yo.screenY):Dl=Il=0,yo=e),Il)},movementY:function(e){return"movementY"in e?e.movementY:Dl}}),hd=nt(Xs),Wy=de({},Xs,{dataTransfer:0}),Hy=nt(Wy),Vy=de({},mi,{relatedTarget:0}),Fl=nt(Vy),Qy=de({},oo,{animationName:0,elapsedTime:0,pseudoElement:0}),Ky=nt(Qy),Gy=de({},oo,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Yy=nt(Gy),Xy=de({},oo,{data:0}),md=nt(Xy),qy={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Zy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Jy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function e0(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Jy[e])?!!t[e]:!1}function Qu(){return e0}var t0=de({},mi,{key:function(e){if(e.key){var t=qy[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ns(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Zy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Qu,charCode:function(e){return e.type==="keypress"?ns(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ns(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),n0=nt(t0),r0=de({},Xs,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),vd=nt(r0),o0=de({},mi,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Qu}),i0=nt(o0),s0=de({},oo,{propertyName:0,elapsedTime:0,pseudoElement:0}),l0=nt(s0),a0=de({},Xs,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),u0=nt(a0),c0=[9,13,27,32],Ku=Vt&&"CompositionEvent"in window,jo=null;Vt&&"documentMode"in document&&(jo=document.documentMode);var d0=Vt&&"TextEvent"in window&&!jo,nh=Vt&&(!Ku||jo&&8<jo&&11>=jo),gd=" ",yd=!1;function rh(e,t){switch(e){case"keyup":return c0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function oh(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var wr=!1;function f0(e,t){switch(e){case"compositionend":return oh(t);case"keypress":return t.which!==32?null:(yd=!0,gd);case"textInput":return e=t.data,e===gd&&yd?null:e;default:return null}}function p0(e,t){if(wr)return e==="compositionend"||!Ku&&rh(e,t)?(e=th(),ts=Hu=yn=null,wr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return nh&&t.locale!=="ko"?null:t.data;default:return null}}var h0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function wd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!h0[e.type]:t==="textarea"}function ih(e,t,n,r){Ip(r),t=Es(t,"onChange"),0<t.length&&(n=new Vu("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Lo=null,Go=null;function m0(e){vh(e,0)}function qs(e){var t=Er(e);if(Rp(t))return e}function v0(e,t){if(e==="change")return t}var sh=!1;if(Vt){var zl;if(Vt){var $l="oninput"in document;if(!$l){var xd=document.createElement("div");xd.setAttribute("oninput","return;"),$l=typeof xd.oninput=="function"}zl=$l}else zl=!1;sh=zl&&(!document.documentMode||9<document.documentMode)}function Sd(){Lo&&(Lo.detachEvent("onpropertychange",lh),Go=Lo=null)}function lh(e){if(e.propertyName==="value"&&qs(Go)){var t=[];ih(t,Go,e,zu(e)),$p(m0,t)}}function g0(e,t,n){e==="focusin"?(Sd(),Lo=t,Go=n,Lo.attachEvent("onpropertychange",lh)):e==="focusout"&&Sd()}function y0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return qs(Go)}function w0(e,t){if(e==="click")return qs(t)}function x0(e,t){if(e==="input"||e==="change")return qs(t)}function S0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var St=typeof Object.is=="function"?Object.is:S0;function Yo(e,t){if(St(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!wa.call(t,o)||!St(e[o],t[o]))return!1}return!0}function Ed(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Cd(e,t){var n=Ed(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Ed(n)}}function ah(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ah(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function uh(){for(var e=window,t=ms();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=ms(e.document)}return t}function Gu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function E0(e){var t=uh(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ah(n.ownerDocument.documentElement,n)){if(r!==null&&Gu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!e.extend&&i>r&&(o=r,r=i,i=o),o=Cd(n,i);var s=Cd(n,r);o&&s&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var C0=Vt&&"documentMode"in document&&11>=document.documentMode,xr=null,Fa=null,Io=null,za=!1;function kd(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;za||xr==null||xr!==ms(r)||(r=xr,"selectionStart"in r&&Gu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Io&&Yo(Io,r)||(Io=r,r=Es(Fa,"onSelect"),0<r.length&&(t=new Vu("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=xr)))}function Mi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Sr={animationend:Mi("Animation","AnimationEnd"),animationiteration:Mi("Animation","AnimationIteration"),animationstart:Mi("Animation","AnimationStart"),transitionend:Mi("Transition","TransitionEnd")},Ul={},ch={};Vt&&(ch=document.createElement("div").style,"AnimationEvent"in window||(delete Sr.animationend.animation,delete Sr.animationiteration.animation,delete Sr.animationstart.animation),"TransitionEvent"in window||delete Sr.transitionend.transition);function Zs(e){if(Ul[e])return Ul[e];if(!Sr[e])return e;var t=Sr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in ch)return Ul[e]=t[n];return e}var dh=Zs("animationend"),fh=Zs("animationiteration"),ph=Zs("animationstart"),hh=Zs("transitionend"),mh=new Map,Pd="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Dn(e,t){mh.set(e,t),sr(t,[e])}for(var Bl=0;Bl<Pd.length;Bl++){var Wl=Pd[Bl],k0=Wl.toLowerCase(),P0=Wl[0].toUpperCase()+Wl.slice(1);Dn(k0,"on"+P0)}Dn(dh,"onAnimationEnd");Dn(fh,"onAnimationIteration");Dn(ph,"onAnimationStart");Dn("dblclick","onDoubleClick");Dn("focusin","onFocus");Dn("focusout","onBlur");Dn(hh,"onTransitionEnd");Kr("onMouseEnter",["mouseout","mouseover"]);Kr("onMouseLeave",["mouseout","mouseover"]);Kr("onPointerEnter",["pointerout","pointerover"]);Kr("onPointerLeave",["pointerout","pointerover"]);sr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));sr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));sr("onBeforeInput",["compositionend","keypress","textInput","paste"]);sr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));sr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));sr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var To="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),b0=new Set("cancel close invalid load scroll toggle".split(" ").concat(To));function bd(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,ky(r,t,void 0,e),e.currentTarget=null}function vh(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var s=r.length-1;0<=s;s--){var l=r[s],a=l.instance,u=l.currentTarget;if(l=l.listener,a!==i&&o.isPropagationStopped())break e;bd(o,l,u),i=a}else for(s=0;s<r.length;s++){if(l=r[s],a=l.instance,u=l.currentTarget,l=l.listener,a!==i&&o.isPropagationStopped())break e;bd(o,l,u),i=a}}}if(gs)throw e=ja,gs=!1,ja=null,e}function oe(e,t){var n=t[Ha];n===void 0&&(n=t[Ha]=new Set);var r=e+"__bubble";n.has(r)||(gh(t,e,2,!1),n.add(r))}function Hl(e,t,n){var r=0;t&&(r|=4),gh(n,e,r,t)}var ji="_reactListening"+Math.random().toString(36).slice(2);function Xo(e){if(!e[ji]){e[ji]=!0,kp.forEach(function(n){n!=="selectionchange"&&(b0.has(n)||Hl(n,!1,e),Hl(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ji]||(t[ji]=!0,Hl("selectionchange",!1,t))}}function gh(e,t,n,r){switch(eh(t)){case 1:var o=$y;break;case 4:o=Uy;break;default:o=Wu}n=o.bind(null,t,n,e),o=void 0,!Ma||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Vl(e,t,n,r,o){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var l=r.stateNode.containerInfo;if(l===o||l.nodeType===8&&l.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var a=s.tag;if((a===3||a===4)&&(a=s.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;s=s.return}for(;l!==null;){if(s=Wn(l),s===null)return;if(a=s.tag,a===5||a===6){r=i=s;continue e}l=l.parentNode}}r=r.return}$p(function(){var u=i,f=zu(n),d=[];e:{var c=mh.get(e);if(c!==void 0){var y=Vu,x=e;switch(e){case"keypress":if(ns(n)===0)break e;case"keydown":case"keyup":y=n0;break;case"focusin":x="focus",y=Fl;break;case"focusout":x="blur",y=Fl;break;case"beforeblur":case"afterblur":y=Fl;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=hd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=Hy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=i0;break;case dh:case fh:case ph:y=Ky;break;case hh:y=l0;break;case"scroll":y=By;break;case"wheel":y=u0;break;case"copy":case"cut":case"paste":y=Yy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=vd}var v=(t&4)!==0,w=!v&&e==="scroll",h=v?c!==null?c+"Capture":null:c;v=[];for(var p=u,g;p!==null;){g=p;var E=g.stateNode;if(g.tag===5&&E!==null&&(g=E,h!==null&&(E=Ho(p,h),E!=null&&v.push(qo(p,E,g)))),w)break;p=p.return}0<v.length&&(c=new y(c,x,null,n,f),d.push({event:c,listeners:v}))}}if(!(t&7)){e:{if(c=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",c&&n!==_a&&(x=n.relatedTarget||n.fromElement)&&(Wn(x)||x[Qt]))break e;if((y||c)&&(c=f.window===f?f:(c=f.ownerDocument)?c.defaultView||c.parentWindow:window,y?(x=n.relatedTarget||n.toElement,y=u,x=x?Wn(x):null,x!==null&&(w=lr(x),x!==w||x.tag!==5&&x.tag!==6)&&(x=null)):(y=null,x=u),y!==x)){if(v=hd,E="onMouseLeave",h="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(v=vd,E="onPointerLeave",h="onPointerEnter",p="pointer"),w=y==null?c:Er(y),g=x==null?c:Er(x),c=new v(E,p+"leave",y,n,f),c.target=w,c.relatedTarget=g,E=null,Wn(f)===u&&(v=new v(h,p+"enter",x,n,f),v.target=g,v.relatedTarget=w,E=v),w=E,y&&x)t:{for(v=y,h=x,p=0,g=v;g;g=pr(g))p++;for(g=0,E=h;E;E=pr(E))g++;for(;0<p-g;)v=pr(v),p--;for(;0<g-p;)h=pr(h),g--;for(;p--;){if(v===h||h!==null&&v===h.alternate)break t;v=pr(v),h=pr(h)}v=null}else v=null;y!==null&&Td(d,c,y,v,!1),x!==null&&w!==null&&Td(d,w,x,v,!0)}}e:{if(c=u?Er(u):window,y=c.nodeName&&c.nodeName.toLowerCase(),y==="select"||y==="input"&&c.type==="file")var C=v0;else if(wd(c))if(sh)C=x0;else{C=y0;var P=g0}else(y=c.nodeName)&&y.toLowerCase()==="input"&&(c.type==="checkbox"||c.type==="radio")&&(C=w0);if(C&&(C=C(e,u))){ih(d,C,n,f);break e}P&&P(e,c,u),e==="focusout"&&(P=c._wrapperState)&&P.controlled&&c.type==="number"&&ba(c,"number",c.value)}switch(P=u?Er(u):window,e){case"focusin":(wd(P)||P.contentEditable==="true")&&(xr=P,Fa=u,Io=null);break;case"focusout":Io=Fa=xr=null;break;case"mousedown":za=!0;break;case"contextmenu":case"mouseup":case"dragend":za=!1,kd(d,n,f);break;case"selectionchange":if(C0)break;case"keydown":case"keyup":kd(d,n,f)}var b;if(Ku)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else wr?rh(e,n)&&(T="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(nh&&n.locale!=="ko"&&(wr||T!=="onCompositionStart"?T==="onCompositionEnd"&&wr&&(b=th()):(yn=f,Hu="value"in yn?yn.value:yn.textContent,wr=!0)),P=Es(u,T),0<P.length&&(T=new md(T,e,null,n,f),d.push({event:T,listeners:P}),b?T.data=b:(b=oh(n),b!==null&&(T.data=b)))),(b=d0?f0(e,n):p0(e,n))&&(u=Es(u,"onBeforeInput"),0<u.length&&(f=new md("onBeforeInput","beforeinput",null,n,f),d.push({event:f,listeners:u}),f.data=b))}vh(d,t)})}function qo(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Es(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=Ho(e,n),i!=null&&r.unshift(qo(e,i,o)),i=Ho(e,t),i!=null&&r.push(qo(e,i,o))),e=e.return}return r}function pr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Td(e,t,n,r,o){for(var i=t._reactName,s=[];n!==null&&n!==r;){var l=n,a=l.alternate,u=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&u!==null&&(l=u,o?(a=Ho(n,i),a!=null&&s.unshift(qo(n,a,l))):o||(a=Ho(n,i),a!=null&&s.push(qo(n,a,l)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var T0=/\r\n?/g,N0=/\u0000|\uFFFD/g;function Nd(e){return(typeof e=="string"?e:""+e).replace(T0,`
`).replace(N0,"")}function Li(e,t,n){if(t=Nd(t),Nd(e)!==t&&n)throw Error(O(425))}function Cs(){}var $a=null,Ua=null;function Ba(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Wa=typeof setTimeout=="function"?setTimeout:void 0,R0=typeof clearTimeout=="function"?clearTimeout:void 0,Rd=typeof Promise=="function"?Promise:void 0,O0=typeof queueMicrotask=="function"?queueMicrotask:typeof Rd<"u"?function(e){return Rd.resolve(null).then(e).catch(_0)}:Wa;function _0(e){setTimeout(function(){throw e})}function Ql(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Ko(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Ko(t)}function Pn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Od(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var io=Math.random().toString(36).slice(2),At="__reactFiber$"+io,Zo="__reactProps$"+io,Qt="__reactContainer$"+io,Ha="__reactEvents$"+io,A0="__reactListeners$"+io,M0="__reactHandles$"+io;function Wn(e){var t=e[At];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Qt]||n[At]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Od(e);e!==null;){if(n=e[At])return n;e=Od(e)}return t}e=n,n=e.parentNode}return null}function vi(e){return e=e[At]||e[Qt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Er(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(O(33))}function Js(e){return e[Zo]||null}var Va=[],Cr=-1;function Fn(e){return{current:e}}function ie(e){0>Cr||(e.current=Va[Cr],Va[Cr]=null,Cr--)}function te(e,t){Cr++,Va[Cr]=e.current,e.current=t}var _n={},Ae=Fn(_n),We=Fn(!1),Jn=_n;function Gr(e,t){var n=e.type.contextTypes;if(!n)return _n;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function He(e){return e=e.childContextTypes,e!=null}function ks(){ie(We),ie(Ae)}function _d(e,t,n){if(Ae.current!==_n)throw Error(O(168));te(Ae,t),te(We,n)}function yh(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(O(108,gy(e)||"Unknown",o));return de({},n,r)}function Ps(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||_n,Jn=Ae.current,te(Ae,e),te(We,We.current),!0}function Ad(e,t,n){var r=e.stateNode;if(!r)throw Error(O(169));n?(e=yh(e,t,Jn),r.__reactInternalMemoizedMergedChildContext=e,ie(We),ie(Ae),te(Ae,e)):ie(We),te(We,n)}var Ut=null,el=!1,Kl=!1;function wh(e){Ut===null?Ut=[e]:Ut.push(e)}function j0(e){el=!0,wh(e)}function zn(){if(!Kl&&Ut!==null){Kl=!0;var e=0,t=J;try{var n=Ut;for(J=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Ut=null,el=!1}catch(o){throw Ut!==null&&(Ut=Ut.slice(e+1)),Hp($u,zn),o}finally{J=t,Kl=!1}}return null}var kr=[],Pr=0,bs=null,Ts=0,ot=[],it=0,er=null,Bt=1,Wt="";function Un(e,t){kr[Pr++]=Ts,kr[Pr++]=bs,bs=e,Ts=t}function xh(e,t,n){ot[it++]=Bt,ot[it++]=Wt,ot[it++]=er,er=e;var r=Bt;e=Wt;var o=32-wt(r)-1;r&=~(1<<o),n+=1;var i=32-wt(t)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,Bt=1<<32-wt(t)+o|n<<o|r,Wt=i+e}else Bt=1<<i|n<<o|r,Wt=e}function Yu(e){e.return!==null&&(Un(e,1),xh(e,1,0))}function Xu(e){for(;e===bs;)bs=kr[--Pr],kr[Pr]=null,Ts=kr[--Pr],kr[Pr]=null;for(;e===er;)er=ot[--it],ot[it]=null,Wt=ot[--it],ot[it]=null,Bt=ot[--it],ot[it]=null}var Ze=null,qe=null,se=!1,yt=null;function Sh(e,t){var n=st(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Md(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ze=e,qe=Pn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ze=e,qe=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=er!==null?{id:Bt,overflow:Wt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=st(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ze=e,qe=null,!0):!1;default:return!1}}function Qa(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Ka(e){if(se){var t=qe;if(t){var n=t;if(!Md(e,t)){if(Qa(e))throw Error(O(418));t=Pn(n.nextSibling);var r=Ze;t&&Md(e,t)?Sh(r,n):(e.flags=e.flags&-4097|2,se=!1,Ze=e)}}else{if(Qa(e))throw Error(O(418));e.flags=e.flags&-4097|2,se=!1,Ze=e}}}function jd(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ze=e}function Ii(e){if(e!==Ze)return!1;if(!se)return jd(e),se=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ba(e.type,e.memoizedProps)),t&&(t=qe)){if(Qa(e))throw Eh(),Error(O(418));for(;t;)Sh(e,t),t=Pn(t.nextSibling)}if(jd(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(O(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){qe=Pn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}qe=null}}else qe=Ze?Pn(e.stateNode.nextSibling):null;return!0}function Eh(){for(var e=qe;e;)e=Pn(e.nextSibling)}function Yr(){qe=Ze=null,se=!1}function qu(e){yt===null?yt=[e]:yt.push(e)}var L0=qt.ReactCurrentBatchConfig;function wo(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(O(309));var r=n.stateNode}if(!r)throw Error(O(147,e));var o=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var l=o.refs;s===null?delete l[i]:l[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(O(284));if(!n._owner)throw Error(O(290,e))}return e}function Di(e,t){throw e=Object.prototype.toString.call(t),Error(O(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Ld(e){var t=e._init;return t(e._payload)}function Ch(e){function t(h,p){if(e){var g=h.deletions;g===null?(h.deletions=[p],h.flags|=16):g.push(p)}}function n(h,p){if(!e)return null;for(;p!==null;)t(h,p),p=p.sibling;return null}function r(h,p){for(h=new Map;p!==null;)p.key!==null?h.set(p.key,p):h.set(p.index,p),p=p.sibling;return h}function o(h,p){return h=Rn(h,p),h.index=0,h.sibling=null,h}function i(h,p,g){return h.index=g,e?(g=h.alternate,g!==null?(g=g.index,g<p?(h.flags|=2,p):g):(h.flags|=2,p)):(h.flags|=1048576,p)}function s(h){return e&&h.alternate===null&&(h.flags|=2),h}function l(h,p,g,E){return p===null||p.tag!==6?(p=ea(g,h.mode,E),p.return=h,p):(p=o(p,g),p.return=h,p)}function a(h,p,g,E){var C=g.type;return C===yr?f(h,p,g.props.children,E,g.key):p!==null&&(p.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===un&&Ld(C)===p.type)?(E=o(p,g.props),E.ref=wo(h,p,g),E.return=h,E):(E=us(g.type,g.key,g.props,null,h.mode,E),E.ref=wo(h,p,g),E.return=h,E)}function u(h,p,g,E){return p===null||p.tag!==4||p.stateNode.containerInfo!==g.containerInfo||p.stateNode.implementation!==g.implementation?(p=ta(g,h.mode,E),p.return=h,p):(p=o(p,g.children||[]),p.return=h,p)}function f(h,p,g,E,C){return p===null||p.tag!==7?(p=qn(g,h.mode,E,C),p.return=h,p):(p=o(p,g),p.return=h,p)}function d(h,p,g){if(typeof p=="string"&&p!==""||typeof p=="number")return p=ea(""+p,h.mode,g),p.return=h,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case bi:return g=us(p.type,p.key,p.props,null,h.mode,g),g.ref=wo(h,null,p),g.return=h,g;case gr:return p=ta(p,h.mode,g),p.return=h,p;case un:var E=p._init;return d(h,E(p._payload),g)}if(Po(p)||ho(p))return p=qn(p,h.mode,g,null),p.return=h,p;Di(h,p)}return null}function c(h,p,g,E){var C=p!==null?p.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return C!==null?null:l(h,p,""+g,E);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case bi:return g.key===C?a(h,p,g,E):null;case gr:return g.key===C?u(h,p,g,E):null;case un:return C=g._init,c(h,p,C(g._payload),E)}if(Po(g)||ho(g))return C!==null?null:f(h,p,g,E,null);Di(h,g)}return null}function y(h,p,g,E,C){if(typeof E=="string"&&E!==""||typeof E=="number")return h=h.get(g)||null,l(p,h,""+E,C);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case bi:return h=h.get(E.key===null?g:E.key)||null,a(p,h,E,C);case gr:return h=h.get(E.key===null?g:E.key)||null,u(p,h,E,C);case un:var P=E._init;return y(h,p,g,P(E._payload),C)}if(Po(E)||ho(E))return h=h.get(g)||null,f(p,h,E,C,null);Di(p,E)}return null}function x(h,p,g,E){for(var C=null,P=null,b=p,T=p=0,D=null;b!==null&&T<g.length;T++){b.index>T?(D=b,b=null):D=b.sibling;var R=c(h,b,g[T],E);if(R===null){b===null&&(b=D);break}e&&b&&R.alternate===null&&t(h,b),p=i(R,p,T),P===null?C=R:P.sibling=R,P=R,b=D}if(T===g.length)return n(h,b),se&&Un(h,T),C;if(b===null){for(;T<g.length;T++)b=d(h,g[T],E),b!==null&&(p=i(b,p,T),P===null?C=b:P.sibling=b,P=b);return se&&Un(h,T),C}for(b=r(h,b);T<g.length;T++)D=y(b,h,T,g[T],E),D!==null&&(e&&D.alternate!==null&&b.delete(D.key===null?T:D.key),p=i(D,p,T),P===null?C=D:P.sibling=D,P=D);return e&&b.forEach(function(j){return t(h,j)}),se&&Un(h,T),C}function v(h,p,g,E){var C=ho(g);if(typeof C!="function")throw Error(O(150));if(g=C.call(g),g==null)throw Error(O(151));for(var P=C=null,b=p,T=p=0,D=null,R=g.next();b!==null&&!R.done;T++,R=g.next()){b.index>T?(D=b,b=null):D=b.sibling;var j=c(h,b,R.value,E);if(j===null){b===null&&(b=D);break}e&&b&&j.alternate===null&&t(h,b),p=i(j,p,T),P===null?C=j:P.sibling=j,P=j,b=D}if(R.done)return n(h,b),se&&Un(h,T),C;if(b===null){for(;!R.done;T++,R=g.next())R=d(h,R.value,E),R!==null&&(p=i(R,p,T),P===null?C=R:P.sibling=R,P=R);return se&&Un(h,T),C}for(b=r(h,b);!R.done;T++,R=g.next())R=y(b,h,T,R.value,E),R!==null&&(e&&R.alternate!==null&&b.delete(R.key===null?T:R.key),p=i(R,p,T),P===null?C=R:P.sibling=R,P=R);return e&&b.forEach(function(L){return t(h,L)}),se&&Un(h,T),C}function w(h,p,g,E){if(typeof g=="object"&&g!==null&&g.type===yr&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case bi:e:{for(var C=g.key,P=p;P!==null;){if(P.key===C){if(C=g.type,C===yr){if(P.tag===7){n(h,P.sibling),p=o(P,g.props.children),p.return=h,h=p;break e}}else if(P.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===un&&Ld(C)===P.type){n(h,P.sibling),p=o(P,g.props),p.ref=wo(h,P,g),p.return=h,h=p;break e}n(h,P);break}else t(h,P);P=P.sibling}g.type===yr?(p=qn(g.props.children,h.mode,E,g.key),p.return=h,h=p):(E=us(g.type,g.key,g.props,null,h.mode,E),E.ref=wo(h,p,g),E.return=h,h=E)}return s(h);case gr:e:{for(P=g.key;p!==null;){if(p.key===P)if(p.tag===4&&p.stateNode.containerInfo===g.containerInfo&&p.stateNode.implementation===g.implementation){n(h,p.sibling),p=o(p,g.children||[]),p.return=h,h=p;break e}else{n(h,p);break}else t(h,p);p=p.sibling}p=ta(g,h.mode,E),p.return=h,h=p}return s(h);case un:return P=g._init,w(h,p,P(g._payload),E)}if(Po(g))return x(h,p,g,E);if(ho(g))return v(h,p,g,E);Di(h,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,p!==null&&p.tag===6?(n(h,p.sibling),p=o(p,g),p.return=h,h=p):(n(h,p),p=ea(g,h.mode,E),p.return=h,h=p),s(h)):n(h,p)}return w}var Xr=Ch(!0),kh=Ch(!1),Ns=Fn(null),Rs=null,br=null,Zu=null;function Ju(){Zu=br=Rs=null}function ec(e){var t=Ns.current;ie(Ns),e._currentValue=t}function Ga(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Mr(e,t){Rs=e,Zu=br=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Be=!0),e.firstContext=null)}function at(e){var t=e._currentValue;if(Zu!==e)if(e={context:e,memoizedValue:t,next:null},br===null){if(Rs===null)throw Error(O(308));br=e,Rs.dependencies={lanes:0,firstContext:e}}else br=br.next=e;return t}var Hn=null;function tc(e){Hn===null?Hn=[e]:Hn.push(e)}function Ph(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,tc(t)):(n.next=o.next,o.next=n),t.interleaved=n,Kt(e,r)}function Kt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var cn=!1;function nc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function bh(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ht(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function bn(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,X&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Kt(e,n)}return o=r.interleaved,o===null?(t.next=t,tc(r)):(t.next=o.next,o.next=t),r.interleaved=t,Kt(e,n)}function rs(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Uu(e,n)}}function Id(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=t:i=i.next=t}else o=i=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Os(e,t,n,r){var o=e.updateQueue;cn=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,l=o.shared.pending;if(l!==null){o.shared.pending=null;var a=l,u=a.next;a.next=null,s===null?i=u:s.next=u,s=a;var f=e.alternate;f!==null&&(f=f.updateQueue,l=f.lastBaseUpdate,l!==s&&(l===null?f.firstBaseUpdate=u:l.next=u,f.lastBaseUpdate=a))}if(i!==null){var d=o.baseState;s=0,f=u=a=null,l=i;do{var c=l.lane,y=l.eventTime;if((r&c)===c){f!==null&&(f=f.next={eventTime:y,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var x=e,v=l;switch(c=t,y=n,v.tag){case 1:if(x=v.payload,typeof x=="function"){d=x.call(y,d,c);break e}d=x;break e;case 3:x.flags=x.flags&-65537|128;case 0:if(x=v.payload,c=typeof x=="function"?x.call(y,d,c):x,c==null)break e;d=de({},d,c);break e;case 2:cn=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,c=o.effects,c===null?o.effects=[l]:c.push(l))}else y={eventTime:y,lane:c,tag:l.tag,payload:l.payload,callback:l.callback,next:null},f===null?(u=f=y,a=d):f=f.next=y,s|=c;if(l=l.next,l===null){if(l=o.shared.pending,l===null)break;c=l,l=c.next,c.next=null,o.lastBaseUpdate=c,o.shared.pending=null}}while(!0);if(f===null&&(a=d),o.baseState=a,o.firstBaseUpdate=u,o.lastBaseUpdate=f,t=o.shared.interleaved,t!==null){o=t;do s|=o.lane,o=o.next;while(o!==t)}else i===null&&(o.shared.lanes=0);nr|=s,e.lanes=s,e.memoizedState=d}}function Dd(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(O(191,o));o.call(r)}}}var gi={},Lt=Fn(gi),Jo=Fn(gi),ei=Fn(gi);function Vn(e){if(e===gi)throw Error(O(174));return e}function rc(e,t){switch(te(ei,t),te(Jo,e),te(Lt,gi),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Na(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Na(t,e)}ie(Lt),te(Lt,t)}function qr(){ie(Lt),ie(Jo),ie(ei)}function Th(e){Vn(ei.current);var t=Vn(Lt.current),n=Na(t,e.type);t!==n&&(te(Jo,e),te(Lt,n))}function oc(e){Jo.current===e&&(ie(Lt),ie(Jo))}var ue=Fn(0);function _s(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Gl=[];function ic(){for(var e=0;e<Gl.length;e++)Gl[e]._workInProgressVersionPrimary=null;Gl.length=0}var os=qt.ReactCurrentDispatcher,Yl=qt.ReactCurrentBatchConfig,tr=0,ce=null,ye=null,Se=null,As=!1,Do=!1,ti=0,I0=0;function Ne(){throw Error(O(321))}function sc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!St(e[n],t[n]))return!1;return!0}function lc(e,t,n,r,o,i){if(tr=i,ce=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,os.current=e===null||e.memoizedState===null?$0:U0,e=n(r,o),Do){i=0;do{if(Do=!1,ti=0,25<=i)throw Error(O(301));i+=1,Se=ye=null,t.updateQueue=null,os.current=B0,e=n(r,o)}while(Do)}if(os.current=Ms,t=ye!==null&&ye.next!==null,tr=0,Se=ye=ce=null,As=!1,t)throw Error(O(300));return e}function ac(){var e=ti!==0;return ti=0,e}function Nt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Se===null?ce.memoizedState=Se=e:Se=Se.next=e,Se}function ut(){if(ye===null){var e=ce.alternate;e=e!==null?e.memoizedState:null}else e=ye.next;var t=Se===null?ce.memoizedState:Se.next;if(t!==null)Se=t,ye=e;else{if(e===null)throw Error(O(310));ye=e,e={memoizedState:ye.memoizedState,baseState:ye.baseState,baseQueue:ye.baseQueue,queue:ye.queue,next:null},Se===null?ce.memoizedState=Se=e:Se=Se.next=e}return Se}function ni(e,t){return typeof t=="function"?t(e):t}function Xl(e){var t=ut(),n=t.queue;if(n===null)throw Error(O(311));n.lastRenderedReducer=e;var r=ye,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var l=s=null,a=null,u=i;do{var f=u.lane;if((tr&f)===f)a!==null&&(a=a.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:f,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};a===null?(l=a=d,s=r):a=a.next=d,ce.lanes|=f,nr|=f}u=u.next}while(u!==null&&u!==i);a===null?s=r:a.next=l,St(r,t.memoizedState)||(Be=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do i=o.lane,ce.lanes|=i,nr|=i,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ql(e){var t=ut(),n=t.queue;if(n===null)throw Error(O(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=e(i,s.action),s=s.next;while(s!==o);St(i,t.memoizedState)||(Be=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Nh(){}function Rh(e,t){var n=ce,r=ut(),o=t(),i=!St(r.memoizedState,o);if(i&&(r.memoizedState=o,Be=!0),r=r.queue,uc(Ah.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||Se!==null&&Se.memoizedState.tag&1){if(n.flags|=2048,ri(9,_h.bind(null,n,r,o,t),void 0,null),Ee===null)throw Error(O(349));tr&30||Oh(n,t,o)}return o}function Oh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ce.updateQueue,t===null?(t={lastEffect:null,stores:null},ce.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function _h(e,t,n,r){t.value=n,t.getSnapshot=r,Mh(t)&&jh(e)}function Ah(e,t,n){return n(function(){Mh(t)&&jh(e)})}function Mh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!St(e,n)}catch{return!0}}function jh(e){var t=Kt(e,1);t!==null&&xt(t,e,1,-1)}function Fd(e){var t=Nt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ni,lastRenderedState:e},t.queue=e,e=e.dispatch=z0.bind(null,ce,e),[t.memoizedState,e]}function ri(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ce.updateQueue,t===null?(t={lastEffect:null,stores:null},ce.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Lh(){return ut().memoizedState}function is(e,t,n,r){var o=Nt();ce.flags|=e,o.memoizedState=ri(1|t,n,void 0,r===void 0?null:r)}function tl(e,t,n,r){var o=ut();r=r===void 0?null:r;var i=void 0;if(ye!==null){var s=ye.memoizedState;if(i=s.destroy,r!==null&&sc(r,s.deps)){o.memoizedState=ri(t,n,i,r);return}}ce.flags|=e,o.memoizedState=ri(1|t,n,i,r)}function zd(e,t){return is(8390656,8,e,t)}function uc(e,t){return tl(2048,8,e,t)}function Ih(e,t){return tl(4,2,e,t)}function Dh(e,t){return tl(4,4,e,t)}function Fh(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function zh(e,t,n){return n=n!=null?n.concat([e]):null,tl(4,4,Fh.bind(null,t,e),n)}function cc(){}function $h(e,t){var n=ut();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&sc(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Uh(e,t){var n=ut();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&sc(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Bh(e,t,n){return tr&21?(St(n,t)||(n=Kp(),ce.lanes|=n,nr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Be=!0),e.memoizedState=n)}function D0(e,t){var n=J;J=n!==0&&4>n?n:4,e(!0);var r=Yl.transition;Yl.transition={};try{e(!1),t()}finally{J=n,Yl.transition=r}}function Wh(){return ut().memoizedState}function F0(e,t,n){var r=Nn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Hh(e))Vh(t,n);else if(n=Ph(e,t,n,r),n!==null){var o=Ie();xt(n,e,r,o),Qh(n,t,r)}}function z0(e,t,n){var r=Nn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Hh(e))Vh(t,o);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,l=i(s,n);if(o.hasEagerState=!0,o.eagerState=l,St(l,s)){var a=t.interleaved;a===null?(o.next=o,tc(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=Ph(e,t,o,r),n!==null&&(o=Ie(),xt(n,e,r,o),Qh(n,t,r))}}function Hh(e){var t=e.alternate;return e===ce||t!==null&&t===ce}function Vh(e,t){Do=As=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Qh(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Uu(e,n)}}var Ms={readContext:at,useCallback:Ne,useContext:Ne,useEffect:Ne,useImperativeHandle:Ne,useInsertionEffect:Ne,useLayoutEffect:Ne,useMemo:Ne,useReducer:Ne,useRef:Ne,useState:Ne,useDebugValue:Ne,useDeferredValue:Ne,useTransition:Ne,useMutableSource:Ne,useSyncExternalStore:Ne,useId:Ne,unstable_isNewReconciler:!1},$0={readContext:at,useCallback:function(e,t){return Nt().memoizedState=[e,t===void 0?null:t],e},useContext:at,useEffect:zd,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,is(4194308,4,Fh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return is(4194308,4,e,t)},useInsertionEffect:function(e,t){return is(4,2,e,t)},useMemo:function(e,t){var n=Nt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Nt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=F0.bind(null,ce,e),[r.memoizedState,e]},useRef:function(e){var t=Nt();return e={current:e},t.memoizedState=e},useState:Fd,useDebugValue:cc,useDeferredValue:function(e){return Nt().memoizedState=e},useTransition:function(){var e=Fd(!1),t=e[0];return e=D0.bind(null,e[1]),Nt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ce,o=Nt();if(se){if(n===void 0)throw Error(O(407));n=n()}else{if(n=t(),Ee===null)throw Error(O(349));tr&30||Oh(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,zd(Ah.bind(null,r,i,e),[e]),r.flags|=2048,ri(9,_h.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=Nt(),t=Ee.identifierPrefix;if(se){var n=Wt,r=Bt;n=(r&~(1<<32-wt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=ti++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=I0++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},U0={readContext:at,useCallback:$h,useContext:at,useEffect:uc,useImperativeHandle:zh,useInsertionEffect:Ih,useLayoutEffect:Dh,useMemo:Uh,useReducer:Xl,useRef:Lh,useState:function(){return Xl(ni)},useDebugValue:cc,useDeferredValue:function(e){var t=ut();return Bh(t,ye.memoizedState,e)},useTransition:function(){var e=Xl(ni)[0],t=ut().memoizedState;return[e,t]},useMutableSource:Nh,useSyncExternalStore:Rh,useId:Wh,unstable_isNewReconciler:!1},B0={readContext:at,useCallback:$h,useContext:at,useEffect:uc,useImperativeHandle:zh,useInsertionEffect:Ih,useLayoutEffect:Dh,useMemo:Uh,useReducer:ql,useRef:Lh,useState:function(){return ql(ni)},useDebugValue:cc,useDeferredValue:function(e){var t=ut();return ye===null?t.memoizedState=e:Bh(t,ye.memoizedState,e)},useTransition:function(){var e=ql(ni)[0],t=ut().memoizedState;return[e,t]},useMutableSource:Nh,useSyncExternalStore:Rh,useId:Wh,unstable_isNewReconciler:!1};function ht(e,t){if(e&&e.defaultProps){t=de({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Ya(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:de({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var nl={isMounted:function(e){return(e=e._reactInternals)?lr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Ie(),o=Nn(e),i=Ht(r,o);i.payload=t,n!=null&&(i.callback=n),t=bn(e,i,o),t!==null&&(xt(t,e,o,r),rs(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Ie(),o=Nn(e),i=Ht(r,o);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=bn(e,i,o),t!==null&&(xt(t,e,o,r),rs(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Ie(),r=Nn(e),o=Ht(n,r);o.tag=2,t!=null&&(o.callback=t),t=bn(e,o,r),t!==null&&(xt(t,e,r,n),rs(t,e,r))}};function $d(e,t,n,r,o,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,s):t.prototype&&t.prototype.isPureReactComponent?!Yo(n,r)||!Yo(o,i):!0}function Kh(e,t,n){var r=!1,o=_n,i=t.contextType;return typeof i=="object"&&i!==null?i=at(i):(o=He(t)?Jn:Ae.current,r=t.contextTypes,i=(r=r!=null)?Gr(e,o):_n),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=nl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=i),t}function Ud(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&nl.enqueueReplaceState(t,t.state,null)}function Xa(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},nc(e);var i=t.contextType;typeof i=="object"&&i!==null?o.context=at(i):(i=He(t)?Jn:Ae.current,o.context=Gr(e,i)),o.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(Ya(e,t,i,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&nl.enqueueReplaceState(o,o.state,null),Os(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Zr(e,t){try{var n="",r=t;do n+=vy(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:o,digest:null}}function Zl(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function qa(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var W0=typeof WeakMap=="function"?WeakMap:Map;function Gh(e,t,n){n=Ht(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Ls||(Ls=!0,lu=r),qa(e,t)},n}function Yh(e,t,n){n=Ht(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){qa(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){qa(e,t),typeof r!="function"&&(Tn===null?Tn=new Set([this]):Tn.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function Bd(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new W0;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=rw.bind(null,e,t,n),t.then(e,e))}function Wd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Hd(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Ht(-1,1),t.tag=2,bn(n,t,1))),n.lanes|=1),e)}var H0=qt.ReactCurrentOwner,Be=!1;function je(e,t,n,r){t.child=e===null?kh(t,null,n,r):Xr(t,e.child,n,r)}function Vd(e,t,n,r,o){n=n.render;var i=t.ref;return Mr(t,o),r=lc(e,t,n,r,i,o),n=ac(),e!==null&&!Be?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Gt(e,t,o)):(se&&n&&Yu(t),t.flags|=1,je(e,t,r,o),t.child)}function Qd(e,t,n,r,o){if(e===null){var i=n.type;return typeof i=="function"&&!yc(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Xh(e,t,i,r,o)):(e=us(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:Yo,n(s,r)&&e.ref===t.ref)return Gt(e,t,o)}return t.flags|=1,e=Rn(i,r),e.ref=t.ref,e.return=t,t.child=e}function Xh(e,t,n,r,o){if(e!==null){var i=e.memoizedProps;if(Yo(i,r)&&e.ref===t.ref)if(Be=!1,t.pendingProps=r=i,(e.lanes&o)!==0)e.flags&131072&&(Be=!0);else return t.lanes=e.lanes,Gt(e,t,o)}return Za(e,t,n,r,o)}function qh(e,t,n){var r=t.pendingProps,o=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},te(Nr,Ye),Ye|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,te(Nr,Ye),Ye|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,te(Nr,Ye),Ye|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,te(Nr,Ye),Ye|=r;return je(e,t,o,n),t.child}function Zh(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Za(e,t,n,r,o){var i=He(n)?Jn:Ae.current;return i=Gr(t,i),Mr(t,o),n=lc(e,t,n,r,i,o),r=ac(),e!==null&&!Be?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Gt(e,t,o)):(se&&r&&Yu(t),t.flags|=1,je(e,t,n,o),t.child)}function Kd(e,t,n,r,o){if(He(n)){var i=!0;Ps(t)}else i=!1;if(Mr(t,o),t.stateNode===null)ss(e,t),Kh(t,n,r),Xa(t,n,r,o),r=!0;else if(e===null){var s=t.stateNode,l=t.memoizedProps;s.props=l;var a=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=at(u):(u=He(n)?Jn:Ae.current,u=Gr(t,u));var f=n.getDerivedStateFromProps,d=typeof f=="function"||typeof s.getSnapshotBeforeUpdate=="function";d||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==r||a!==u)&&Ud(t,s,r,u),cn=!1;var c=t.memoizedState;s.state=c,Os(t,r,s,o),a=t.memoizedState,l!==r||c!==a||We.current||cn?(typeof f=="function"&&(Ya(t,n,f,r),a=t.memoizedState),(l=cn||$d(t,n,l,r,c,a,u))?(d||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),s.props=r,s.state=a,s.context=u,r=l):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,bh(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:ht(t.type,l),s.props=u,d=t.pendingProps,c=s.context,a=n.contextType,typeof a=="object"&&a!==null?a=at(a):(a=He(n)?Jn:Ae.current,a=Gr(t,a));var y=n.getDerivedStateFromProps;(f=typeof y=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(l!==d||c!==a)&&Ud(t,s,r,a),cn=!1,c=t.memoizedState,s.state=c,Os(t,r,s,o);var x=t.memoizedState;l!==d||c!==x||We.current||cn?(typeof y=="function"&&(Ya(t,n,y,r),x=t.memoizedState),(u=cn||$d(t,n,u,r,c,x,a)||!1)?(f||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,x,a),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,x,a)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&c===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&c===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=x),s.props=r,s.state=x,s.context=a,r=u):(typeof s.componentDidUpdate!="function"||l===e.memoizedProps&&c===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&c===e.memoizedState||(t.flags|=1024),r=!1)}return Ja(e,t,n,r,i,o)}function Ja(e,t,n,r,o,i){Zh(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return o&&Ad(t,n,!1),Gt(e,t,i);r=t.stateNode,H0.current=t;var l=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=Xr(t,e.child,null,i),t.child=Xr(t,null,l,i)):je(e,t,l,i),t.memoizedState=r.state,o&&Ad(t,n,!0),t.child}function Jh(e){var t=e.stateNode;t.pendingContext?_d(e,t.pendingContext,t.pendingContext!==t.context):t.context&&_d(e,t.context,!1),rc(e,t.containerInfo)}function Gd(e,t,n,r,o){return Yr(),qu(o),t.flags|=256,je(e,t,n,r),t.child}var eu={dehydrated:null,treeContext:null,retryLane:0};function tu(e){return{baseLanes:e,cachePool:null,transitions:null}}function em(e,t,n){var r=t.pendingProps,o=ue.current,i=!1,s=(t.flags&128)!==0,l;if((l=s)||(l=e!==null&&e.memoizedState===null?!1:(o&2)!==0),l?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),te(ue,o&1),e===null)return Ka(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,i?(r=t.mode,i=t.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=il(s,r,0,null),e=qn(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=tu(n),t.memoizedState=eu,e):dc(t,s));if(o=e.memoizedState,o!==null&&(l=o.dehydrated,l!==null))return V0(e,t,s,r,l,o,n);if(i){i=r.fallback,s=t.mode,o=e.child,l=o.sibling;var a={mode:"hidden",children:r.children};return!(s&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Rn(o,a),r.subtreeFlags=o.subtreeFlags&14680064),l!==null?i=Rn(l,i):(i=qn(i,s,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,s=e.child.memoizedState,s=s===null?tu(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=eu,r}return i=e.child,e=i.sibling,r=Rn(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function dc(e,t){return t=il({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Fi(e,t,n,r){return r!==null&&qu(r),Xr(t,e.child,null,n),e=dc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function V0(e,t,n,r,o,i,s){if(n)return t.flags&256?(t.flags&=-257,r=Zl(Error(O(422))),Fi(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=il({mode:"visible",children:r.children},o,0,null),i=qn(i,o,s,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Xr(t,e.child,null,s),t.child.memoizedState=tu(s),t.memoizedState=eu,i);if(!(t.mode&1))return Fi(e,t,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var l=r.dgst;return r=l,i=Error(O(419)),r=Zl(i,r,void 0),Fi(e,t,s,r)}if(l=(s&e.childLanes)!==0,Be||l){if(r=Ee,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,Kt(e,o),xt(r,e,o,-1))}return gc(),r=Zl(Error(O(421))),Fi(e,t,s,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=ow.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,qe=Pn(o.nextSibling),Ze=t,se=!0,yt=null,e!==null&&(ot[it++]=Bt,ot[it++]=Wt,ot[it++]=er,Bt=e.id,Wt=e.overflow,er=t),t=dc(t,r.children),t.flags|=4096,t)}function Yd(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Ga(e.return,t,n)}function Jl(e,t,n,r,o){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function tm(e,t,n){var r=t.pendingProps,o=r.revealOrder,i=r.tail;if(je(e,t,r.children,n),r=ue.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Yd(e,n,t);else if(e.tag===19)Yd(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(te(ue,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&_s(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Jl(t,!1,o,n,i);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&_s(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Jl(t,!0,n,null,i);break;case"together":Jl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ss(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Gt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),nr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(O(153));if(t.child!==null){for(e=t.child,n=Rn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Rn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function Q0(e,t,n){switch(t.tag){case 3:Jh(t),Yr();break;case 5:Th(t);break;case 1:He(t.type)&&Ps(t);break;case 4:rc(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;te(Ns,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(te(ue,ue.current&1),t.flags|=128,null):n&t.child.childLanes?em(e,t,n):(te(ue,ue.current&1),e=Gt(e,t,n),e!==null?e.sibling:null);te(ue,ue.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return tm(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),te(ue,ue.current),r)break;return null;case 22:case 23:return t.lanes=0,qh(e,t,n)}return Gt(e,t,n)}var nm,nu,rm,om;nm=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};nu=function(){};rm=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Vn(Lt.current);var i=null;switch(n){case"input":o=ka(e,o),r=ka(e,r),i=[];break;case"select":o=de({},o,{value:void 0}),r=de({},r,{value:void 0}),i=[];break;case"textarea":o=Ta(e,o),r=Ta(e,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Cs)}Ra(n,r);var s;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var l=o[u];for(s in l)l.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Bo.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var a=r[u];if(l=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&a!==l&&(a!=null||l!=null))if(u==="style")if(l){for(s in l)!l.hasOwnProperty(s)||a&&a.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in a)a.hasOwnProperty(s)&&l[s]!==a[s]&&(n||(n={}),n[s]=a[s])}else n||(i||(i=[]),i.push(u,n)),n=a;else u==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(i=i||[]).push(u,a)):u==="children"?typeof a!="string"&&typeof a!="number"||(i=i||[]).push(u,""+a):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Bo.hasOwnProperty(u)?(a!=null&&u==="onScroll"&&oe("scroll",e),i||l===a||(i=[])):(i=i||[]).push(u,a))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};om=function(e,t,n,r){n!==r&&(t.flags|=4)};function xo(e,t){if(!se)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Re(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function K0(e,t,n){var r=t.pendingProps;switch(Xu(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Re(t),null;case 1:return He(t.type)&&ks(),Re(t),null;case 3:return r=t.stateNode,qr(),ie(We),ie(Ae),ic(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Ii(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,yt!==null&&(cu(yt),yt=null))),nu(e,t),Re(t),null;case 5:oc(t);var o=Vn(ei.current);if(n=t.type,e!==null&&t.stateNode!=null)rm(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(O(166));return Re(t),null}if(e=Vn(Lt.current),Ii(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[At]=t,r[Zo]=i,e=(t.mode&1)!==0,n){case"dialog":oe("cancel",r),oe("close",r);break;case"iframe":case"object":case"embed":oe("load",r);break;case"video":case"audio":for(o=0;o<To.length;o++)oe(To[o],r);break;case"source":oe("error",r);break;case"img":case"image":case"link":oe("error",r),oe("load",r);break;case"details":oe("toggle",r);break;case"input":od(r,i),oe("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},oe("invalid",r);break;case"textarea":sd(r,i),oe("invalid",r)}Ra(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var l=i[s];s==="children"?typeof l=="string"?r.textContent!==l&&(i.suppressHydrationWarning!==!0&&Li(r.textContent,l,e),o=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(i.suppressHydrationWarning!==!0&&Li(r.textContent,l,e),o=["children",""+l]):Bo.hasOwnProperty(s)&&l!=null&&s==="onScroll"&&oe("scroll",r)}switch(n){case"input":Ti(r),id(r,i,!0);break;case"textarea":Ti(r),ld(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Cs)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Ap(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[At]=t,e[Zo]=r,nm(e,t,!1,!1),t.stateNode=e;e:{switch(s=Oa(n,r),n){case"dialog":oe("cancel",e),oe("close",e),o=r;break;case"iframe":case"object":case"embed":oe("load",e),o=r;break;case"video":case"audio":for(o=0;o<To.length;o++)oe(To[o],e);o=r;break;case"source":oe("error",e),o=r;break;case"img":case"image":case"link":oe("error",e),oe("load",e),o=r;break;case"details":oe("toggle",e),o=r;break;case"input":od(e,r),o=ka(e,r),oe("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=de({},r,{value:void 0}),oe("invalid",e);break;case"textarea":sd(e,r),o=Ta(e,r),oe("invalid",e);break;default:o=r}Ra(n,o),l=o;for(i in l)if(l.hasOwnProperty(i)){var a=l[i];i==="style"?Lp(e,a):i==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&Mp(e,a)):i==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&Wo(e,a):typeof a=="number"&&Wo(e,""+a):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Bo.hasOwnProperty(i)?a!=null&&i==="onScroll"&&oe("scroll",e):a!=null&&Lu(e,i,a,s))}switch(n){case"input":Ti(e),id(e,r,!1);break;case"textarea":Ti(e),ld(e);break;case"option":r.value!=null&&e.setAttribute("value",""+On(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Rr(e,!!r.multiple,i,!1):r.defaultValue!=null&&Rr(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Cs)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Re(t),null;case 6:if(e&&t.stateNode!=null)om(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(O(166));if(n=Vn(ei.current),Vn(Lt.current),Ii(t)){if(r=t.stateNode,n=t.memoizedProps,r[At]=t,(i=r.nodeValue!==n)&&(e=Ze,e!==null))switch(e.tag){case 3:Li(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Li(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[At]=t,t.stateNode=r}return Re(t),null;case 13:if(ie(ue),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(se&&qe!==null&&t.mode&1&&!(t.flags&128))Eh(),Yr(),t.flags|=98560,i=!1;else if(i=Ii(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(O(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(O(317));i[At]=t}else Yr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Re(t),i=!1}else yt!==null&&(cu(yt),yt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||ue.current&1?we===0&&(we=3):gc())),t.updateQueue!==null&&(t.flags|=4),Re(t),null);case 4:return qr(),nu(e,t),e===null&&Xo(t.stateNode.containerInfo),Re(t),null;case 10:return ec(t.type._context),Re(t),null;case 17:return He(t.type)&&ks(),Re(t),null;case 19:if(ie(ue),i=t.memoizedState,i===null)return Re(t),null;if(r=(t.flags&128)!==0,s=i.rendering,s===null)if(r)xo(i,!1);else{if(we!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=_s(e),s!==null){for(t.flags|=128,xo(i,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return te(ue,ue.current&1|2),t.child}e=e.sibling}i.tail!==null&&he()>Jr&&(t.flags|=128,r=!0,xo(i,!1),t.lanes=4194304)}else{if(!r)if(e=_s(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),xo(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!se)return Re(t),null}else 2*he()-i.renderingStartTime>Jr&&n!==1073741824&&(t.flags|=128,r=!0,xo(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=he(),t.sibling=null,n=ue.current,te(ue,r?n&1|2:n&1),t):(Re(t),null);case 22:case 23:return vc(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ye&1073741824&&(Re(t),t.subtreeFlags&6&&(t.flags|=8192)):Re(t),null;case 24:return null;case 25:return null}throw Error(O(156,t.tag))}function G0(e,t){switch(Xu(t),t.tag){case 1:return He(t.type)&&ks(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return qr(),ie(We),ie(Ae),ic(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return oc(t),null;case 13:if(ie(ue),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(O(340));Yr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return ie(ue),null;case 4:return qr(),null;case 10:return ec(t.type._context),null;case 22:case 23:return vc(),null;case 24:return null;default:return null}}var zi=!1,_e=!1,Y0=typeof WeakSet=="function"?WeakSet:Set,z=null;function Tr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){pe(e,t,r)}else n.current=null}function ru(e,t,n){try{n()}catch(r){pe(e,t,r)}}var Xd=!1;function X0(e,t){if($a=xs,e=uh(),Gu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var s=0,l=-1,a=-1,u=0,f=0,d=e,c=null;t:for(;;){for(var y;d!==n||o!==0&&d.nodeType!==3||(l=s+o),d!==i||r!==0&&d.nodeType!==3||(a=s+r),d.nodeType===3&&(s+=d.nodeValue.length),(y=d.firstChild)!==null;)c=d,d=y;for(;;){if(d===e)break t;if(c===n&&++u===o&&(l=s),c===i&&++f===r&&(a=s),(y=d.nextSibling)!==null)break;d=c,c=d.parentNode}d=y}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(Ua={focusedElem:e,selectionRange:n},xs=!1,z=t;z!==null;)if(t=z,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,z=e;else for(;z!==null;){t=z;try{var x=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(x!==null){var v=x.memoizedProps,w=x.memoizedState,h=t.stateNode,p=h.getSnapshotBeforeUpdate(t.elementType===t.type?v:ht(t.type,v),w);h.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(O(163))}}catch(E){pe(t,t.return,E)}if(e=t.sibling,e!==null){e.return=t.return,z=e;break}z=t.return}return x=Xd,Xd=!1,x}function Fo(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var i=o.destroy;o.destroy=void 0,i!==void 0&&ru(t,n,i)}o=o.next}while(o!==r)}}function rl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ou(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function im(e){var t=e.alternate;t!==null&&(e.alternate=null,im(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[At],delete t[Zo],delete t[Ha],delete t[A0],delete t[M0])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function sm(e){return e.tag===5||e.tag===3||e.tag===4}function qd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||sm(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function iu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Cs));else if(r!==4&&(e=e.child,e!==null))for(iu(e,t,n),e=e.sibling;e!==null;)iu(e,t,n),e=e.sibling}function su(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(su(e,t,n),e=e.sibling;e!==null;)su(e,t,n),e=e.sibling}var ke=null,gt=!1;function rn(e,t,n){for(n=n.child;n!==null;)lm(e,t,n),n=n.sibling}function lm(e,t,n){if(jt&&typeof jt.onCommitFiberUnmount=="function")try{jt.onCommitFiberUnmount(Ys,n)}catch{}switch(n.tag){case 5:_e||Tr(n,t);case 6:var r=ke,o=gt;ke=null,rn(e,t,n),ke=r,gt=o,ke!==null&&(gt?(e=ke,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ke.removeChild(n.stateNode));break;case 18:ke!==null&&(gt?(e=ke,n=n.stateNode,e.nodeType===8?Ql(e.parentNode,n):e.nodeType===1&&Ql(e,n),Ko(e)):Ql(ke,n.stateNode));break;case 4:r=ke,o=gt,ke=n.stateNode.containerInfo,gt=!0,rn(e,t,n),ke=r,gt=o;break;case 0:case 11:case 14:case 15:if(!_e&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&ru(n,t,s),o=o.next}while(o!==r)}rn(e,t,n);break;case 1:if(!_e&&(Tr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){pe(n,t,l)}rn(e,t,n);break;case 21:rn(e,t,n);break;case 22:n.mode&1?(_e=(r=_e)||n.memoizedState!==null,rn(e,t,n),_e=r):rn(e,t,n);break;default:rn(e,t,n)}}function Zd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Y0),t.forEach(function(r){var o=iw.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function ft(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,l=s;e:for(;l!==null;){switch(l.tag){case 5:ke=l.stateNode,gt=!1;break e;case 3:ke=l.stateNode.containerInfo,gt=!0;break e;case 4:ke=l.stateNode.containerInfo,gt=!0;break e}l=l.return}if(ke===null)throw Error(O(160));lm(i,s,o),ke=null,gt=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(u){pe(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)am(t,e),t=t.sibling}function am(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ft(t,e),Tt(e),r&4){try{Fo(3,e,e.return),rl(3,e)}catch(v){pe(e,e.return,v)}try{Fo(5,e,e.return)}catch(v){pe(e,e.return,v)}}break;case 1:ft(t,e),Tt(e),r&512&&n!==null&&Tr(n,n.return);break;case 5:if(ft(t,e),Tt(e),r&512&&n!==null&&Tr(n,n.return),e.flags&32){var o=e.stateNode;try{Wo(o,"")}catch(v){pe(e,e.return,v)}}if(r&4&&(o=e.stateNode,o!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&i.type==="radio"&&i.name!=null&&Op(o,i),Oa(l,s);var u=Oa(l,i);for(s=0;s<a.length;s+=2){var f=a[s],d=a[s+1];f==="style"?Lp(o,d):f==="dangerouslySetInnerHTML"?Mp(o,d):f==="children"?Wo(o,d):Lu(o,f,d,u)}switch(l){case"input":Pa(o,i);break;case"textarea":_p(o,i);break;case"select":var c=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var y=i.value;y!=null?Rr(o,!!i.multiple,y,!1):c!==!!i.multiple&&(i.defaultValue!=null?Rr(o,!!i.multiple,i.defaultValue,!0):Rr(o,!!i.multiple,i.multiple?[]:"",!1))}o[Zo]=i}catch(v){pe(e,e.return,v)}}break;case 6:if(ft(t,e),Tt(e),r&4){if(e.stateNode===null)throw Error(O(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(v){pe(e,e.return,v)}}break;case 3:if(ft(t,e),Tt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Ko(t.containerInfo)}catch(v){pe(e,e.return,v)}break;case 4:ft(t,e),Tt(e);break;case 13:ft(t,e),Tt(e),o=e.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(hc=he())),r&4&&Zd(e);break;case 22:if(f=n!==null&&n.memoizedState!==null,e.mode&1?(_e=(u=_e)||f,ft(t,e),_e=u):ft(t,e),Tt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!f&&e.mode&1)for(z=e,f=e.child;f!==null;){for(d=z=f;z!==null;){switch(c=z,y=c.child,c.tag){case 0:case 11:case 14:case 15:Fo(4,c,c.return);break;case 1:Tr(c,c.return);var x=c.stateNode;if(typeof x.componentWillUnmount=="function"){r=c,n=c.return;try{t=r,x.props=t.memoizedProps,x.state=t.memoizedState,x.componentWillUnmount()}catch(v){pe(r,n,v)}}break;case 5:Tr(c,c.return);break;case 22:if(c.memoizedState!==null){ef(d);continue}}y!==null?(y.return=c,z=y):ef(d)}f=f.sibling}e:for(f=null,d=e;;){if(d.tag===5){if(f===null){f=d;try{o=d.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(l=d.stateNode,a=d.memoizedProps.style,s=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=jp("display",s))}catch(v){pe(e,e.return,v)}}}else if(d.tag===6){if(f===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(v){pe(e,e.return,v)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;f===d&&(f=null),d=d.return}f===d&&(f=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:ft(t,e),Tt(e),r&4&&Zd(e);break;case 21:break;default:ft(t,e),Tt(e)}}function Tt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(sm(n)){var r=n;break e}n=n.return}throw Error(O(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Wo(o,""),r.flags&=-33);var i=qd(e);su(e,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,l=qd(e);iu(e,l,s);break;default:throw Error(O(161))}}catch(a){pe(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function q0(e,t,n){z=e,um(e)}function um(e,t,n){for(var r=(e.mode&1)!==0;z!==null;){var o=z,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||zi;if(!s){var l=o.alternate,a=l!==null&&l.memoizedState!==null||_e;l=zi;var u=_e;if(zi=s,(_e=a)&&!u)for(z=o;z!==null;)s=z,a=s.child,s.tag===22&&s.memoizedState!==null?tf(o):a!==null?(a.return=s,z=a):tf(o);for(;i!==null;)z=i,um(i),i=i.sibling;z=o,zi=l,_e=u}Jd(e)}else o.subtreeFlags&8772&&i!==null?(i.return=o,z=i):Jd(e)}}function Jd(e){for(;z!==null;){var t=z;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:_e||rl(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!_e)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:ht(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Dd(t,i,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Dd(t,s,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var f=u.memoizedState;if(f!==null){var d=f.dehydrated;d!==null&&Ko(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(O(163))}_e||t.flags&512&&ou(t)}catch(c){pe(t,t.return,c)}}if(t===e){z=null;break}if(n=t.sibling,n!==null){n.return=t.return,z=n;break}z=t.return}}function ef(e){for(;z!==null;){var t=z;if(t===e){z=null;break}var n=t.sibling;if(n!==null){n.return=t.return,z=n;break}z=t.return}}function tf(e){for(;z!==null;){var t=z;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rl(4,t)}catch(a){pe(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){pe(t,o,a)}}var i=t.return;try{ou(t)}catch(a){pe(t,i,a)}break;case 5:var s=t.return;try{ou(t)}catch(a){pe(t,s,a)}}}catch(a){pe(t,t.return,a)}if(t===e){z=null;break}var l=t.sibling;if(l!==null){l.return=t.return,z=l;break}z=t.return}}var Z0=Math.ceil,js=qt.ReactCurrentDispatcher,fc=qt.ReactCurrentOwner,lt=qt.ReactCurrentBatchConfig,X=0,Ee=null,ge=null,Pe=0,Ye=0,Nr=Fn(0),we=0,oi=null,nr=0,ol=0,pc=0,zo=null,Ue=null,hc=0,Jr=1/0,$t=null,Ls=!1,lu=null,Tn=null,$i=!1,wn=null,Is=0,$o=0,au=null,ls=-1,as=0;function Ie(){return X&6?he():ls!==-1?ls:ls=he()}function Nn(e){return e.mode&1?X&2&&Pe!==0?Pe&-Pe:L0.transition!==null?(as===0&&(as=Kp()),as):(e=J,e!==0||(e=window.event,e=e===void 0?16:eh(e.type)),e):1}function xt(e,t,n,r){if(50<$o)throw $o=0,au=null,Error(O(185));hi(e,n,r),(!(X&2)||e!==Ee)&&(e===Ee&&(!(X&2)&&(ol|=n),we===4&&fn(e,Pe)),Ve(e,r),n===1&&X===0&&!(t.mode&1)&&(Jr=he()+500,el&&zn()))}function Ve(e,t){var n=e.callbackNode;Ly(e,t);var r=ws(e,e===Ee?Pe:0);if(r===0)n!==null&&cd(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&cd(n),t===1)e.tag===0?j0(nf.bind(null,e)):wh(nf.bind(null,e)),O0(function(){!(X&6)&&zn()}),n=null;else{switch(Gp(r)){case 1:n=$u;break;case 4:n=Vp;break;case 16:n=ys;break;case 536870912:n=Qp;break;default:n=ys}n=gm(n,cm.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function cm(e,t){if(ls=-1,as=0,X&6)throw Error(O(327));var n=e.callbackNode;if(jr()&&e.callbackNode!==n)return null;var r=ws(e,e===Ee?Pe:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Ds(e,r);else{t=r;var o=X;X|=2;var i=fm();(Ee!==e||Pe!==t)&&($t=null,Jr=he()+500,Xn(e,t));do try{tw();break}catch(l){dm(e,l)}while(!0);Ju(),js.current=i,X=o,ge!==null?t=0:(Ee=null,Pe=0,t=we)}if(t!==0){if(t===2&&(o=La(e),o!==0&&(r=o,t=uu(e,o))),t===1)throw n=oi,Xn(e,0),fn(e,r),Ve(e,he()),n;if(t===6)fn(e,r);else{if(o=e.current.alternate,!(r&30)&&!J0(o)&&(t=Ds(e,r),t===2&&(i=La(e),i!==0&&(r=i,t=uu(e,i))),t===1))throw n=oi,Xn(e,0),fn(e,r),Ve(e,he()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(O(345));case 2:Bn(e,Ue,$t);break;case 3:if(fn(e,r),(r&130023424)===r&&(t=hc+500-he(),10<t)){if(ws(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){Ie(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Wa(Bn.bind(null,e,Ue,$t),t);break}Bn(e,Ue,$t);break;case 4:if(fn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-wt(r);i=1<<s,s=t[s],s>o&&(o=s),r&=~i}if(r=o,r=he()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Z0(r/1960))-r,10<r){e.timeoutHandle=Wa(Bn.bind(null,e,Ue,$t),r);break}Bn(e,Ue,$t);break;case 5:Bn(e,Ue,$t);break;default:throw Error(O(329))}}}return Ve(e,he()),e.callbackNode===n?cm.bind(null,e):null}function uu(e,t){var n=zo;return e.current.memoizedState.isDehydrated&&(Xn(e,t).flags|=256),e=Ds(e,t),e!==2&&(t=Ue,Ue=n,t!==null&&cu(t)),e}function cu(e){Ue===null?Ue=e:Ue.push.apply(Ue,e)}function J0(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!St(i(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function fn(e,t){for(t&=~pc,t&=~ol,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-wt(t),r=1<<n;e[n]=-1,t&=~r}}function nf(e){if(X&6)throw Error(O(327));jr();var t=ws(e,0);if(!(t&1))return Ve(e,he()),null;var n=Ds(e,t);if(e.tag!==0&&n===2){var r=La(e);r!==0&&(t=r,n=uu(e,r))}if(n===1)throw n=oi,Xn(e,0),fn(e,t),Ve(e,he()),n;if(n===6)throw Error(O(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Bn(e,Ue,$t),Ve(e,he()),null}function mc(e,t){var n=X;X|=1;try{return e(t)}finally{X=n,X===0&&(Jr=he()+500,el&&zn())}}function rr(e){wn!==null&&wn.tag===0&&!(X&6)&&jr();var t=X;X|=1;var n=lt.transition,r=J;try{if(lt.transition=null,J=1,e)return e()}finally{J=r,lt.transition=n,X=t,!(X&6)&&zn()}}function vc(){Ye=Nr.current,ie(Nr)}function Xn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,R0(n)),ge!==null)for(n=ge.return;n!==null;){var r=n;switch(Xu(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ks();break;case 3:qr(),ie(We),ie(Ae),ic();break;case 5:oc(r);break;case 4:qr();break;case 13:ie(ue);break;case 19:ie(ue);break;case 10:ec(r.type._context);break;case 22:case 23:vc()}n=n.return}if(Ee=e,ge=e=Rn(e.current,null),Pe=Ye=t,we=0,oi=null,pc=ol=nr=0,Ue=zo=null,Hn!==null){for(t=0;t<Hn.length;t++)if(n=Hn[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}Hn=null}return e}function dm(e,t){do{var n=ge;try{if(Ju(),os.current=Ms,As){for(var r=ce.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}As=!1}if(tr=0,Se=ye=ce=null,Do=!1,ti=0,fc.current=null,n===null||n.return===null){we=1,oi=t,ge=null;break}e:{var i=e,s=n.return,l=n,a=t;if(t=Pe,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var u=a,f=l,d=f.tag;if(!(f.mode&1)&&(d===0||d===11||d===15)){var c=f.alternate;c?(f.updateQueue=c.updateQueue,f.memoizedState=c.memoizedState,f.lanes=c.lanes):(f.updateQueue=null,f.memoizedState=null)}var y=Wd(s);if(y!==null){y.flags&=-257,Hd(y,s,l,i,t),y.mode&1&&Bd(i,u,t),t=y,a=u;var x=t.updateQueue;if(x===null){var v=new Set;v.add(a),t.updateQueue=v}else x.add(a);break e}else{if(!(t&1)){Bd(i,u,t),gc();break e}a=Error(O(426))}}else if(se&&l.mode&1){var w=Wd(s);if(w!==null){!(w.flags&65536)&&(w.flags|=256),Hd(w,s,l,i,t),qu(Zr(a,l));break e}}i=a=Zr(a,l),we!==4&&(we=2),zo===null?zo=[i]:zo.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var h=Gh(i,a,t);Id(i,h);break e;case 1:l=a;var p=i.type,g=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(Tn===null||!Tn.has(g)))){i.flags|=65536,t&=-t,i.lanes|=t;var E=Yh(i,l,t);Id(i,E);break e}}i=i.return}while(i!==null)}hm(n)}catch(C){t=C,ge===n&&n!==null&&(ge=n=n.return);continue}break}while(!0)}function fm(){var e=js.current;return js.current=Ms,e===null?Ms:e}function gc(){(we===0||we===3||we===2)&&(we=4),Ee===null||!(nr&268435455)&&!(ol&268435455)||fn(Ee,Pe)}function Ds(e,t){var n=X;X|=2;var r=fm();(Ee!==e||Pe!==t)&&($t=null,Xn(e,t));do try{ew();break}catch(o){dm(e,o)}while(!0);if(Ju(),X=n,js.current=r,ge!==null)throw Error(O(261));return Ee=null,Pe=0,we}function ew(){for(;ge!==null;)pm(ge)}function tw(){for(;ge!==null&&!by();)pm(ge)}function pm(e){var t=vm(e.alternate,e,Ye);e.memoizedProps=e.pendingProps,t===null?hm(e):ge=t,fc.current=null}function hm(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=G0(n,t),n!==null){n.flags&=32767,ge=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{we=6,ge=null;return}}else if(n=K0(n,t,Ye),n!==null){ge=n;return}if(t=t.sibling,t!==null){ge=t;return}ge=t=e}while(t!==null);we===0&&(we=5)}function Bn(e,t,n){var r=J,o=lt.transition;try{lt.transition=null,J=1,nw(e,t,n,r)}finally{lt.transition=o,J=r}return null}function nw(e,t,n,r){do jr();while(wn!==null);if(X&6)throw Error(O(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(O(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Iy(e,i),e===Ee&&(ge=Ee=null,Pe=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||$i||($i=!0,gm(ys,function(){return jr(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=lt.transition,lt.transition=null;var s=J;J=1;var l=X;X|=4,fc.current=null,X0(e,n),am(n,e),E0(Ua),xs=!!$a,Ua=$a=null,e.current=n,q0(n),Ty(),X=l,J=s,lt.transition=i}else e.current=n;if($i&&($i=!1,wn=e,Is=o),i=e.pendingLanes,i===0&&(Tn=null),Oy(n.stateNode),Ve(e,he()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Ls)throw Ls=!1,e=lu,lu=null,e;return Is&1&&e.tag!==0&&jr(),i=e.pendingLanes,i&1?e===au?$o++:($o=0,au=e):$o=0,zn(),null}function jr(){if(wn!==null){var e=Gp(Is),t=lt.transition,n=J;try{if(lt.transition=null,J=16>e?16:e,wn===null)var r=!1;else{if(e=wn,wn=null,Is=0,X&6)throw Error(O(331));var o=X;for(X|=4,z=e.current;z!==null;){var i=z,s=i.child;if(z.flags&16){var l=i.deletions;if(l!==null){for(var a=0;a<l.length;a++){var u=l[a];for(z=u;z!==null;){var f=z;switch(f.tag){case 0:case 11:case 15:Fo(8,f,i)}var d=f.child;if(d!==null)d.return=f,z=d;else for(;z!==null;){f=z;var c=f.sibling,y=f.return;if(im(f),f===u){z=null;break}if(c!==null){c.return=y,z=c;break}z=y}}}var x=i.alternate;if(x!==null){var v=x.child;if(v!==null){x.child=null;do{var w=v.sibling;v.sibling=null,v=w}while(v!==null)}}z=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,z=s;else e:for(;z!==null;){if(i=z,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Fo(9,i,i.return)}var h=i.sibling;if(h!==null){h.return=i.return,z=h;break e}z=i.return}}var p=e.current;for(z=p;z!==null;){s=z;var g=s.child;if(s.subtreeFlags&2064&&g!==null)g.return=s,z=g;else e:for(s=p;z!==null;){if(l=z,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:rl(9,l)}}catch(C){pe(l,l.return,C)}if(l===s){z=null;break e}var E=l.sibling;if(E!==null){E.return=l.return,z=E;break e}z=l.return}}if(X=o,zn(),jt&&typeof jt.onPostCommitFiberRoot=="function")try{jt.onPostCommitFiberRoot(Ys,e)}catch{}r=!0}return r}finally{J=n,lt.transition=t}}return!1}function rf(e,t,n){t=Zr(n,t),t=Gh(e,t,1),e=bn(e,t,1),t=Ie(),e!==null&&(hi(e,1,t),Ve(e,t))}function pe(e,t,n){if(e.tag===3)rf(e,e,n);else for(;t!==null;){if(t.tag===3){rf(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Tn===null||!Tn.has(r))){e=Zr(n,e),e=Yh(t,e,1),t=bn(t,e,1),e=Ie(),t!==null&&(hi(t,1,e),Ve(t,e));break}}t=t.return}}function rw(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Ie(),e.pingedLanes|=e.suspendedLanes&n,Ee===e&&(Pe&n)===n&&(we===4||we===3&&(Pe&130023424)===Pe&&500>he()-hc?Xn(e,0):pc|=n),Ve(e,t)}function mm(e,t){t===0&&(e.mode&1?(t=Oi,Oi<<=1,!(Oi&130023424)&&(Oi=4194304)):t=1);var n=Ie();e=Kt(e,t),e!==null&&(hi(e,t,n),Ve(e,n))}function ow(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),mm(e,n)}function iw(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(O(314))}r!==null&&r.delete(t),mm(e,n)}var vm;vm=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||We.current)Be=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Be=!1,Q0(e,t,n);Be=!!(e.flags&131072)}else Be=!1,se&&t.flags&1048576&&xh(t,Ts,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ss(e,t),e=t.pendingProps;var o=Gr(t,Ae.current);Mr(t,n),o=lc(null,t,r,e,o,n);var i=ac();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,He(r)?(i=!0,Ps(t)):i=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,nc(t),o.updater=nl,t.stateNode=o,o._reactInternals=t,Xa(t,r,e,n),t=Ja(null,t,r,!0,i,n)):(t.tag=0,se&&i&&Yu(t),je(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ss(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=lw(r),e=ht(r,e),o){case 0:t=Za(null,t,r,e,n);break e;case 1:t=Kd(null,t,r,e,n);break e;case 11:t=Vd(null,t,r,e,n);break e;case 14:t=Qd(null,t,r,ht(r.type,e),n);break e}throw Error(O(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ht(r,o),Za(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ht(r,o),Kd(e,t,r,o,n);case 3:e:{if(Jh(t),e===null)throw Error(O(387));r=t.pendingProps,i=t.memoizedState,o=i.element,bh(e,t),Os(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){o=Zr(Error(O(423)),t),t=Gd(e,t,r,n,o);break e}else if(r!==o){o=Zr(Error(O(424)),t),t=Gd(e,t,r,n,o);break e}else for(qe=Pn(t.stateNode.containerInfo.firstChild),Ze=t,se=!0,yt=null,n=kh(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Yr(),r===o){t=Gt(e,t,n);break e}je(e,t,r,n)}t=t.child}return t;case 5:return Th(t),e===null&&Ka(t),r=t.type,o=t.pendingProps,i=e!==null?e.memoizedProps:null,s=o.children,Ba(r,o)?s=null:i!==null&&Ba(r,i)&&(t.flags|=32),Zh(e,t),je(e,t,s,n),t.child;case 6:return e===null&&Ka(t),null;case 13:return em(e,t,n);case 4:return rc(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Xr(t,null,r,n):je(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ht(r,o),Vd(e,t,r,o,n);case 7:return je(e,t,t.pendingProps,n),t.child;case 8:return je(e,t,t.pendingProps.children,n),t.child;case 12:return je(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,te(Ns,r._currentValue),r._currentValue=s,i!==null)if(St(i.value,s)){if(i.children===o.children&&!We.current){t=Gt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var l=i.dependencies;if(l!==null){s=i.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(i.tag===1){a=Ht(-1,n&-n),a.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var f=u.pending;f===null?a.next=a:(a.next=f.next,f.next=a),u.pending=a}}i.lanes|=n,a=i.alternate,a!==null&&(a.lanes|=n),Ga(i.return,n,t),l.lanes|=n;break}a=a.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(O(341));s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),Ga(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}je(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Mr(t,n),o=at(o),r=r(o),t.flags|=1,je(e,t,r,n),t.child;case 14:return r=t.type,o=ht(r,t.pendingProps),o=ht(r.type,o),Qd(e,t,r,o,n);case 15:return Xh(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ht(r,o),ss(e,t),t.tag=1,He(r)?(e=!0,Ps(t)):e=!1,Mr(t,n),Kh(t,r,o),Xa(t,r,o,n),Ja(null,t,r,!0,e,n);case 19:return tm(e,t,n);case 22:return qh(e,t,n)}throw Error(O(156,t.tag))};function gm(e,t){return Hp(e,t)}function sw(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function st(e,t,n,r){return new sw(e,t,n,r)}function yc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function lw(e){if(typeof e=="function")return yc(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Du)return 11;if(e===Fu)return 14}return 2}function Rn(e,t){var n=e.alternate;return n===null?(n=st(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function us(e,t,n,r,o,i){var s=2;if(r=e,typeof e=="function")yc(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case yr:return qn(n.children,o,i,t);case Iu:s=8,o|=8;break;case xa:return e=st(12,n,t,o|2),e.elementType=xa,e.lanes=i,e;case Sa:return e=st(13,n,t,o),e.elementType=Sa,e.lanes=i,e;case Ea:return e=st(19,n,t,o),e.elementType=Ea,e.lanes=i,e;case Tp:return il(n,o,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Pp:s=10;break e;case bp:s=9;break e;case Du:s=11;break e;case Fu:s=14;break e;case un:s=16,r=null;break e}throw Error(O(130,e==null?e:typeof e,""))}return t=st(s,n,t,o),t.elementType=e,t.type=r,t.lanes=i,t}function qn(e,t,n,r){return e=st(7,e,r,t),e.lanes=n,e}function il(e,t,n,r){return e=st(22,e,r,t),e.elementType=Tp,e.lanes=n,e.stateNode={isHidden:!1},e}function ea(e,t,n){return e=st(6,e,null,t),e.lanes=n,e}function ta(e,t,n){return t=st(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function aw(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Ll(0),this.expirationTimes=Ll(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ll(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function wc(e,t,n,r,o,i,s,l,a){return e=new aw(e,t,n,l,a),t===1?(t=1,i===!0&&(t|=8)):t=0,i=st(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},nc(i),e}function uw(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:gr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function ym(e){if(!e)return _n;e=e._reactInternals;e:{if(lr(e)!==e||e.tag!==1)throw Error(O(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(He(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(O(171))}if(e.tag===1){var n=e.type;if(He(n))return yh(e,n,t)}return t}function wm(e,t,n,r,o,i,s,l,a){return e=wc(n,r,!0,e,o,i,s,l,a),e.context=ym(null),n=e.current,r=Ie(),o=Nn(n),i=Ht(r,o),i.callback=t??null,bn(n,i,o),e.current.lanes=o,hi(e,o,r),Ve(e,r),e}function sl(e,t,n,r){var o=t.current,i=Ie(),s=Nn(o);return n=ym(n),t.context===null?t.context=n:t.pendingContext=n,t=Ht(i,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=bn(o,t,s),e!==null&&(xt(e,o,s,i),rs(e,o,s)),s}function Fs(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function of(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function xc(e,t){of(e,t),(e=e.alternate)&&of(e,t)}function cw(){return null}var xm=typeof reportError=="function"?reportError:function(e){console.error(e)};function Sc(e){this._internalRoot=e}ll.prototype.render=Sc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(O(409));sl(e,t,null,null)};ll.prototype.unmount=Sc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;rr(function(){sl(null,e,null,null)}),t[Qt]=null}};function ll(e){this._internalRoot=e}ll.prototype.unstable_scheduleHydration=function(e){if(e){var t=qp();e={blockedOn:null,target:e,priority:t};for(var n=0;n<dn.length&&t!==0&&t<dn[n].priority;n++);dn.splice(n,0,e),n===0&&Jp(e)}};function Ec(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function al(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function sf(){}function dw(e,t,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=Fs(s);i.call(u)}}var s=wm(t,r,e,0,null,!1,!1,"",sf);return e._reactRootContainer=s,e[Qt]=s.current,Xo(e.nodeType===8?e.parentNode:e),rr(),s}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var l=r;r=function(){var u=Fs(a);l.call(u)}}var a=wc(e,0,!1,null,null,!1,!1,"",sf);return e._reactRootContainer=a,e[Qt]=a.current,Xo(e.nodeType===8?e.parentNode:e),rr(function(){sl(t,a,n,r)}),a}function ul(e,t,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var l=o;o=function(){var a=Fs(s);l.call(a)}}sl(t,s,e,o)}else s=dw(n,t,e,o,r);return Fs(s)}Yp=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=bo(t.pendingLanes);n!==0&&(Uu(t,n|1),Ve(t,he()),!(X&6)&&(Jr=he()+500,zn()))}break;case 13:rr(function(){var r=Kt(e,1);if(r!==null){var o=Ie();xt(r,e,1,o)}}),xc(e,1)}};Bu=function(e){if(e.tag===13){var t=Kt(e,134217728);if(t!==null){var n=Ie();xt(t,e,134217728,n)}xc(e,134217728)}};Xp=function(e){if(e.tag===13){var t=Nn(e),n=Kt(e,t);if(n!==null){var r=Ie();xt(n,e,t,r)}xc(e,t)}};qp=function(){return J};Zp=function(e,t){var n=J;try{return J=e,t()}finally{J=n}};Aa=function(e,t,n){switch(t){case"input":if(Pa(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Js(r);if(!o)throw Error(O(90));Rp(r),Pa(r,o)}}}break;case"textarea":_p(e,n);break;case"select":t=n.value,t!=null&&Rr(e,!!n.multiple,t,!1)}};Fp=mc;zp=rr;var fw={usingClientEntryPoint:!1,Events:[vi,Er,Js,Ip,Dp,mc]},So={findFiberByHostInstance:Wn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},pw={bundleType:So.bundleType,version:So.version,rendererPackageName:So.rendererPackageName,rendererConfig:So.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:qt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Bp(e),e===null?null:e.stateNode},findFiberByHostInstance:So.findFiberByHostInstance||cw,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ui=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ui.isDisabled&&Ui.supportsFiber)try{Ys=Ui.inject(pw),jt=Ui}catch{}}tt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=fw;tt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ec(t))throw Error(O(200));return uw(e,t,null,n)};tt.createRoot=function(e,t){if(!Ec(e))throw Error(O(299));var n=!1,r="",o=xm;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=wc(e,1,!1,null,null,n,!1,r,o),e[Qt]=t.current,Xo(e.nodeType===8?e.parentNode:e),new Sc(t)};tt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(O(188)):(e=Object.keys(e).join(","),Error(O(268,e)));return e=Bp(t),e=e===null?null:e.stateNode,e};tt.flushSync=function(e){return rr(e)};tt.hydrate=function(e,t,n){if(!al(t))throw Error(O(200));return ul(null,e,t,!0,n)};tt.hydrateRoot=function(e,t,n){if(!Ec(e))throw Error(O(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=xm;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=wm(t,null,e,1,n??null,o,!1,i,s),e[Qt]=t.current,Xo(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new ll(t)};tt.render=function(e,t,n){if(!al(t))throw Error(O(200));return ul(null,e,t,!1,n)};tt.unmountComponentAtNode=function(e){if(!al(e))throw Error(O(40));return e._reactRootContainer?(rr(function(){ul(null,null,e,!1,function(){e._reactRootContainer=null,e[Qt]=null})}),!0):!1};tt.unstable_batchedUpdates=mc;tt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!al(n))throw Error(O(200));if(e==null||e._reactInternals===void 0)throw Error(O(38));return ul(e,t,n,!1,r)};tt.version="18.3.1-next-f1338f8080-20240426";function Sm(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Sm)}catch(e){console.error(e)}}Sm(),Sp.exports=tt;var yi=Sp.exports;const Em=ap(yi);var Cm,lf=yi;Cm=lf.createRoot,lf.hydrateRoot;const hw=1,mw=1e6;let na=0;function vw(){return na=(na+1)%Number.MAX_SAFE_INTEGER,na.toString()}const ra=new Map,af=e=>{if(ra.has(e))return;const t=setTimeout(()=>{ra.delete(e),Uo({type:"REMOVE_TOAST",toastId:e})},mw);ra.set(e,t)},gw=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,hw)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?af(n):e.toasts.forEach(r=>{af(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},cs=[];let ds={toasts:[]};function Uo(e){ds=gw(ds,e),cs.forEach(t=>{t(ds)})}function yw({...e}){const t=vw(),n=o=>Uo({type:"UPDATE_TOAST",toast:{...o,id:t}}),r=()=>Uo({type:"DISMISS_TOAST",toastId:t});return Uo({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||r()}}}),{id:t,dismiss:r,update:n}}function ww(){const[e,t]=m.useState(ds);return m.useEffect(()=>(cs.push(t),()=>{const n=cs.indexOf(t);n>-1&&cs.splice(n,1)}),[e]),{...e,toast:yw,dismiss:n=>Uo({type:"DISMISS_TOAST",toastId:n})}}function le(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function xw(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function km(...e){return t=>e.forEach(n=>xw(n,t))}function De(...e){return m.useCallback(km(...e),e)}function Sw(e,t=[]){let n=[];function r(i,s){const l=m.createContext(s),a=n.length;n=[...n,s];function u(d){const{scope:c,children:y,...x}=d,v=(c==null?void 0:c[e][a])||l,w=m.useMemo(()=>x,Object.values(x));return S.jsx(v.Provider,{value:w,children:y})}function f(d,c){const y=(c==null?void 0:c[e][a])||l,x=m.useContext(y);if(x)return x;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return u.displayName=i+"Provider",[u,f]}const o=()=>{const i=n.map(s=>m.createContext(s));return function(l){const a=(l==null?void 0:l[e])||i;return m.useMemo(()=>({[`__scope${e}`]:{...l,[e]:a}}),[l,a])}};return o.scopeName=e,[r,Ew(o,...t)]}function Ew(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((l,{useScope:a,scopeName:u})=>{const d=a(i)[`__scope${u}`];return{...l,...d}},{});return m.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var eo=m.forwardRef((e,t)=>{const{children:n,...r}=e,o=m.Children.toArray(n),i=o.find(Cw);if(i){const s=i.props.children,l=o.map(a=>a===i?m.Children.count(s)>1?m.Children.only(null):m.isValidElement(s)?s.props.children:null:a);return S.jsx(du,{...r,ref:t,children:m.isValidElement(s)?m.cloneElement(s,void 0,l):null})}return S.jsx(du,{...r,ref:t,children:n})});eo.displayName="Slot";var du=m.forwardRef((e,t)=>{const{children:n,...r}=e;if(m.isValidElement(n)){const o=Pw(n);return m.cloneElement(n,{...kw(r,n.props),ref:t?km(t,o):o})}return m.Children.count(n)>1?m.Children.only(null):null});du.displayName="SlotClone";var Pm=({children:e})=>S.jsx(S.Fragment,{children:e});function Cw(e){return m.isValidElement(e)&&e.type===Pm}function kw(e,t){const n={...t};for(const r in t){const o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...l)=>{i(...l),o(...l)}:o&&(n[r]=o):r==="style"?n[r]={...o,...i}:r==="className"&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}function Pw(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function bw(e){const t=e+"CollectionProvider",[n,r]=Sw(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=y=>{const{scope:x,children:v}=y,w=A.useRef(null),h=A.useRef(new Map).current;return S.jsx(o,{scope:x,itemMap:h,collectionRef:w,children:v})};s.displayName=t;const l=e+"CollectionSlot",a=A.forwardRef((y,x)=>{const{scope:v,children:w}=y,h=i(l,v),p=De(x,h.collectionRef);return S.jsx(eo,{ref:p,children:w})});a.displayName=l;const u=e+"CollectionItemSlot",f="data-radix-collection-item",d=A.forwardRef((y,x)=>{const{scope:v,children:w,...h}=y,p=A.useRef(null),g=De(x,p),E=i(u,v);return A.useEffect(()=>(E.itemMap.set(p,{ref:p,...h}),()=>void E.itemMap.delete(p))),S.jsx(eo,{[f]:"",ref:g,children:w})});d.displayName=u;function c(y){const x=i(e+"CollectionConsumer",y);return A.useCallback(()=>{const w=x.collectionRef.current;if(!w)return[];const h=Array.from(w.querySelectorAll(`[${f}]`));return Array.from(x.itemMap.values()).sort((E,C)=>h.indexOf(E.ref.current)-h.indexOf(C.ref.current))},[x.collectionRef,x.itemMap])}return[{Provider:s,Slot:a,ItemSlot:d},c,r]}function Tw(e,t){const n=m.createContext(t),r=i=>{const{children:s,...l}=i,a=m.useMemo(()=>l,Object.values(l));return S.jsx(n.Provider,{value:a,children:s})};r.displayName=e+"Provider";function o(i){const s=m.useContext(n);if(s)return s;if(t!==void 0)return t;throw new Error(`\`${i}\` must be used within \`${e}\``)}return[r,o]}function Cc(e,t=[]){let n=[];function r(i,s){const l=m.createContext(s),a=n.length;n=[...n,s];const u=d=>{var h;const{scope:c,children:y,...x}=d,v=((h=c==null?void 0:c[e])==null?void 0:h[a])||l,w=m.useMemo(()=>x,Object.values(x));return S.jsx(v.Provider,{value:w,children:y})};u.displayName=i+"Provider";function f(d,c){var v;const y=((v=c==null?void 0:c[e])==null?void 0:v[a])||l,x=m.useContext(y);if(x)return x;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return[u,f]}const o=()=>{const i=n.map(s=>m.createContext(s));return function(l){const a=(l==null?void 0:l[e])||i;return m.useMemo(()=>({[`__scope${e}`]:{...l,[e]:a}}),[l,a])}};return o.scopeName=e,[r,Nw(o,...t)]}function Nw(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((l,{useScope:a,scopeName:u})=>{const d=a(i)[`__scope${u}`];return{...l,...d}},{});return m.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}var Rw=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],me=Rw.reduce((e,t)=>{const n=m.forwardRef((r,o)=>{const{asChild:i,...s}=r,l=i?eo:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),S.jsx(l,{...s,ref:o})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function bm(e,t){e&&yi.flushSync(()=>e.dispatchEvent(t))}function ct(e){const t=m.useRef(e);return m.useEffect(()=>{t.current=e}),m.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function Ow(e,t=globalThis==null?void 0:globalThis.document){const n=ct(e);m.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var _w="DismissableLayer",fu="dismissableLayer.update",Aw="dismissableLayer.pointerDownOutside",Mw="dismissableLayer.focusOutside",uf,Tm=m.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),cl=m.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:i,onInteractOutside:s,onDismiss:l,...a}=e,u=m.useContext(Tm),[f,d]=m.useState(null),c=(f==null?void 0:f.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,y]=m.useState({}),x=De(t,b=>d(b)),v=Array.from(u.layers),[w]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),h=v.indexOf(w),p=f?v.indexOf(f):-1,g=u.layersWithOutsidePointerEventsDisabled.size>0,E=p>=h,C=Lw(b=>{const T=b.target,D=[...u.branches].some(R=>R.contains(T));!E||D||(o==null||o(b),s==null||s(b),b.defaultPrevented||l==null||l())},c),P=Iw(b=>{const T=b.target;[...u.branches].some(R=>R.contains(T))||(i==null||i(b),s==null||s(b),b.defaultPrevented||l==null||l())},c);return Ow(b=>{p===u.layers.size-1&&(r==null||r(b),!b.defaultPrevented&&l&&(b.preventDefault(),l()))},c),m.useEffect(()=>{if(f)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(uf=c.body.style.pointerEvents,c.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(f)),u.layers.add(f),cf(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(c.body.style.pointerEvents=uf)}},[f,c,n,u]),m.useEffect(()=>()=>{f&&(u.layers.delete(f),u.layersWithOutsidePointerEventsDisabled.delete(f),cf())},[f,u]),m.useEffect(()=>{const b=()=>y({});return document.addEventListener(fu,b),()=>document.removeEventListener(fu,b)},[]),S.jsx(me.div,{...a,ref:x,style:{pointerEvents:g?E?"auto":"none":void 0,...e.style},onFocusCapture:le(e.onFocusCapture,P.onFocusCapture),onBlurCapture:le(e.onBlurCapture,P.onBlurCapture),onPointerDownCapture:le(e.onPointerDownCapture,C.onPointerDownCapture)})});cl.displayName=_w;var jw="DismissableLayerBranch",Nm=m.forwardRef((e,t)=>{const n=m.useContext(Tm),r=m.useRef(null),o=De(t,r);return m.useEffect(()=>{const i=r.current;if(i)return n.branches.add(i),()=>{n.branches.delete(i)}},[n.branches]),S.jsx(me.div,{...e,ref:o})});Nm.displayName=jw;function Lw(e,t=globalThis==null?void 0:globalThis.document){const n=ct(e),r=m.useRef(!1),o=m.useRef(()=>{});return m.useEffect(()=>{const i=l=>{if(l.target&&!r.current){let a=function(){Rm(Aw,n,u,{discrete:!0})};const u={originalEvent:l};l.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=a,t.addEventListener("click",o.current,{once:!0})):a()}else t.removeEventListener("click",o.current);r.current=!1},s=window.setTimeout(()=>{t.addEventListener("pointerdown",i)},0);return()=>{window.clearTimeout(s),t.removeEventListener("pointerdown",i),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Iw(e,t=globalThis==null?void 0:globalThis.document){const n=ct(e),r=m.useRef(!1);return m.useEffect(()=>{const o=i=>{i.target&&!r.current&&Rm(Mw,n,{originalEvent:i},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function cf(){const e=new CustomEvent(fu);document.dispatchEvent(e)}function Rm(e,t,n,{discrete:r}){const o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?bm(o,i):o.dispatchEvent(i)}var Dw=cl,Fw=Nm,An=globalThis!=null&&globalThis.document?m.useLayoutEffect:()=>{},zw="Portal",kc=m.forwardRef((e,t)=>{var l;const{container:n,...r}=e,[o,i]=m.useState(!1);An(()=>i(!0),[]);const s=n||o&&((l=globalThis==null?void 0:globalThis.document)==null?void 0:l.body);return s?Em.createPortal(S.jsx(me.div,{...r,ref:t}),s):null});kc.displayName=zw;function $w(e,t){return m.useReducer((n,r)=>t[n][r]??n,e)}var so=e=>{const{present:t,children:n}=e,r=Uw(t),o=typeof n=="function"?n({present:r.isPresent}):m.Children.only(n),i=De(r.ref,Bw(o));return typeof n=="function"||r.isPresent?m.cloneElement(o,{ref:i}):null};so.displayName="Presence";function Uw(e){const[t,n]=m.useState(),r=m.useRef({}),o=m.useRef(e),i=m.useRef("none"),s=e?"mounted":"unmounted",[l,a]=$w(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return m.useEffect(()=>{const u=Bi(r.current);i.current=l==="mounted"?u:"none"},[l]),An(()=>{const u=r.current,f=o.current;if(f!==e){const c=i.current,y=Bi(u);e?a("MOUNT"):y==="none"||(u==null?void 0:u.display)==="none"?a("UNMOUNT"):a(f&&c!==y?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,a]),An(()=>{if(t){let u;const f=t.ownerDocument.defaultView??window,d=y=>{const v=Bi(r.current).includes(y.animationName);if(y.target===t&&v&&(a("ANIMATION_END"),!o.current)){const w=t.style.animationFillMode;t.style.animationFillMode="forwards",u=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=w)})}},c=y=>{y.target===t&&(i.current=Bi(r.current))};return t.addEventListener("animationstart",c),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{f.clearTimeout(u),t.removeEventListener("animationstart",c),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else a("ANIMATION_END")},[t,a]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:m.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function Bi(e){return(e==null?void 0:e.animationName)||"none"}function Bw(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Om({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=Ww({defaultProp:t,onChange:n}),i=e!==void 0,s=i?e:r,l=ct(n),a=m.useCallback(u=>{if(i){const d=typeof u=="function"?u(e):u;d!==e&&l(d)}else o(u)},[i,e,o,l]);return[s,a]}function Ww({defaultProp:e,onChange:t}){const n=m.useState(e),[r]=n,o=m.useRef(r),i=ct(t);return m.useEffect(()=>{o.current!==r&&(i(r),o.current=r)},[r,o,i]),n}var Hw="VisuallyHidden",dl=m.forwardRef((e,t)=>S.jsx(me.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));dl.displayName=Hw;var Vw=dl,Pc="ToastProvider",[bc,Qw,Kw]=bw("Toast"),[_m,Uk]=Cc("Toast",[Kw]),[Gw,fl]=_m(Pc),Am=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:i=50,children:s}=e,[l,a]=m.useState(null),[u,f]=m.useState(0),d=m.useRef(!1),c=m.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${Pc}\`. Expected non-empty \`string\`.`),S.jsx(bc.Provider,{scope:t,children:S.jsx(Gw,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:i,toastCount:u,viewport:l,onViewportChange:a,onToastAdd:m.useCallback(()=>f(y=>y+1),[]),onToastRemove:m.useCallback(()=>f(y=>y-1),[]),isFocusedToastEscapeKeyDownRef:d,isClosePausedRef:c,children:s})})};Am.displayName=Pc;var Mm="ToastViewport",Yw=["F8"],pu="toast.viewportPause",hu="toast.viewportResume",jm=m.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=Yw,label:o="Notifications ({hotkey})",...i}=e,s=fl(Mm,n),l=Qw(n),a=m.useRef(null),u=m.useRef(null),f=m.useRef(null),d=m.useRef(null),c=De(t,d,s.onViewportChange),y=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),x=s.toastCount>0;m.useEffect(()=>{const w=h=>{var g;r.length!==0&&r.every(E=>h[E]||h.code===E)&&((g=d.current)==null||g.focus())};return document.addEventListener("keydown",w),()=>document.removeEventListener("keydown",w)},[r]),m.useEffect(()=>{const w=a.current,h=d.current;if(x&&w&&h){const p=()=>{if(!s.isClosePausedRef.current){const P=new CustomEvent(pu);h.dispatchEvent(P),s.isClosePausedRef.current=!0}},g=()=>{if(s.isClosePausedRef.current){const P=new CustomEvent(hu);h.dispatchEvent(P),s.isClosePausedRef.current=!1}},E=P=>{!w.contains(P.relatedTarget)&&g()},C=()=>{w.contains(document.activeElement)||g()};return w.addEventListener("focusin",p),w.addEventListener("focusout",E),w.addEventListener("pointermove",p),w.addEventListener("pointerleave",C),window.addEventListener("blur",p),window.addEventListener("focus",g),()=>{w.removeEventListener("focusin",p),w.removeEventListener("focusout",E),w.removeEventListener("pointermove",p),w.removeEventListener("pointerleave",C),window.removeEventListener("blur",p),window.removeEventListener("focus",g)}}},[x,s.isClosePausedRef]);const v=m.useCallback(({tabbingDirection:w})=>{const p=l().map(g=>{const E=g.ref.current,C=[E,...a1(E)];return w==="forwards"?C:C.reverse()});return(w==="forwards"?p.reverse():p).flat()},[l]);return m.useEffect(()=>{const w=d.current;if(w){const h=p=>{var C,P,b;const g=p.altKey||p.ctrlKey||p.metaKey;if(p.key==="Tab"&&!g){const T=document.activeElement,D=p.shiftKey;if(p.target===w&&D){(C=u.current)==null||C.focus();return}const L=v({tabbingDirection:D?"backwards":"forwards"}),B=L.findIndex(M=>M===T);oa(L.slice(B+1))?p.preventDefault():D?(P=u.current)==null||P.focus():(b=f.current)==null||b.focus()}};return w.addEventListener("keydown",h),()=>w.removeEventListener("keydown",h)}},[l,v]),S.jsxs(Fw,{ref:a,role:"region","aria-label":o.replace("{hotkey}",y),tabIndex:-1,style:{pointerEvents:x?void 0:"none"},children:[x&&S.jsx(mu,{ref:u,onFocusFromOutsideViewport:()=>{const w=v({tabbingDirection:"forwards"});oa(w)}}),S.jsx(bc.Slot,{scope:n,children:S.jsx(me.ol,{tabIndex:-1,...i,ref:c})}),x&&S.jsx(mu,{ref:f,onFocusFromOutsideViewport:()=>{const w=v({tabbingDirection:"backwards"});oa(w)}})]})});jm.displayName=Mm;var Lm="ToastFocusProxy",mu=m.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,i=fl(Lm,n);return S.jsx(dl,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:s=>{var u;const l=s.relatedTarget;!((u=i.viewport)!=null&&u.contains(l))&&r()}})});mu.displayName=Lm;var pl="Toast",Xw="toast.swipeStart",qw="toast.swipeMove",Zw="toast.swipeCancel",Jw="toast.swipeEnd",Im=m.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:i,...s}=e,[l=!0,a]=Om({prop:r,defaultProp:o,onChange:i});return S.jsx(so,{present:n||l,children:S.jsx(n1,{open:l,...s,ref:t,onClose:()=>a(!1),onPause:ct(e.onPause),onResume:ct(e.onResume),onSwipeStart:le(e.onSwipeStart,u=>{u.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:le(e.onSwipeMove,u=>{const{x:f,y:d}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","move"),u.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${f}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${d}px`)}),onSwipeCancel:le(e.onSwipeCancel,u=>{u.currentTarget.setAttribute("data-swipe","cancel"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:le(e.onSwipeEnd,u=>{const{x:f,y:d}=u.detail.delta;u.currentTarget.setAttribute("data-swipe","end"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),u.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),u.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${f}px`),u.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${d}px`),a(!1)})})})});Im.displayName=pl;var[e1,t1]=_m(pl,{onClose(){}}),n1=m.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:i,onClose:s,onEscapeKeyDown:l,onPause:a,onResume:u,onSwipeStart:f,onSwipeMove:d,onSwipeCancel:c,onSwipeEnd:y,...x}=e,v=fl(pl,n),[w,h]=m.useState(null),p=De(t,M=>h(M)),g=m.useRef(null),E=m.useRef(null),C=o||v.duration,P=m.useRef(0),b=m.useRef(C),T=m.useRef(0),{onToastAdd:D,onToastRemove:R}=v,j=ct(()=>{var H;(w==null?void 0:w.contains(document.activeElement))&&((H=v.viewport)==null||H.focus()),s()}),L=m.useCallback(M=>{!M||M===1/0||(window.clearTimeout(T.current),P.current=new Date().getTime(),T.current=window.setTimeout(j,M))},[j]);m.useEffect(()=>{const M=v.viewport;if(M){const H=()=>{L(b.current),u==null||u()},U=()=>{const W=new Date().getTime()-P.current;b.current=b.current-W,window.clearTimeout(T.current),a==null||a()};return M.addEventListener(pu,U),M.addEventListener(hu,H),()=>{M.removeEventListener(pu,U),M.removeEventListener(hu,H)}}},[v.viewport,C,a,u,L]),m.useEffect(()=>{i&&!v.isClosePausedRef.current&&L(C)},[i,C,v.isClosePausedRef,L]),m.useEffect(()=>(D(),()=>R()),[D,R]);const B=m.useMemo(()=>w?Wm(w):null,[w]);return v.viewport?S.jsxs(S.Fragment,{children:[B&&S.jsx(r1,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:B}),S.jsx(e1,{scope:n,onClose:j,children:yi.createPortal(S.jsx(bc.ItemSlot,{scope:n,children:S.jsx(Dw,{asChild:!0,onEscapeKeyDown:le(l,()=>{v.isFocusedToastEscapeKeyDownRef.current||j(),v.isFocusedToastEscapeKeyDownRef.current=!1}),children:S.jsx(me.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":i?"open":"closed","data-swipe-direction":v.swipeDirection,...x,ref:p,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:le(e.onKeyDown,M=>{M.key==="Escape"&&(l==null||l(M.nativeEvent),M.nativeEvent.defaultPrevented||(v.isFocusedToastEscapeKeyDownRef.current=!0,j()))}),onPointerDown:le(e.onPointerDown,M=>{M.button===0&&(g.current={x:M.clientX,y:M.clientY})}),onPointerMove:le(e.onPointerMove,M=>{if(!g.current)return;const H=M.clientX-g.current.x,U=M.clientY-g.current.y,W=!!E.current,k=["left","right"].includes(v.swipeDirection),_=["left","up"].includes(v.swipeDirection)?Math.min:Math.max,I=k?_(0,H):0,F=k?0:_(0,U),$=M.pointerType==="touch"?10:2,Y={x:I,y:F},ae={originalEvent:M,delta:Y};W?(E.current=Y,Wi(qw,d,ae,{discrete:!1})):df(Y,v.swipeDirection,$)?(E.current=Y,Wi(Xw,f,ae,{discrete:!1}),M.target.setPointerCapture(M.pointerId)):(Math.abs(H)>$||Math.abs(U)>$)&&(g.current=null)}),onPointerUp:le(e.onPointerUp,M=>{const H=E.current,U=M.target;if(U.hasPointerCapture(M.pointerId)&&U.releasePointerCapture(M.pointerId),E.current=null,g.current=null,H){const W=M.currentTarget,k={originalEvent:M,delta:H};df(H,v.swipeDirection,v.swipeThreshold)?Wi(Jw,y,k,{discrete:!0}):Wi(Zw,c,k,{discrete:!0}),W.addEventListener("click",_=>_.preventDefault(),{once:!0})}})})})}),v.viewport)})]}):null}),r1=e=>{const{__scopeToast:t,children:n,...r}=e,o=fl(pl,t),[i,s]=m.useState(!1),[l,a]=m.useState(!1);return s1(()=>s(!0)),m.useEffect(()=>{const u=window.setTimeout(()=>a(!0),1e3);return()=>window.clearTimeout(u)},[]),l?null:S.jsx(kc,{asChild:!0,children:S.jsx(dl,{...r,children:i&&S.jsxs(S.Fragment,{children:[o.label," ",n]})})})},o1="ToastTitle",Dm=m.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return S.jsx(me.div,{...r,ref:t})});Dm.displayName=o1;var i1="ToastDescription",Fm=m.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return S.jsx(me.div,{...r,ref:t})});Fm.displayName=i1;var zm="ToastAction",$m=m.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?S.jsx(Bm,{altText:n,asChild:!0,children:S.jsx(Tc,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${zm}\`. Expected non-empty \`string\`.`),null)});$m.displayName=zm;var Um="ToastClose",Tc=m.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=t1(Um,n);return S.jsx(Bm,{asChild:!0,children:S.jsx(me.button,{type:"button",...r,ref:t,onClick:le(e.onClick,o.onClose)})})});Tc.displayName=Um;var Bm=m.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return S.jsx(me.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function Wm(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),l1(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",i=r.dataset.radixToastAnnounceExclude==="";if(!o)if(i){const s=r.dataset.radixToastAnnounceAlt;s&&t.push(s)}else t.push(...Wm(r))}}),t}function Wi(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?bm(o,i):o.dispatchEvent(i)}var df=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),i=r>o;return t==="left"||t==="right"?i&&r>n:!i&&o>n};function s1(e=()=>{}){const t=ct(e);An(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function l1(e){return e.nodeType===e.ELEMENT_NODE}function a1(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function oa(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var u1=Am,Hm=jm,Vm=Im,Qm=Dm,Km=Fm,Gm=$m,Ym=Tc;function Xm(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=Xm(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function qm(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=Xm(e))&&(r&&(r+=" "),r+=t);return r}const ff=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,pf=qm,Nc=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return pf(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:i}=t,s=Object.keys(o).map(u=>{const f=n==null?void 0:n[u],d=i==null?void 0:i[u];if(f===null)return null;const c=ff(f)||ff(d);return o[u][c]}),l=n&&Object.entries(n).reduce((u,f)=>{let[d,c]=f;return c===void 0||(u[d]=c),u},{}),a=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,f)=>{let{class:d,className:c,...y}=f;return Object.entries(y).every(x=>{let[v,w]=x;return Array.isArray(w)?w.includes({...i,...l}[v]):{...i,...l}[v]===w})?[...u,d,c]:u},[]);return pf(e,s,a,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c1=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Zm=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var d1={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f1=m.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:i,iconNode:s,...l},a)=>m.createElement("svg",{ref:a,...d1,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Zm("lucide",o),...l},[...s.map(([u,f])=>m.createElement(u,f)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ar=(e,t)=>{const n=m.forwardRef(({className:r,...o},i)=>m.createElement(f1,{ref:i,iconNode:t,className:Zm(`lucide-${c1(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p1=ar("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h1=ar("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m1=ar("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hf=ar("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v1=ar("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const g1=ar("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jm=ar("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Rc="-",y1=e=>{const t=x1(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:s=>{const l=s.split(Rc);return l[0]===""&&l.length!==1&&l.shift(),ev(l,t)||w1(s)},getConflictingClassGroupIds:(s,l)=>{const a=n[s]||[];return l&&r[s]?[...a,...r[s]]:a}}},ev=(e,t)=>{var s;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?ev(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const i=e.join(Rc);return(s=t.validators.find(({validator:l})=>l(i)))==null?void 0:s.classGroupId},mf=/^\[(.+)\]$/,w1=e=>{if(mf.test(e)){const t=mf.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},x1=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return E1(Object.entries(e.classGroups),n).forEach(([i,s])=>{vu(s,r,i,t)}),r},vu=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const i=o===""?t:vf(t,o);i.classGroupId=n;return}if(typeof o=="function"){if(S1(o)){vu(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([i,s])=>{vu(s,vf(t,i),n,r)})})},vf=(e,t)=>{let n=e;return t.split(Rc).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},S1=e=>e.isThemeGetter,E1=(e,t)=>t?e.map(([n,r])=>{const o=r.map(i=>typeof i=="string"?t+i:typeof i=="object"?Object.fromEntries(Object.entries(i).map(([s,l])=>[t+s,l])):i);return[n,o]}):e,C1=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(i,s)=>{n.set(i,s),t++,t>e&&(t=0,r=n,n=new Map)};return{get(i){let s=n.get(i);if(s!==void 0)return s;if((s=r.get(i))!==void 0)return o(i,s),s},set(i,s){n.has(i)?n.set(i,s):o(i,s)}}},tv="!",k1=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],i=t.length,s=l=>{const a=[];let u=0,f=0,d;for(let w=0;w<l.length;w++){let h=l[w];if(u===0){if(h===o&&(r||l.slice(w,w+i)===t)){a.push(l.slice(f,w)),f=w+i;continue}if(h==="/"){d=w;continue}}h==="["?u++:h==="]"&&u--}const c=a.length===0?l:l.substring(f),y=c.startsWith(tv),x=y?c.substring(1):c,v=d&&d>f?d-f:void 0;return{modifiers:a,hasImportantModifier:y,baseClassName:x,maybePostfixModifierPosition:v}};return n?l=>n({className:l,parseClassName:s}):s},P1=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},b1=e=>({cache:C1(e.cacheSize),parseClassName:k1(e),...y1(e)}),T1=/\s+/,N1=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,i=[],s=e.trim().split(T1);let l="";for(let a=s.length-1;a>=0;a-=1){const u=s[a],{modifiers:f,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:y}=n(u);let x=!!y,v=r(x?c.substring(0,y):c);if(!v){if(!x){l=u+(l.length>0?" "+l:l);continue}if(v=r(c),!v){l=u+(l.length>0?" "+l:l);continue}x=!1}const w=P1(f).join(":"),h=d?w+tv:w,p=h+v;if(i.includes(p))continue;i.push(p);const g=o(v,x);for(let E=0;E<g.length;++E){const C=g[E];i.push(h+C)}l=u+(l.length>0?" "+l:l)}return l};function R1(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=nv(t))&&(r&&(r+=" "),r+=n);return r}const nv=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=nv(e[r]))&&(n&&(n+=" "),n+=t);return n};function O1(e,...t){let n,r,o,i=s;function s(a){const u=t.reduce((f,d)=>d(f),e());return n=b1(u),r=n.cache.get,o=n.cache.set,i=l,l(a)}function l(a){const u=r(a);if(u)return u;const f=N1(a,n);return o(a,f),f}return function(){return i(R1.apply(null,arguments))}}const re=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},rv=/^\[(?:([a-z-]+):)?(.+)\]$/i,_1=/^\d+\/\d+$/,A1=new Set(["px","full","screen"]),M1=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,j1=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,L1=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,I1=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,D1=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ft=e=>Lr(e)||A1.has(e)||_1.test(e),on=e=>lo(e,"length",V1),Lr=e=>!!e&&!Number.isNaN(Number(e)),ia=e=>lo(e,"number",Lr),Eo=e=>!!e&&Number.isInteger(Number(e)),F1=e=>e.endsWith("%")&&Lr(e.slice(0,-1)),Q=e=>rv.test(e),sn=e=>M1.test(e),z1=new Set(["length","size","percentage"]),$1=e=>lo(e,z1,ov),U1=e=>lo(e,"position",ov),B1=new Set(["image","url"]),W1=e=>lo(e,B1,K1),H1=e=>lo(e,"",Q1),Co=()=>!0,lo=(e,t,n)=>{const r=rv.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},V1=e=>j1.test(e)&&!L1.test(e),ov=()=>!1,Q1=e=>I1.test(e),K1=e=>D1.test(e),G1=()=>{const e=re("colors"),t=re("spacing"),n=re("blur"),r=re("brightness"),o=re("borderColor"),i=re("borderRadius"),s=re("borderSpacing"),l=re("borderWidth"),a=re("contrast"),u=re("grayscale"),f=re("hueRotate"),d=re("invert"),c=re("gap"),y=re("gradientColorStops"),x=re("gradientColorStopPositions"),v=re("inset"),w=re("margin"),h=re("opacity"),p=re("padding"),g=re("saturate"),E=re("scale"),C=re("sepia"),P=re("skew"),b=re("space"),T=re("translate"),D=()=>["auto","contain","none"],R=()=>["auto","hidden","clip","visible","scroll"],j=()=>["auto",Q,t],L=()=>[Q,t],B=()=>["",Ft,on],M=()=>["auto",Lr,Q],H=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],U=()=>["solid","dashed","dotted","double","none"],W=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],k=()=>["start","end","center","between","around","evenly","stretch"],_=()=>["","0",Q],I=()=>["auto","avoid","all","avoid-page","page","left","right","column"],F=()=>[Lr,Q];return{cacheSize:500,separator:":",theme:{colors:[Co],spacing:[Ft,on],blur:["none","",sn,Q],brightness:F(),borderColor:[e],borderRadius:["none","","full",sn,Q],borderSpacing:L(),borderWidth:B(),contrast:F(),grayscale:_(),hueRotate:F(),invert:_(),gap:L(),gradientColorStops:[e],gradientColorStopPositions:[F1,on],inset:j(),margin:j(),opacity:F(),padding:L(),saturate:F(),scale:F(),sepia:_(),skew:F(),space:L(),translate:L()},classGroups:{aspect:[{aspect:["auto","square","video",Q]}],container:["container"],columns:[{columns:[sn]}],"break-after":[{"break-after":I()}],"break-before":[{"break-before":I()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...H(),Q]}],overflow:[{overflow:R()}],"overflow-x":[{"overflow-x":R()}],"overflow-y":[{"overflow-y":R()}],overscroll:[{overscroll:D()}],"overscroll-x":[{"overscroll-x":D()}],"overscroll-y":[{"overscroll-y":D()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[v]}],"inset-x":[{"inset-x":[v]}],"inset-y":[{"inset-y":[v]}],start:[{start:[v]}],end:[{end:[v]}],top:[{top:[v]}],right:[{right:[v]}],bottom:[{bottom:[v]}],left:[{left:[v]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Eo,Q]}],basis:[{basis:j()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",Q]}],grow:[{grow:_()}],shrink:[{shrink:_()}],order:[{order:["first","last","none",Eo,Q]}],"grid-cols":[{"grid-cols":[Co]}],"col-start-end":[{col:["auto",{span:["full",Eo,Q]},Q]}],"col-start":[{"col-start":M()}],"col-end":[{"col-end":M()}],"grid-rows":[{"grid-rows":[Co]}],"row-start-end":[{row:["auto",{span:[Eo,Q]},Q]}],"row-start":[{"row-start":M()}],"row-end":[{"row-end":M()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",Q]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",Q]}],gap:[{gap:[c]}],"gap-x":[{"gap-x":[c]}],"gap-y":[{"gap-y":[c]}],"justify-content":[{justify:["normal",...k()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...k(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...k(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[p]}],px:[{px:[p]}],py:[{py:[p]}],ps:[{ps:[p]}],pe:[{pe:[p]}],pt:[{pt:[p]}],pr:[{pr:[p]}],pb:[{pb:[p]}],pl:[{pl:[p]}],m:[{m:[w]}],mx:[{mx:[w]}],my:[{my:[w]}],ms:[{ms:[w]}],me:[{me:[w]}],mt:[{mt:[w]}],mr:[{mr:[w]}],mb:[{mb:[w]}],ml:[{ml:[w]}],"space-x":[{"space-x":[b]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[b]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",Q,t]}],"min-w":[{"min-w":[Q,t,"min","max","fit"]}],"max-w":[{"max-w":[Q,t,"none","full","min","max","fit","prose",{screen:[sn]},sn]}],h:[{h:[Q,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[Q,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[Q,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[Q,t,"auto","min","max","fit"]}],"font-size":[{text:["base",sn,on]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",ia]}],"font-family":[{font:[Co]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",Q]}],"line-clamp":[{"line-clamp":["none",Lr,ia]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Ft,Q]}],"list-image":[{"list-image":["none",Q]}],"list-style-type":[{list:["none","disc","decimal",Q]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[h]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[h]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...U(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Ft,on]}],"underline-offset":[{"underline-offset":["auto",Ft,Q]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:L()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Q]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Q]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[h]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...H(),U1]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",$1]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},W1]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[x]}],"gradient-via-pos":[{via:[x]}],"gradient-to-pos":[{to:[x]}],"gradient-from":[{from:[y]}],"gradient-via":[{via:[y]}],"gradient-to":[{to:[y]}],rounded:[{rounded:[i]}],"rounded-s":[{"rounded-s":[i]}],"rounded-e":[{"rounded-e":[i]}],"rounded-t":[{"rounded-t":[i]}],"rounded-r":[{"rounded-r":[i]}],"rounded-b":[{"rounded-b":[i]}],"rounded-l":[{"rounded-l":[i]}],"rounded-ss":[{"rounded-ss":[i]}],"rounded-se":[{"rounded-se":[i]}],"rounded-ee":[{"rounded-ee":[i]}],"rounded-es":[{"rounded-es":[i]}],"rounded-tl":[{"rounded-tl":[i]}],"rounded-tr":[{"rounded-tr":[i]}],"rounded-br":[{"rounded-br":[i]}],"rounded-bl":[{"rounded-bl":[i]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[h]}],"border-style":[{border:[...U(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[h]}],"divide-style":[{divide:U()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...U()]}],"outline-offset":[{"outline-offset":[Ft,Q]}],"outline-w":[{outline:[Ft,on]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:B()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[h]}],"ring-offset-w":[{"ring-offset":[Ft,on]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",sn,H1]}],"shadow-color":[{shadow:[Co]}],opacity:[{opacity:[h]}],"mix-blend":[{"mix-blend":[...W(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":W()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",sn,Q]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[f]}],invert:[{invert:[d]}],saturate:[{saturate:[g]}],sepia:[{sepia:[C]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[f]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[h]}],"backdrop-saturate":[{"backdrop-saturate":[g]}],"backdrop-sepia":[{"backdrop-sepia":[C]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",Q]}],duration:[{duration:F()}],ease:[{ease:["linear","in","out","in-out",Q]}],delay:[{delay:F()}],animate:[{animate:["none","spin","ping","pulse","bounce",Q]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[E]}],"scale-x":[{"scale-x":[E]}],"scale-y":[{"scale-y":[E]}],rotate:[{rotate:[Eo,Q]}],"translate-x":[{"translate-x":[T]}],"translate-y":[{"translate-y":[T]}],"skew-x":[{"skew-x":[P]}],"skew-y":[{"skew-y":[P]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",Q]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Q]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":L()}],"scroll-mx":[{"scroll-mx":L()}],"scroll-my":[{"scroll-my":L()}],"scroll-ms":[{"scroll-ms":L()}],"scroll-me":[{"scroll-me":L()}],"scroll-mt":[{"scroll-mt":L()}],"scroll-mr":[{"scroll-mr":L()}],"scroll-mb":[{"scroll-mb":L()}],"scroll-ml":[{"scroll-ml":L()}],"scroll-p":[{"scroll-p":L()}],"scroll-px":[{"scroll-px":L()}],"scroll-py":[{"scroll-py":L()}],"scroll-ps":[{"scroll-ps":L()}],"scroll-pe":[{"scroll-pe":L()}],"scroll-pt":[{"scroll-pt":L()}],"scroll-pr":[{"scroll-pr":L()}],"scroll-pb":[{"scroll-pb":L()}],"scroll-pl":[{"scroll-pl":L()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Q]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Ft,on,ia]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},Y1=O1(G1);function ve(...e){return Y1(qm(e))}const X1=u1,iv=m.forwardRef(({className:e,...t},n)=>S.jsx(Hm,{ref:n,className:ve("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));iv.displayName=Hm.displayName;const q1=Nc("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),sv=m.forwardRef(({className:e,variant:t,...n},r)=>S.jsx(Vm,{ref:r,className:ve(q1({variant:t}),e),...n}));sv.displayName=Vm.displayName;const Z1=m.forwardRef(({className:e,...t},n)=>S.jsx(Gm,{ref:n,className:ve("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));Z1.displayName=Gm.displayName;const lv=m.forwardRef(({className:e,...t},n)=>S.jsx(Ym,{ref:n,className:ve("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:S.jsx(Jm,{className:"h-4 w-4"})}));lv.displayName=Ym.displayName;const av=m.forwardRef(({className:e,...t},n)=>S.jsx(Qm,{ref:n,className:ve("text-sm font-semibold",e),...t}));av.displayName=Qm.displayName;const uv=m.forwardRef(({className:e,...t},n)=>S.jsx(Km,{ref:n,className:ve("text-sm opacity-90",e),...t}));uv.displayName=Km.displayName;function J1(){const{toasts:e}=ww();return S.jsxs(X1,{children:[e.map(function({id:t,title:n,description:r,action:o,...i}){return S.jsxs(sv,{...i,children:[S.jsxs("div",{className:"grid gap-1",children:[n&&S.jsx(av,{children:n}),r&&S.jsx(uv,{children:r})]}),o,S.jsx(lv,{})]},t)}),S.jsx(iv,{})]})}var gf=["light","dark"],ex="(prefers-color-scheme: dark)",tx=m.createContext(void 0),nx={setTheme:e=>{},themes:[]},rx=()=>{var e;return(e=m.useContext(tx))!=null?e:nx};m.memo(({forcedTheme:e,storageKey:t,attribute:n,enableSystem:r,enableColorScheme:o,defaultTheme:i,value:s,attrs:l,nonce:a})=>{let u=i==="system",f=n==="class"?`var d=document.documentElement,c=d.classList;${`c.remove(${l.map(x=>`'${x}'`).join(",")})`};`:`var d=document.documentElement,n='${n}',s='setAttribute';`,d=o?gf.includes(i)&&i?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${i}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",c=(x,v=!1,w=!0)=>{let h=s?s[x]:x,p=v?x+"|| ''":`'${h}'`,g="";return o&&w&&!v&&gf.includes(x)&&(g+=`d.style.colorScheme = '${x}';`),n==="class"?v||h?g+=`c.add(${p})`:g+="null":h&&(g+=`d[s](n,${p})`),g},y=e?`!function(){${f}${c(e)}}()`:r?`!function(){try{${f}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${u})){var t='${ex}',m=window.matchMedia(t);if(m.media!==t||m.matches){${c("dark")}}else{${c("light")}}}else if(e){${s?`var x=${JSON.stringify(s)};`:""}${c(s?"x[e]":"e",!0)}}${u?"":"else{"+c(i,!1,!1)+"}"}${d}}catch(e){}}()`:`!function(){try{${f}var e=localStorage.getItem('${t}');if(e){${s?`var x=${JSON.stringify(s)};`:""}${c(s?"x[e]":"e",!0)}}else{${c(i,!1,!1)};}${d}}catch(t){}}();`;return m.createElement("script",{nonce:a,dangerouslySetInnerHTML:{__html:y}})});var ox=e=>{switch(e){case"success":return lx;case"info":return ux;case"warning":return ax;case"error":return cx;default:return null}},ix=Array(12).fill(0),sx=({visible:e})=>A.createElement("div",{className:"sonner-loading-wrapper","data-visible":e},A.createElement("div",{className:"sonner-spinner"},ix.map((t,n)=>A.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${n}`})))),lx=A.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},A.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),ax=A.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},A.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),ux=A.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},A.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),cx=A.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},A.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),dx=()=>{let[e,t]=A.useState(document.hidden);return A.useEffect(()=>{let n=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",n),()=>window.removeEventListener("visibilitychange",n)},[]),e},gu=1,fx=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:n,...r}=e,o=typeof(e==null?void 0:e.id)=="number"||((t=e.id)==null?void 0:t.length)>0?e.id:gu++,i=this.toasts.find(l=>l.id===o),s=e.dismissible===void 0?!0:e.dismissible;return i?this.toasts=this.toasts.map(l=>l.id===o?(this.publish({...l,...e,id:o,title:n}),{...l,...e,id:o,dismissible:s,title:n}):l):this.addToast({title:n,...r,dismissible:s,id:o}),o},this.dismiss=e=>(e||this.toasts.forEach(t=>{this.subscribers.forEach(n=>n({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{if(!t)return;let n;t.loading!==void 0&&(n=this.create({...t,promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0}));let r=e instanceof Promise?e:e(),o=n!==void 0;return r.then(async i=>{if(hx(i)&&!i.ok){o=!1;let s=typeof t.error=="function"?await t.error(`HTTP error! status: ${i.status}`):t.error,l=typeof t.description=="function"?await t.description(`HTTP error! status: ${i.status}`):t.description;this.create({id:n,type:"error",message:s,description:l})}else if(t.success!==void 0){o=!1;let s=typeof t.success=="function"?await t.success(i):t.success,l=typeof t.description=="function"?await t.description(i):t.description;this.create({id:n,type:"success",message:s,description:l})}}).catch(async i=>{if(t.error!==void 0){o=!1;let s=typeof t.error=="function"?await t.error(i):t.error,l=typeof t.description=="function"?await t.description(i):t.description;this.create({id:n,type:"error",message:s,description:l})}}).finally(()=>{var i;o&&(this.dismiss(n),n=void 0),(i=t.finally)==null||i.call(t)}),n},this.custom=(e,t)=>{let n=(t==null?void 0:t.id)||gu++;return this.create({jsx:e(n),id:n,...t}),n},this.subscribers=[],this.toasts=[]}},Ge=new fx,px=(e,t)=>{let n=(t==null?void 0:t.id)||gu++;return Ge.addToast({title:e,...t,id:n}),n},hx=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",mx=px,vx=()=>Ge.toasts,pt=Object.assign(mx,{success:Ge.success,info:Ge.info,warning:Ge.warning,error:Ge.error,custom:Ge.custom,message:Ge.message,promise:Ge.promise,dismiss:Ge.dismiss,loading:Ge.loading},{getHistory:vx});function gx(e,{insertAt:t}={}){if(typeof document>"u")return;let n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t==="top"&&n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}gx(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position="right"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position="left"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function Hi(e){return e.label!==void 0}var yx=3,wx="32px",xx=4e3,Sx=356,Ex=14,Cx=20,kx=200;function Px(...e){return e.filter(Boolean).join(" ")}var bx=e=>{var t,n,r,o,i,s,l,a,u,f;let{invert:d,toast:c,unstyled:y,interacting:x,setHeights:v,visibleToasts:w,heights:h,index:p,toasts:g,expanded:E,removeToast:C,defaultRichColors:P,closeButton:b,style:T,cancelButtonStyle:D,actionButtonStyle:R,className:j="",descriptionClassName:L="",duration:B,position:M,gap:H,loadingIcon:U,expandByDefault:W,classNames:k,icons:_,closeButtonAriaLabel:I="Close toast",pauseWhenPageIsHidden:F,cn:$}=e,[Y,ae]=A.useState(!1),[Qe,Z]=A.useState(!1),[dt,Zt]=A.useState(!1),[Jt,en]=A.useState(!1),[xi,ur]=A.useState(0),[$n,po]=A.useState(0),Si=A.useRef(null),tn=A.useRef(null),Pl=p===0,bl=p+1<=w,Ce=c.type,cr=c.dismissible!==!1,Lg=c.className||"",Ig=c.descriptionClassName||"",Ei=A.useMemo(()=>h.findIndex(V=>V.toastId===c.id)||0,[h,c.id]),Dg=A.useMemo(()=>{var V;return(V=c.closeButton)!=null?V:b},[c.closeButton,b]),Qc=A.useMemo(()=>c.duration||B||xx,[c.duration,B]),Tl=A.useRef(0),dr=A.useRef(0),Kc=A.useRef(0),fr=A.useRef(null),[Gc,Fg]=M.split("-"),Yc=A.useMemo(()=>h.reduce((V,ne,ee)=>ee>=Ei?V:V+ne.height,0),[h,Ei]),Xc=dx(),zg=c.invert||d,Nl=Ce==="loading";dr.current=A.useMemo(()=>Ei*H+Yc,[Ei,Yc]),A.useEffect(()=>{ae(!0)},[]),A.useLayoutEffect(()=>{if(!Y)return;let V=tn.current,ne=V.style.height;V.style.height="auto";let ee=V.getBoundingClientRect().height;V.style.height=ne,po(ee),v(Pt=>Pt.find(bt=>bt.toastId===c.id)?Pt.map(bt=>bt.toastId===c.id?{...bt,height:ee}:bt):[{toastId:c.id,height:ee,position:c.position},...Pt])},[Y,c.title,c.description,v,c.id]);let nn=A.useCallback(()=>{Z(!0),ur(dr.current),v(V=>V.filter(ne=>ne.toastId!==c.id)),setTimeout(()=>{C(c)},kx)},[c,C,v,dr]);A.useEffect(()=>{if(c.promise&&Ce==="loading"||c.duration===1/0||c.type==="loading")return;let V,ne=Qc;return E||x||F&&Xc?(()=>{if(Kc.current<Tl.current){let ee=new Date().getTime()-Tl.current;ne=ne-ee}Kc.current=new Date().getTime()})():ne!==1/0&&(Tl.current=new Date().getTime(),V=setTimeout(()=>{var ee;(ee=c.onAutoClose)==null||ee.call(c,c),nn()},ne)),()=>clearTimeout(V)},[E,x,W,c,Qc,nn,c.promise,Ce,F,Xc]),A.useEffect(()=>{let V=tn.current;if(V){let ne=V.getBoundingClientRect().height;return po(ne),v(ee=>[{toastId:c.id,height:ne,position:c.position},...ee]),()=>v(ee=>ee.filter(Pt=>Pt.toastId!==c.id))}},[v,c.id]),A.useEffect(()=>{c.delete&&nn()},[nn,c.delete]);function $g(){return _!=null&&_.loading?A.createElement("div",{className:"sonner-loader","data-visible":Ce==="loading"},_.loading):U?A.createElement("div",{className:"sonner-loader","data-visible":Ce==="loading"},U):A.createElement(sx,{visible:Ce==="loading"})}return A.createElement("li",{"aria-live":c.important?"assertive":"polite","aria-atomic":"true",role:"status",tabIndex:0,ref:tn,className:$(j,Lg,k==null?void 0:k.toast,(t=c==null?void 0:c.classNames)==null?void 0:t.toast,k==null?void 0:k.default,k==null?void 0:k[Ce],(n=c==null?void 0:c.classNames)==null?void 0:n[Ce]),"data-sonner-toast":"","data-rich-colors":(r=c.richColors)!=null?r:P,"data-styled":!(c.jsx||c.unstyled||y),"data-mounted":Y,"data-promise":!!c.promise,"data-removed":Qe,"data-visible":bl,"data-y-position":Gc,"data-x-position":Fg,"data-index":p,"data-front":Pl,"data-swiping":dt,"data-dismissible":cr,"data-type":Ce,"data-invert":zg,"data-swipe-out":Jt,"data-expanded":!!(E||W&&Y),style:{"--index":p,"--toasts-before":p,"--z-index":g.length-p,"--offset":`${Qe?xi:dr.current}px`,"--initial-height":W?"auto":`${$n}px`,...T,...c.style},onPointerDown:V=>{Nl||!cr||(Si.current=new Date,ur(dr.current),V.target.setPointerCapture(V.pointerId),V.target.tagName!=="BUTTON"&&(Zt(!0),fr.current={x:V.clientX,y:V.clientY}))},onPointerUp:()=>{var V,ne,ee,Pt;if(Jt||!cr)return;fr.current=null;let bt=Number(((V=tn.current)==null?void 0:V.style.getPropertyValue("--swipe-amount").replace("px",""))||0),Ci=new Date().getTime()-((ne=Si.current)==null?void 0:ne.getTime()),Ug=Math.abs(bt)/Ci;if(Math.abs(bt)>=Cx||Ug>.11){ur(dr.current),(ee=c.onDismiss)==null||ee.call(c,c),nn(),en(!0);return}(Pt=tn.current)==null||Pt.style.setProperty("--swipe-amount","0px"),Zt(!1)},onPointerMove:V=>{var ne;if(!fr.current||!cr)return;let ee=V.clientY-fr.current.y,Pt=V.clientX-fr.current.x,bt=(Gc==="top"?Math.min:Math.max)(0,ee),Ci=V.pointerType==="touch"?10:2;Math.abs(bt)>Ci?(ne=tn.current)==null||ne.style.setProperty("--swipe-amount",`${ee}px`):Math.abs(Pt)>Ci&&(fr.current=null)}},Dg&&!c.jsx?A.createElement("button",{"aria-label":I,"data-disabled":Nl,"data-close-button":!0,onClick:Nl||!cr?()=>{}:()=>{var V;nn(),(V=c.onDismiss)==null||V.call(c,c)},className:$(k==null?void 0:k.closeButton,(o=c==null?void 0:c.classNames)==null?void 0:o.closeButton)},A.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},A.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),A.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))):null,c.jsx||A.isValidElement(c.title)?c.jsx||c.title:A.createElement(A.Fragment,null,Ce||c.icon||c.promise?A.createElement("div",{"data-icon":"",className:$(k==null?void 0:k.icon,(i=c==null?void 0:c.classNames)==null?void 0:i.icon)},c.promise||c.type==="loading"&&!c.icon?c.icon||$g():null,c.type!=="loading"?c.icon||(_==null?void 0:_[Ce])||ox(Ce):null):null,A.createElement("div",{"data-content":"",className:$(k==null?void 0:k.content,(s=c==null?void 0:c.classNames)==null?void 0:s.content)},A.createElement("div",{"data-title":"",className:$(k==null?void 0:k.title,(l=c==null?void 0:c.classNames)==null?void 0:l.title)},c.title),c.description?A.createElement("div",{"data-description":"",className:$(L,Ig,k==null?void 0:k.description,(a=c==null?void 0:c.classNames)==null?void 0:a.description)},c.description):null),A.isValidElement(c.cancel)?c.cancel:c.cancel&&Hi(c.cancel)?A.createElement("button",{"data-button":!0,"data-cancel":!0,style:c.cancelButtonStyle||D,onClick:V=>{var ne,ee;Hi(c.cancel)&&cr&&((ee=(ne=c.cancel).onClick)==null||ee.call(ne,V),nn())},className:$(k==null?void 0:k.cancelButton,(u=c==null?void 0:c.classNames)==null?void 0:u.cancelButton)},c.cancel.label):null,A.isValidElement(c.action)?c.action:c.action&&Hi(c.action)?A.createElement("button",{"data-button":!0,"data-action":!0,style:c.actionButtonStyle||R,onClick:V=>{var ne,ee;Hi(c.action)&&(V.defaultPrevented||((ee=(ne=c.action).onClick)==null||ee.call(ne,V),nn()))},className:$(k==null?void 0:k.actionButton,(f=c==null?void 0:c.classNames)==null?void 0:f.actionButton)},c.action.label):null))};function yf(){if(typeof window>"u"||typeof document>"u")return"ltr";let e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}var Tx=e=>{let{invert:t,position:n="bottom-right",hotkey:r=["altKey","KeyT"],expand:o,closeButton:i,className:s,offset:l,theme:a="light",richColors:u,duration:f,style:d,visibleToasts:c=yx,toastOptions:y,dir:x=yf(),gap:v=Ex,loadingIcon:w,icons:h,containerAriaLabel:p="Notifications",pauseWhenPageIsHidden:g,cn:E=Px}=e,[C,P]=A.useState([]),b=A.useMemo(()=>Array.from(new Set([n].concat(C.filter(F=>F.position).map(F=>F.position)))),[C,n]),[T,D]=A.useState([]),[R,j]=A.useState(!1),[L,B]=A.useState(!1),[M,H]=A.useState(a!=="system"?a:typeof window<"u"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),U=A.useRef(null),W=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),k=A.useRef(null),_=A.useRef(!1),I=A.useCallback(F=>{var $;($=C.find(Y=>Y.id===F.id))!=null&&$.delete||Ge.dismiss(F.id),P(Y=>Y.filter(({id:ae})=>ae!==F.id))},[C]);return A.useEffect(()=>Ge.subscribe(F=>{if(F.dismiss){P($=>$.map(Y=>Y.id===F.id?{...Y,delete:!0}:Y));return}setTimeout(()=>{Em.flushSync(()=>{P($=>{let Y=$.findIndex(ae=>ae.id===F.id);return Y!==-1?[...$.slice(0,Y),{...$[Y],...F},...$.slice(Y+1)]:[F,...$]})})})}),[]),A.useEffect(()=>{if(a!=="system"){H(a);return}a==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?H("dark"):H("light")),typeof window<"u"&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",({matches:F})=>{H(F?"dark":"light")})},[a]),A.useEffect(()=>{C.length<=1&&j(!1)},[C]),A.useEffect(()=>{let F=$=>{var Y,ae;r.every(Qe=>$[Qe]||$.code===Qe)&&(j(!0),(Y=U.current)==null||Y.focus()),$.code==="Escape"&&(document.activeElement===U.current||(ae=U.current)!=null&&ae.contains(document.activeElement))&&j(!1)};return document.addEventListener("keydown",F),()=>document.removeEventListener("keydown",F)},[r]),A.useEffect(()=>{if(U.current)return()=>{k.current&&(k.current.focus({preventScroll:!0}),k.current=null,_.current=!1)}},[U.current]),C.length?A.createElement("section",{"aria-label":`${p} ${W}`,tabIndex:-1},b.map((F,$)=>{var Y;let[ae,Qe]=F.split("-");return A.createElement("ol",{key:F,dir:x==="auto"?yf():x,tabIndex:-1,ref:U,className:s,"data-sonner-toaster":!0,"data-theme":M,"data-y-position":ae,"data-x-position":Qe,style:{"--front-toast-height":`${((Y=T[0])==null?void 0:Y.height)||0}px`,"--offset":typeof l=="number"?`${l}px`:l||wx,"--width":`${Sx}px`,"--gap":`${v}px`,...d},onBlur:Z=>{_.current&&!Z.currentTarget.contains(Z.relatedTarget)&&(_.current=!1,k.current&&(k.current.focus({preventScroll:!0}),k.current=null))},onFocus:Z=>{Z.target instanceof HTMLElement&&Z.target.dataset.dismissible==="false"||_.current||(_.current=!0,k.current=Z.relatedTarget)},onMouseEnter:()=>j(!0),onMouseMove:()=>j(!0),onMouseLeave:()=>{L||j(!1)},onPointerDown:Z=>{Z.target instanceof HTMLElement&&Z.target.dataset.dismissible==="false"||B(!0)},onPointerUp:()=>B(!1)},C.filter(Z=>!Z.position&&$===0||Z.position===F).map((Z,dt)=>{var Zt,Jt;return A.createElement(bx,{key:Z.id,icons:h,index:dt,toast:Z,defaultRichColors:u,duration:(Zt=y==null?void 0:y.duration)!=null?Zt:f,className:y==null?void 0:y.className,descriptionClassName:y==null?void 0:y.descriptionClassName,invert:t,visibleToasts:c,closeButton:(Jt=y==null?void 0:y.closeButton)!=null?Jt:i,interacting:L,position:F,style:y==null?void 0:y.style,unstyled:y==null?void 0:y.unstyled,classNames:y==null?void 0:y.classNames,cancelButtonStyle:y==null?void 0:y.cancelButtonStyle,actionButtonStyle:y==null?void 0:y.actionButtonStyle,removeToast:I,toasts:C.filter(en=>en.position==Z.position),heights:T.filter(en=>en.position==Z.position),setHeights:D,expandByDefault:o,gap:v,loadingIcon:w,expanded:R,pauseWhenPageIsHidden:g,cn:E})}))})):null};const Nx=({...e})=>{const{theme:t="system"}=rx();return S.jsx(Tx,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})};var Rx=wp.useId||(()=>{}),Ox=0;function sa(e){const[t,n]=m.useState(Rx());return An(()=>{e||n(r=>r??String(Ox++))},[e]),e||(t?`radix-${t}`:"")}const _x=["top","right","bottom","left"],Mn=Math.min,Xe=Math.max,zs=Math.round,Vi=Math.floor,jn=e=>({x:e,y:e}),Ax={left:"right",right:"left",bottom:"top",top:"bottom"},Mx={start:"end",end:"start"};function yu(e,t,n){return Xe(e,Mn(t,n))}function Yt(e,t){return typeof e=="function"?e(t):e}function Xt(e){return e.split("-")[0]}function ao(e){return e.split("-")[1]}function Oc(e){return e==="x"?"y":"x"}function _c(e){return e==="y"?"height":"width"}function Ln(e){return["top","bottom"].includes(Xt(e))?"y":"x"}function Ac(e){return Oc(Ln(e))}function jx(e,t,n){n===void 0&&(n=!1);const r=ao(e),o=Ac(e),i=_c(o);let s=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[i]>t.floating[i]&&(s=$s(s)),[s,$s(s)]}function Lx(e){const t=$s(e);return[wu(e),t,wu(t)]}function wu(e){return e.replace(/start|end/g,t=>Mx[t])}function Ix(e,t,n){const r=["left","right"],o=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?i:s;default:return[]}}function Dx(e,t,n,r){const o=ao(e);let i=Ix(Xt(e),n==="start",r);return o&&(i=i.map(s=>s+"-"+o),t&&(i=i.concat(i.map(wu)))),i}function $s(e){return e.replace(/left|right|bottom|top/g,t=>Ax[t])}function Fx(e){return{top:0,right:0,bottom:0,left:0,...e}}function cv(e){return typeof e!="number"?Fx(e):{top:e,right:e,bottom:e,left:e}}function Us(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function wf(e,t,n){let{reference:r,floating:o}=e;const i=Ln(t),s=Ac(t),l=_c(s),a=Xt(t),u=i==="y",f=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,c=r[l]/2-o[l]/2;let y;switch(a){case"top":y={x:f,y:r.y-o.height};break;case"bottom":y={x:f,y:r.y+r.height};break;case"right":y={x:r.x+r.width,y:d};break;case"left":y={x:r.x-o.width,y:d};break;default:y={x:r.x,y:r.y}}switch(ao(t)){case"start":y[s]-=c*(n&&u?-1:1);break;case"end":y[s]+=c*(n&&u?-1:1);break}return y}const zx=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,l=i.filter(Boolean),a=await(s.isRTL==null?void 0:s.isRTL(t));let u=await s.getElementRects({reference:e,floating:t,strategy:o}),{x:f,y:d}=wf(u,r,a),c=r,y={},x=0;for(let v=0;v<l.length;v++){const{name:w,fn:h}=l[v],{x:p,y:g,data:E,reset:C}=await h({x:f,y:d,initialPlacement:r,placement:c,strategy:o,middlewareData:y,rects:u,platform:s,elements:{reference:e,floating:t}});f=p??f,d=g??d,y={...y,[w]:{...y[w],...E}},C&&x<=50&&(x++,typeof C=="object"&&(C.placement&&(c=C.placement),C.rects&&(u=C.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:o}):C.rects),{x:f,y:d}=wf(u,c,a)),v=-1)}return{x:f,y:d,placement:c,strategy:o,middlewareData:y}};async function ii(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:i,rects:s,elements:l,strategy:a}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:d="floating",altBoundary:c=!1,padding:y=0}=Yt(t,e),x=cv(y),w=l[c?d==="floating"?"reference":"floating":d],h=Us(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(w)))==null||n?w:w.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(l.floating)),boundary:u,rootBoundary:f,strategy:a})),p=d==="floating"?{x:r,y:o,width:s.floating.width,height:s.floating.height}:s.reference,g=await(i.getOffsetParent==null?void 0:i.getOffsetParent(l.floating)),E=await(i.isElement==null?void 0:i.isElement(g))?await(i.getScale==null?void 0:i.getScale(g))||{x:1,y:1}:{x:1,y:1},C=Us(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:p,offsetParent:g,strategy:a}):p);return{top:(h.top-C.top+x.top)/E.y,bottom:(C.bottom-h.bottom+x.bottom)/E.y,left:(h.left-C.left+x.left)/E.x,right:(C.right-h.right+x.right)/E.x}}const $x=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:i,platform:s,elements:l,middlewareData:a}=t,{element:u,padding:f=0}=Yt(e,t)||{};if(u==null)return{};const d=cv(f),c={x:n,y:r},y=Ac(o),x=_c(y),v=await s.getDimensions(u),w=y==="y",h=w?"top":"left",p=w?"bottom":"right",g=w?"clientHeight":"clientWidth",E=i.reference[x]+i.reference[y]-c[y]-i.floating[x],C=c[y]-i.reference[y],P=await(s.getOffsetParent==null?void 0:s.getOffsetParent(u));let b=P?P[g]:0;(!b||!await(s.isElement==null?void 0:s.isElement(P)))&&(b=l.floating[g]||i.floating[x]);const T=E/2-C/2,D=b/2-v[x]/2-1,R=Mn(d[h],D),j=Mn(d[p],D),L=R,B=b-v[x]-j,M=b/2-v[x]/2+T,H=yu(L,M,B),U=!a.arrow&&ao(o)!=null&&M!==H&&i.reference[x]/2-(M<L?R:j)-v[x]/2<0,W=U?M<L?M-L:M-B:0;return{[y]:c[y]+W,data:{[y]:H,centerOffset:M-H-W,...U&&{alignmentOffset:W}},reset:U}}}),Ux=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:i,rects:s,initialPlacement:l,platform:a,elements:u}=t,{mainAxis:f=!0,crossAxis:d=!0,fallbackPlacements:c,fallbackStrategy:y="bestFit",fallbackAxisSideDirection:x="none",flipAlignment:v=!0,...w}=Yt(e,t);if((n=i.arrow)!=null&&n.alignmentOffset)return{};const h=Xt(o),p=Ln(l),g=Xt(l)===l,E=await(a.isRTL==null?void 0:a.isRTL(u.floating)),C=c||(g||!v?[$s(l)]:Lx(l)),P=x!=="none";!c&&P&&C.push(...Dx(l,v,x,E));const b=[l,...C],T=await ii(t,w),D=[];let R=((r=i.flip)==null?void 0:r.overflows)||[];if(f&&D.push(T[h]),d){const M=jx(o,s,E);D.push(T[M[0]],T[M[1]])}if(R=[...R,{placement:o,overflows:D}],!D.every(M=>M<=0)){var j,L;const M=(((j=i.flip)==null?void 0:j.index)||0)+1,H=b[M];if(H)return{data:{index:M,overflows:R},reset:{placement:H}};let U=(L=R.filter(W=>W.overflows[0]<=0).sort((W,k)=>W.overflows[1]-k.overflows[1])[0])==null?void 0:L.placement;if(!U)switch(y){case"bestFit":{var B;const W=(B=R.filter(k=>{if(P){const _=Ln(k.placement);return _===p||_==="y"}return!0}).map(k=>[k.placement,k.overflows.filter(_=>_>0).reduce((_,I)=>_+I,0)]).sort((k,_)=>k[1]-_[1])[0])==null?void 0:B[0];W&&(U=W);break}case"initialPlacement":U=l;break}if(o!==U)return{reset:{placement:U}}}return{}}}};function xf(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Sf(e){return _x.some(t=>e[t]>=0)}const Bx=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=Yt(e,t);switch(r){case"referenceHidden":{const i=await ii(t,{...o,elementContext:"reference"}),s=xf(i,n.reference);return{data:{referenceHiddenOffsets:s,referenceHidden:Sf(s)}}}case"escaped":{const i=await ii(t,{...o,altBoundary:!0}),s=xf(i,n.floating);return{data:{escapedOffsets:s,escaped:Sf(s)}}}default:return{}}}}};async function Wx(e,t){const{placement:n,platform:r,elements:o}=e,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),s=Xt(n),l=ao(n),a=Ln(n)==="y",u=["left","top"].includes(s)?-1:1,f=i&&a?-1:1,d=Yt(t,e);let{mainAxis:c,crossAxis:y,alignmentAxis:x}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&typeof x=="number"&&(y=l==="end"?x*-1:x),a?{x:y*f,y:c*u}:{x:c*u,y:y*f}}const Hx=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:i,placement:s,middlewareData:l}=t,a=await Wx(t,e);return s===((n=l.offset)==null?void 0:n.placement)&&(r=l.arrow)!=null&&r.alignmentOffset?{}:{x:o+a.x,y:i+a.y,data:{...a,placement:s}}}}},Vx=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:s=!1,limiter:l={fn:w=>{let{x:h,y:p}=w;return{x:h,y:p}}},...a}=Yt(e,t),u={x:n,y:r},f=await ii(t,a),d=Ln(Xt(o)),c=Oc(d);let y=u[c],x=u[d];if(i){const w=c==="y"?"top":"left",h=c==="y"?"bottom":"right",p=y+f[w],g=y-f[h];y=yu(p,y,g)}if(s){const w=d==="y"?"top":"left",h=d==="y"?"bottom":"right",p=x+f[w],g=x-f[h];x=yu(p,x,g)}const v=l.fn({...t,[c]:y,[d]:x});return{...v,data:{x:v.x-n,y:v.y-r,enabled:{[c]:i,[d]:s}}}}}},Qx=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:i,middlewareData:s}=t,{offset:l=0,mainAxis:a=!0,crossAxis:u=!0}=Yt(e,t),f={x:n,y:r},d=Ln(o),c=Oc(d);let y=f[c],x=f[d];const v=Yt(l,t),w=typeof v=="number"?{mainAxis:v,crossAxis:0}:{mainAxis:0,crossAxis:0,...v};if(a){const g=c==="y"?"height":"width",E=i.reference[c]-i.floating[g]+w.mainAxis,C=i.reference[c]+i.reference[g]-w.mainAxis;y<E?y=E:y>C&&(y=C)}if(u){var h,p;const g=c==="y"?"width":"height",E=["top","left"].includes(Xt(o)),C=i.reference[d]-i.floating[g]+(E&&((h=s.offset)==null?void 0:h[d])||0)+(E?0:w.crossAxis),P=i.reference[d]+i.reference[g]+(E?0:((p=s.offset)==null?void 0:p[d])||0)-(E?w.crossAxis:0);x<C?x=C:x>P&&(x=P)}return{[c]:y,[d]:x}}}},Kx=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:i,platform:s,elements:l}=t,{apply:a=()=>{},...u}=Yt(e,t),f=await ii(t,u),d=Xt(o),c=ao(o),y=Ln(o)==="y",{width:x,height:v}=i.floating;let w,h;d==="top"||d==="bottom"?(w=d,h=c===(await(s.isRTL==null?void 0:s.isRTL(l.floating))?"start":"end")?"left":"right"):(h=d,w=c==="end"?"top":"bottom");const p=v-f.top-f.bottom,g=x-f.left-f.right,E=Mn(v-f[w],p),C=Mn(x-f[h],g),P=!t.middlewareData.shift;let b=E,T=C;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(T=g),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(b=p),P&&!c){const R=Xe(f.left,0),j=Xe(f.right,0),L=Xe(f.top,0),B=Xe(f.bottom,0);y?T=x-2*(R!==0||j!==0?R+j:Xe(f.left,f.right)):b=v-2*(L!==0||B!==0?L+B:Xe(f.top,f.bottom))}await a({...t,availableWidth:T,availableHeight:b});const D=await s.getDimensions(l.floating);return x!==D.width||v!==D.height?{reset:{rects:!0}}:{}}}};function hl(){return typeof window<"u"}function uo(e){return dv(e)?(e.nodeName||"").toLowerCase():"#document"}function Je(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Dt(e){var t;return(t=(dv(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function dv(e){return hl()?e instanceof Node||e instanceof Je(e).Node:!1}function Et(e){return hl()?e instanceof Element||e instanceof Je(e).Element:!1}function It(e){return hl()?e instanceof HTMLElement||e instanceof Je(e).HTMLElement:!1}function Ef(e){return!hl()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Je(e).ShadowRoot}function wi(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Ct(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function Gx(e){return["table","td","th"].includes(uo(e))}function ml(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function Mc(e){const t=jc(),n=Et(e)?Ct(e):e;return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function Yx(e){let t=In(e);for(;It(t)&&!to(t);){if(Mc(t))return t;if(ml(t))return null;t=In(t)}return null}function jc(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function to(e){return["html","body","#document"].includes(uo(e))}function Ct(e){return Je(e).getComputedStyle(e)}function vl(e){return Et(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function In(e){if(uo(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Ef(e)&&e.host||Dt(e);return Ef(t)?t.host:t}function fv(e){const t=In(e);return to(t)?e.ownerDocument?e.ownerDocument.body:e.body:It(t)&&wi(t)?t:fv(t)}function si(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=fv(e),i=o===((r=e.ownerDocument)==null?void 0:r.body),s=Je(o);if(i){const l=xu(s);return t.concat(s,s.visualViewport||[],wi(o)?o:[],l&&n?si(l):[])}return t.concat(o,si(o,[],n))}function xu(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function pv(e){const t=Ct(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=It(e),i=o?e.offsetWidth:n,s=o?e.offsetHeight:r,l=zs(n)!==i||zs(r)!==s;return l&&(n=i,r=s),{width:n,height:r,$:l}}function Lc(e){return Et(e)?e:e.contextElement}function Ir(e){const t=Lc(e);if(!It(t))return jn(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:i}=pv(t);let s=(i?zs(n.width):n.width)/r,l=(i?zs(n.height):n.height)/o;return(!s||!Number.isFinite(s))&&(s=1),(!l||!Number.isFinite(l))&&(l=1),{x:s,y:l}}const Xx=jn(0);function hv(e){const t=Je(e);return!jc()||!t.visualViewport?Xx:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function qx(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Je(e)?!1:t}function or(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),i=Lc(e);let s=jn(1);t&&(r?Et(r)&&(s=Ir(r)):s=Ir(e));const l=qx(i,n,r)?hv(i):jn(0);let a=(o.left+l.x)/s.x,u=(o.top+l.y)/s.y,f=o.width/s.x,d=o.height/s.y;if(i){const c=Je(i),y=r&&Et(r)?Je(r):r;let x=c,v=xu(x);for(;v&&r&&y!==x;){const w=Ir(v),h=v.getBoundingClientRect(),p=Ct(v),g=h.left+(v.clientLeft+parseFloat(p.paddingLeft))*w.x,E=h.top+(v.clientTop+parseFloat(p.paddingTop))*w.y;a*=w.x,u*=w.y,f*=w.x,d*=w.y,a+=g,u+=E,x=Je(v),v=xu(x)}}return Us({width:f,height:d,x:a,y:u})}function Zx(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const i=o==="fixed",s=Dt(r),l=t?ml(t.floating):!1;if(r===s||l&&i)return n;let a={scrollLeft:0,scrollTop:0},u=jn(1);const f=jn(0),d=It(r);if((d||!d&&!i)&&((uo(r)!=="body"||wi(s))&&(a=vl(r)),It(r))){const c=or(r);u=Ir(r),f.x=c.x+r.clientLeft,f.y=c.y+r.clientTop}return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-a.scrollLeft*u.x+f.x,y:n.y*u.y-a.scrollTop*u.y+f.y}}function Jx(e){return Array.from(e.getClientRects())}function Su(e,t){const n=vl(e).scrollLeft;return t?t.left+n:or(Dt(e)).left+n}function eS(e){const t=Dt(e),n=vl(e),r=e.ownerDocument.body,o=Xe(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=Xe(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let s=-n.scrollLeft+Su(e);const l=-n.scrollTop;return Ct(r).direction==="rtl"&&(s+=Xe(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:s,y:l}}function tS(e,t){const n=Je(e),r=Dt(e),o=n.visualViewport;let i=r.clientWidth,s=r.clientHeight,l=0,a=0;if(o){i=o.width,s=o.height;const u=jc();(!u||u&&t==="fixed")&&(l=o.offsetLeft,a=o.offsetTop)}return{width:i,height:s,x:l,y:a}}function nS(e,t){const n=or(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=It(e)?Ir(e):jn(1),s=e.clientWidth*i.x,l=e.clientHeight*i.y,a=o*i.x,u=r*i.y;return{width:s,height:l,x:a,y:u}}function Cf(e,t,n){let r;if(t==="viewport")r=tS(e,n);else if(t==="document")r=eS(Dt(e));else if(Et(t))r=nS(t,n);else{const o=hv(e);r={...t,x:t.x-o.x,y:t.y-o.y}}return Us(r)}function mv(e,t){const n=In(e);return n===t||!Et(n)||to(n)?!1:Ct(n).position==="fixed"||mv(n,t)}function rS(e,t){const n=t.get(e);if(n)return n;let r=si(e,[],!1).filter(l=>Et(l)&&uo(l)!=="body"),o=null;const i=Ct(e).position==="fixed";let s=i?In(e):e;for(;Et(s)&&!to(s);){const l=Ct(s),a=Mc(s);!a&&l.position==="fixed"&&(o=null),(i?!a&&!o:!a&&l.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||wi(s)&&!a&&mv(e,s))?r=r.filter(f=>f!==s):o=l,s=In(s)}return t.set(e,r),r}function oS(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const s=[...n==="clippingAncestors"?ml(t)?[]:rS(t,this._c):[].concat(n),r],l=s[0],a=s.reduce((u,f)=>{const d=Cf(t,f,o);return u.top=Xe(d.top,u.top),u.right=Mn(d.right,u.right),u.bottom=Mn(d.bottom,u.bottom),u.left=Xe(d.left,u.left),u},Cf(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function iS(e){const{width:t,height:n}=pv(e);return{width:t,height:n}}function sS(e,t,n){const r=It(t),o=Dt(t),i=n==="fixed",s=or(e,!0,i,t);let l={scrollLeft:0,scrollTop:0};const a=jn(0);if(r||!r&&!i)if((uo(t)!=="body"||wi(o))&&(l=vl(t)),r){const y=or(t,!0,i,t);a.x=y.x+t.clientLeft,a.y=y.y+t.clientTop}else o&&(a.x=Su(o));let u=0,f=0;if(o&&!r&&!i){const y=o.getBoundingClientRect();f=y.top+l.scrollTop,u=y.left+l.scrollLeft-Su(o,y)}const d=s.left+l.scrollLeft-a.x-u,c=s.top+l.scrollTop-a.y-f;return{x:d,y:c,width:s.width,height:s.height}}function la(e){return Ct(e).position==="static"}function kf(e,t){if(!It(e)||Ct(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Dt(e)===n&&(n=n.ownerDocument.body),n}function vv(e,t){const n=Je(e);if(ml(e))return n;if(!It(e)){let o=In(e);for(;o&&!to(o);){if(Et(o)&&!la(o))return o;o=In(o)}return n}let r=kf(e,t);for(;r&&Gx(r)&&la(r);)r=kf(r,t);return r&&to(r)&&la(r)&&!Mc(r)?n:r||Yx(e)||n}const lS=async function(e){const t=this.getOffsetParent||vv,n=this.getDimensions,r=await n(e.floating);return{reference:sS(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function aS(e){return Ct(e).direction==="rtl"}const uS={convertOffsetParentRelativeRectToViewportRelativeRect:Zx,getDocumentElement:Dt,getClippingRect:oS,getOffsetParent:vv,getElementRects:lS,getClientRects:Jx,getDimensions:iS,getScale:Ir,isElement:Et,isRTL:aS};function cS(e,t){let n=null,r;const o=Dt(e);function i(){var l;clearTimeout(r),(l=n)==null||l.disconnect(),n=null}function s(l,a){l===void 0&&(l=!1),a===void 0&&(a=1),i();const{left:u,top:f,width:d,height:c}=e.getBoundingClientRect();if(l||t(),!d||!c)return;const y=Vi(f),x=Vi(o.clientWidth-(u+d)),v=Vi(o.clientHeight-(f+c)),w=Vi(u),p={rootMargin:-y+"px "+-x+"px "+-v+"px "+-w+"px",threshold:Xe(0,Mn(1,a))||1};let g=!0;function E(C){const P=C[0].intersectionRatio;if(P!==a){if(!g)return s();P?s(!1,P):r=setTimeout(()=>{s(!1,1e-7)},1e3)}g=!1}try{n=new IntersectionObserver(E,{...p,root:o.ownerDocument})}catch{n=new IntersectionObserver(E,p)}n.observe(e)}return s(!0),i}function dS(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:a=!1}=r,u=Lc(e),f=o||i?[...u?si(u):[],...si(t)]:[];f.forEach(h=>{o&&h.addEventListener("scroll",n,{passive:!0}),i&&h.addEventListener("resize",n)});const d=u&&l?cS(u,n):null;let c=-1,y=null;s&&(y=new ResizeObserver(h=>{let[p]=h;p&&p.target===u&&y&&(y.unobserve(t),cancelAnimationFrame(c),c=requestAnimationFrame(()=>{var g;(g=y)==null||g.observe(t)})),n()}),u&&!a&&y.observe(u),y.observe(t));let x,v=a?or(e):null;a&&w();function w(){const h=or(e);v&&(h.x!==v.x||h.y!==v.y||h.width!==v.width||h.height!==v.height)&&n(),v=h,x=requestAnimationFrame(w)}return n(),()=>{var h;f.forEach(p=>{o&&p.removeEventListener("scroll",n),i&&p.removeEventListener("resize",n)}),d==null||d(),(h=y)==null||h.disconnect(),y=null,a&&cancelAnimationFrame(x)}}const fS=Hx,pS=Vx,hS=Ux,mS=Kx,vS=Bx,Pf=$x,gS=Qx,yS=(e,t,n)=>{const r=new Map,o={platform:uS,...n},i={...o.platform,_c:r};return zx(e,t,{...o,platform:i})};var fs=typeof document<"u"?m.useLayoutEffect:m.useEffect;function Bs(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Bs(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const i=o[r];if(!(i==="_owner"&&e.$$typeof)&&!Bs(e[i],t[i]))return!1}return!0}return e!==e&&t!==t}function gv(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function bf(e,t){const n=gv(e);return Math.round(t*n)/n}function aa(e){const t=m.useRef(e);return fs(()=>{t.current=e}),t}function wS(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:s}={},transform:l=!0,whileElementsMounted:a,open:u}=e,[f,d]=m.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[c,y]=m.useState(r);Bs(c,r)||y(r);const[x,v]=m.useState(null),[w,h]=m.useState(null),p=m.useCallback(k=>{k!==P.current&&(P.current=k,v(k))},[]),g=m.useCallback(k=>{k!==b.current&&(b.current=k,h(k))},[]),E=i||x,C=s||w,P=m.useRef(null),b=m.useRef(null),T=m.useRef(f),D=a!=null,R=aa(a),j=aa(o),L=aa(u),B=m.useCallback(()=>{if(!P.current||!b.current)return;const k={placement:t,strategy:n,middleware:c};j.current&&(k.platform=j.current),yS(P.current,b.current,k).then(_=>{const I={..._,isPositioned:L.current!==!1};M.current&&!Bs(T.current,I)&&(T.current=I,yi.flushSync(()=>{d(I)}))})},[c,t,n,j,L]);fs(()=>{u===!1&&T.current.isPositioned&&(T.current.isPositioned=!1,d(k=>({...k,isPositioned:!1})))},[u]);const M=m.useRef(!1);fs(()=>(M.current=!0,()=>{M.current=!1}),[]),fs(()=>{if(E&&(P.current=E),C&&(b.current=C),E&&C){if(R.current)return R.current(E,C,B);B()}},[E,C,B,R,D]);const H=m.useMemo(()=>({reference:P,floating:b,setReference:p,setFloating:g}),[p,g]),U=m.useMemo(()=>({reference:E,floating:C}),[E,C]),W=m.useMemo(()=>{const k={position:n,left:0,top:0};if(!U.floating)return k;const _=bf(U.floating,f.x),I=bf(U.floating,f.y);return l?{...k,transform:"translate("+_+"px, "+I+"px)",...gv(U.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:_,top:I}},[n,l,U.floating,f.x,f.y]);return m.useMemo(()=>({...f,update:B,refs:H,elements:U,floatingStyles:W}),[f,B,H,U,W])}const xS=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Pf({element:r.current,padding:o}).fn(n):{}:r?Pf({element:r,padding:o}).fn(n):{}}}},SS=(e,t)=>({...fS(e),options:[e,t]}),ES=(e,t)=>({...pS(e),options:[e,t]}),CS=(e,t)=>({...gS(e),options:[e,t]}),kS=(e,t)=>({...hS(e),options:[e,t]}),PS=(e,t)=>({...mS(e),options:[e,t]}),bS=(e,t)=>({...vS(e),options:[e,t]}),TS=(e,t)=>({...xS(e),options:[e,t]});var NS="Arrow",yv=m.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...i}=e;return S.jsx(me.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:S.jsx("polygon",{points:"0,0 30,0 15,10"})})});yv.displayName=NS;var RS=yv;function OS(e,t=[]){let n=[];function r(i,s){const l=m.createContext(s),a=n.length;n=[...n,s];function u(d){const{scope:c,children:y,...x}=d,v=(c==null?void 0:c[e][a])||l,w=m.useMemo(()=>x,Object.values(x));return S.jsx(v.Provider,{value:w,children:y})}function f(d,c){const y=(c==null?void 0:c[e][a])||l,x=m.useContext(y);if(x)return x;if(s!==void 0)return s;throw new Error(`\`${d}\` must be used within \`${i}\``)}return u.displayName=i+"Provider",[u,f]}const o=()=>{const i=n.map(s=>m.createContext(s));return function(l){const a=(l==null?void 0:l[e])||i;return m.useMemo(()=>({[`__scope${e}`]:{...l,[e]:a}}),[l,a])}};return o.scopeName=e,[r,_S(o,...t)]}function _S(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(i){const s=r.reduce((l,{useScope:a,scopeName:u})=>{const d=a(i)[`__scope${u}`];return{...l,...d}},{});return m.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return n.scopeName=t.scopeName,n}function AS(e){const[t,n]=m.useState(void 0);return An(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const i=o[0];let s,l;if("borderBoxSize"in i){const a=i.borderBoxSize,u=Array.isArray(a)?a[0]:a;s=u.inlineSize,l=u.blockSize}else s=e.offsetWidth,l=e.offsetHeight;n({width:s,height:l})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var wv="Popper",[xv,Sv]=OS(wv),[Bk,Ev]=xv(wv),Cv="PopperAnchor",kv=m.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,i=Ev(Cv,n),s=m.useRef(null),l=De(t,s);return m.useEffect(()=>{i.onAnchorChange((r==null?void 0:r.current)||s.current)}),r?null:S.jsx(me.div,{...o,ref:l})});kv.displayName=Cv;var Ic="PopperContent",[MS,jS]=xv(Ic),Pv=m.forwardRef((e,t)=>{var dt,Zt,Jt,en,xi,ur;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:i="center",alignOffset:s=0,arrowPadding:l=0,avoidCollisions:a=!0,collisionBoundary:u=[],collisionPadding:f=0,sticky:d="partial",hideWhenDetached:c=!1,updatePositionStrategy:y="optimized",onPlaced:x,...v}=e,w=Ev(Ic,n),[h,p]=m.useState(null),g=De(t,$n=>p($n)),[E,C]=m.useState(null),P=AS(E),b=(P==null?void 0:P.width)??0,T=(P==null?void 0:P.height)??0,D=r+(i!=="center"?"-"+i:""),R=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},j=Array.isArray(u)?u:[u],L=j.length>0,B={padding:R,boundary:j.filter(IS),altBoundary:L},{refs:M,floatingStyles:H,placement:U,isPositioned:W,middlewareData:k}=wS({strategy:"fixed",placement:D,whileElementsMounted:(...$n)=>dS(...$n,{animationFrame:y==="always"}),elements:{reference:w.anchor},middleware:[SS({mainAxis:o+T,alignmentAxis:s}),a&&ES({mainAxis:!0,crossAxis:!1,limiter:d==="partial"?CS():void 0,...B}),a&&kS({...B}),PS({...B,apply:({elements:$n,rects:po,availableWidth:Si,availableHeight:tn})=>{const{width:Pl,height:bl}=po.reference,Ce=$n.floating.style;Ce.setProperty("--radix-popper-available-width",`${Si}px`),Ce.setProperty("--radix-popper-available-height",`${tn}px`),Ce.setProperty("--radix-popper-anchor-width",`${Pl}px`),Ce.setProperty("--radix-popper-anchor-height",`${bl}px`)}}),E&&TS({element:E,padding:l}),DS({arrowWidth:b,arrowHeight:T}),c&&bS({strategy:"referenceHidden",...B})]}),[_,I]=Nv(U),F=ct(x);An(()=>{W&&(F==null||F())},[W,F]);const $=(dt=k.arrow)==null?void 0:dt.x,Y=(Zt=k.arrow)==null?void 0:Zt.y,ae=((Jt=k.arrow)==null?void 0:Jt.centerOffset)!==0,[Qe,Z]=m.useState();return An(()=>{h&&Z(window.getComputedStyle(h).zIndex)},[h]),S.jsx("div",{ref:M.setFloating,"data-radix-popper-content-wrapper":"",style:{...H,transform:W?H.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Qe,"--radix-popper-transform-origin":[(en=k.transformOrigin)==null?void 0:en.x,(xi=k.transformOrigin)==null?void 0:xi.y].join(" "),...((ur=k.hide)==null?void 0:ur.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:S.jsx(MS,{scope:n,placedSide:_,onArrowChange:C,arrowX:$,arrowY:Y,shouldHideArrow:ae,children:S.jsx(me.div,{"data-side":_,"data-align":I,...v,ref:g,style:{...v.style,animation:W?void 0:"none"}})})})});Pv.displayName=Ic;var bv="PopperArrow",LS={top:"bottom",right:"left",bottom:"top",left:"right"},Tv=m.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,i=jS(bv,r),s=LS[i.placedSide];return S.jsx("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[s]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:S.jsx(RS,{...o,ref:n,style:{...o.style,display:"block"}})})});Tv.displayName=bv;function IS(e){return e!==null}var DS=e=>({name:"transformOrigin",options:e,fn(t){var w,h,p;const{placement:n,rects:r,middlewareData:o}=t,s=((w=o.arrow)==null?void 0:w.centerOffset)!==0,l=s?0:e.arrowWidth,a=s?0:e.arrowHeight,[u,f]=Nv(n),d={start:"0%",center:"50%",end:"100%"}[f],c=(((h=o.arrow)==null?void 0:h.x)??0)+l/2,y=(((p=o.arrow)==null?void 0:p.y)??0)+a/2;let x="",v="";return u==="bottom"?(x=s?d:`${c}px`,v=`${-a}px`):u==="top"?(x=s?d:`${c}px`,v=`${r.floating.height+a}px`):u==="right"?(x=`${-a}px`,v=s?d:`${y}px`):u==="left"&&(x=`${r.floating.width+a}px`,v=s?d:`${y}px`),{data:{x,y:v}}}});function Nv(e){const[t,n="center"]=e.split("-");return[t,n]}var FS=kv,zS=Pv,$S=Tv,[gl,Wk]=Cc("Tooltip",[Sv]),Dc=Sv(),Rv="TooltipProvider",US=700,Tf="tooltip.open",[BS,Ov]=gl(Rv),_v=e=>{const{__scopeTooltip:t,delayDuration:n=US,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:i}=e,[s,l]=m.useState(!0),a=m.useRef(!1),u=m.useRef(0);return m.useEffect(()=>{const f=u.current;return()=>window.clearTimeout(f)},[]),S.jsx(BS,{scope:t,isOpenDelayed:s,delayDuration:n,onOpen:m.useCallback(()=>{window.clearTimeout(u.current),l(!1)},[]),onClose:m.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>l(!0),r)},[r]),isPointerInTransitRef:a,onPointerInTransitChange:m.useCallback(f=>{a.current=f},[]),disableHoverableContent:o,children:i})};_v.displayName=Rv;var Av="Tooltip",[Hk,yl]=gl(Av),Eu="TooltipTrigger",WS=m.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=yl(Eu,n),i=Ov(Eu,n),s=Dc(n),l=m.useRef(null),a=De(t,l,o.onTriggerChange),u=m.useRef(!1),f=m.useRef(!1),d=m.useCallback(()=>u.current=!1,[]);return m.useEffect(()=>()=>document.removeEventListener("pointerup",d),[d]),S.jsx(FS,{asChild:!0,...s,children:S.jsx(me.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:a,onPointerMove:le(e.onPointerMove,c=>{c.pointerType!=="touch"&&!f.current&&!i.isPointerInTransitRef.current&&(o.onTriggerEnter(),f.current=!0)}),onPointerLeave:le(e.onPointerLeave,()=>{o.onTriggerLeave(),f.current=!1}),onPointerDown:le(e.onPointerDown,()=>{u.current=!0,document.addEventListener("pointerup",d,{once:!0})}),onFocus:le(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:le(e.onBlur,o.onClose),onClick:le(e.onClick,o.onClose)})})});WS.displayName=Eu;var HS="TooltipPortal",[Vk,VS]=gl(HS,{forceMount:void 0}),no="TooltipContent",Mv=m.forwardRef((e,t)=>{const n=VS(no,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,s=yl(no,e.__scopeTooltip);return S.jsx(so,{present:r||s.open,children:s.disableHoverableContent?S.jsx(jv,{side:o,...i,ref:t}):S.jsx(QS,{side:o,...i,ref:t})})}),QS=m.forwardRef((e,t)=>{const n=yl(no,e.__scopeTooltip),r=Ov(no,e.__scopeTooltip),o=m.useRef(null),i=De(t,o),[s,l]=m.useState(null),{trigger:a,onClose:u}=n,f=o.current,{onPointerInTransitChange:d}=r,c=m.useCallback(()=>{l(null),d(!1)},[d]),y=m.useCallback((x,v)=>{const w=x.currentTarget,h={x:x.clientX,y:x.clientY},p=XS(h,w.getBoundingClientRect()),g=qS(h,p),E=ZS(v.getBoundingClientRect()),C=eE([...g,...E]);l(C),d(!0)},[d]);return m.useEffect(()=>()=>c(),[c]),m.useEffect(()=>{if(a&&f){const x=w=>y(w,f),v=w=>y(w,a);return a.addEventListener("pointerleave",x),f.addEventListener("pointerleave",v),()=>{a.removeEventListener("pointerleave",x),f.removeEventListener("pointerleave",v)}}},[a,f,y,c]),m.useEffect(()=>{if(s){const x=v=>{const w=v.target,h={x:v.clientX,y:v.clientY},p=(a==null?void 0:a.contains(w))||(f==null?void 0:f.contains(w)),g=!JS(h,s);p?c():g&&(c(),u())};return document.addEventListener("pointermove",x),()=>document.removeEventListener("pointermove",x)}},[a,f,s,u,c]),S.jsx(jv,{...e,ref:i})}),[KS,GS]=gl(Av,{isInside:!1}),jv=m.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:i,onPointerDownOutside:s,...l}=e,a=yl(no,n),u=Dc(n),{onClose:f}=a;return m.useEffect(()=>(document.addEventListener(Tf,f),()=>document.removeEventListener(Tf,f)),[f]),m.useEffect(()=>{if(a.trigger){const d=c=>{const y=c.target;y!=null&&y.contains(a.trigger)&&f()};return window.addEventListener("scroll",d,{capture:!0}),()=>window.removeEventListener("scroll",d,{capture:!0})}},[a.trigger,f]),S.jsx(cl,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:d=>d.preventDefault(),onDismiss:f,children:S.jsxs(zS,{"data-state":a.stateAttribute,...u,...l,ref:t,style:{...l.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[S.jsx(Pm,{children:r}),S.jsx(KS,{scope:n,isInside:!0,children:S.jsx(Vw,{id:a.contentId,role:"tooltip",children:o||r})})]})})});Mv.displayName=no;var Lv="TooltipArrow",YS=m.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Dc(n);return GS(Lv,n).isInside?null:S.jsx($S,{...o,...r,ref:t})});YS.displayName=Lv;function XS(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function qS(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function ZS(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function JS(e,t){const{x:n,y:r}=e;let o=!1;for(let i=0,s=t.length-1;i<t.length;s=i++){const l=t[i].x,a=t[i].y,u=t[s].x,f=t[s].y;a>r!=f>r&&n<(u-l)*(r-a)/(f-a)+l&&(o=!o)}return o}function eE(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),tE(t)}function tE(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const i=t[t.length-1],s=t[t.length-2];if((i.x-s.x)*(o.y-s.y)>=(i.y-s.y)*(o.x-s.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const i=n[n.length-1],s=n[n.length-2];if((i.x-s.x)*(o.y-s.y)>=(i.y-s.y)*(o.x-s.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var nE=_v,Iv=Mv;const rE=nE,oE=m.forwardRef(({className:e,sideOffset:t=4,...n},r)=>S.jsx(Iv,{ref:r,sideOffset:t,className:ve("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n}));oE.displayName=Iv.displayName;var wl=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},xl=typeof window>"u"||"Deno"in globalThis;function mt(){}function iE(e,t){return typeof e=="function"?e(t):e}function sE(e){return typeof e=="number"&&e>=0&&e!==1/0}function lE(e,t){return Math.max(e+(t||0)-Date.now(),0)}function Nf(e,t){return typeof e=="function"?e(t):e}function aE(e,t){return typeof e=="function"?e(t):e}function Rf(e,t){const{type:n="all",exact:r,fetchStatus:o,predicate:i,queryKey:s,stale:l}=e;if(s){if(r){if(t.queryHash!==Fc(s,t.options))return!1}else if(!ai(t.queryKey,s))return!1}if(n!=="all"){const a=t.isActive();if(n==="active"&&!a||n==="inactive"&&a)return!1}return!(typeof l=="boolean"&&t.isStale()!==l||o&&o!==t.state.fetchStatus||i&&!i(t))}function Of(e,t){const{exact:n,status:r,predicate:o,mutationKey:i}=e;if(i){if(!t.options.mutationKey)return!1;if(n){if(li(t.options.mutationKey)!==li(i))return!1}else if(!ai(t.options.mutationKey,i))return!1}return!(r&&t.state.status!==r||o&&!o(t))}function Fc(e,t){return((t==null?void 0:t.queryKeyHashFn)||li)(e)}function li(e){return JSON.stringify(e,(t,n)=>Cu(n)?Object.keys(n).sort().reduce((r,o)=>(r[o]=n[o],r),{}):n)}function ai(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(n=>!ai(e[n],t[n])):!1}function Dv(e,t){if(e===t)return e;const n=_f(e)&&_f(t);if(n||Cu(e)&&Cu(t)){const r=n?e:Object.keys(e),o=r.length,i=n?t:Object.keys(t),s=i.length,l=n?[]:{};let a=0;for(let u=0;u<s;u++){const f=n?u:i[u];(!n&&r.includes(f)||n)&&e[f]===void 0&&t[f]===void 0?(l[f]=void 0,a++):(l[f]=Dv(e[f],t[f]),l[f]===e[f]&&e[f]!==void 0&&a++)}return o===s&&a===o?e:l}return t}function _f(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Cu(e){if(!Af(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!Af(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function Af(e){return Object.prototype.toString.call(e)==="[object Object]"}function uE(e){return new Promise(t=>{setTimeout(t,e)})}function cE(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?Dv(e,t):t}function dE(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function fE(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var zc=Symbol();function Fv(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===zc?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var Qn,pn,Fr,ep,pE=(ep=class extends wl{constructor(){super();q(this,Qn);q(this,pn);q(this,Fr);K(this,Fr,t=>{if(!xl&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){N(this,pn)||this.setEventListener(N(this,Fr))}onUnsubscribe(){var t;this.hasListeners()||((t=N(this,pn))==null||t.call(this),K(this,pn,void 0))}setEventListener(t){var n;K(this,Fr,t),(n=N(this,pn))==null||n.call(this),K(this,pn,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){N(this,Qn)!==t&&(K(this,Qn,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof N(this,Qn)=="boolean"?N(this,Qn):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},Qn=new WeakMap,pn=new WeakMap,Fr=new WeakMap,ep),zv=new pE,zr,hn,$r,tp,hE=(tp=class extends wl{constructor(){super();q(this,zr,!0);q(this,hn);q(this,$r);K(this,$r,t=>{if(!xl&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){N(this,hn)||this.setEventListener(N(this,$r))}onUnsubscribe(){var t;this.hasListeners()||((t=N(this,hn))==null||t.call(this),K(this,hn,void 0))}setEventListener(t){var n;K(this,$r,t),(n=N(this,hn))==null||n.call(this),K(this,hn,t(this.setOnline.bind(this)))}setOnline(t){N(this,zr)!==t&&(K(this,zr,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return N(this,zr)}},zr=new WeakMap,hn=new WeakMap,$r=new WeakMap,tp),Ws=new hE;function mE(){let e,t;const n=new Promise((o,i)=>{e=o,t=i});n.status="pending",n.catch(()=>{});function r(o){Object.assign(n,o),delete n.resolve,delete n.reject}return n.resolve=o=>{r({status:"fulfilled",value:o}),e(o)},n.reject=o=>{r({status:"rejected",reason:o}),t(o)},n}function vE(e){return Math.min(1e3*2**e,3e4)}function $v(e){return(e??"online")==="online"?Ws.isOnline():!0}var Uv=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function ua(e){return e instanceof Uv}function Bv(e){let t=!1,n=0,r=!1,o;const i=mE(),s=v=>{var w;r||(c(new Uv(v)),(w=e.abort)==null||w.call(e))},l=()=>{t=!0},a=()=>{t=!1},u=()=>zv.isFocused()&&(e.networkMode==="always"||Ws.isOnline())&&e.canRun(),f=()=>$v(e.networkMode)&&e.canRun(),d=v=>{var w;r||(r=!0,(w=e.onSuccess)==null||w.call(e,v),o==null||o(),i.resolve(v))},c=v=>{var w;r||(r=!0,(w=e.onError)==null||w.call(e,v),o==null||o(),i.reject(v))},y=()=>new Promise(v=>{var w;o=h=>{(r||u())&&v(h)},(w=e.onPause)==null||w.call(e)}).then(()=>{var v;o=void 0,r||(v=e.onContinue)==null||v.call(e)}),x=()=>{if(r)return;let v;const w=n===0?e.initialPromise:void 0;try{v=w??e.fn()}catch(h){v=Promise.reject(h)}Promise.resolve(v).then(d).catch(h=>{var P;if(r)return;const p=e.retry??(xl?0:3),g=e.retryDelay??vE,E=typeof g=="function"?g(n,h):g,C=p===!0||typeof p=="number"&&n<p||typeof p=="function"&&p(n,h);if(t||!C){c(h);return}n++,(P=e.onFail)==null||P.call(e,n,h),uE(E).then(()=>u()?void 0:y()).then(()=>{t?c(h):x()})})};return{promise:i,cancel:s,continue:()=>(o==null||o(),i),cancelRetry:l,continueRetry:a,canStart:f,start:()=>(f()?x():y().then(x),i)}}function gE(){let e=[],t=0,n=l=>{l()},r=l=>{l()},o=l=>setTimeout(l,0);const i=l=>{t?e.push(l):o(()=>{n(l)})},s=()=>{const l=e;e=[],l.length&&o(()=>{r(()=>{l.forEach(a=>{n(a)})})})};return{batch:l=>{let a;t++;try{a=l()}finally{t--,t||s()}return a},batchCalls:l=>(...a)=>{i(()=>{l(...a)})},schedule:i,setNotifyFunction:l=>{n=l},setBatchNotifyFunction:l=>{r=l},setScheduler:l=>{o=l}}}var Le=gE(),Kn,np,Wv=(np=class{constructor(){q(this,Kn)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),sE(this.gcTime)&&K(this,Kn,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(xl?1/0:5*60*1e3))}clearGcTimeout(){N(this,Kn)&&(clearTimeout(N(this,Kn)),K(this,Kn,void 0))}},Kn=new WeakMap,np),Ur,Br,rt,Oe,di,Gn,vt,zt,rp,yE=(rp=class extends Wv{constructor(t){super();q(this,vt);q(this,Ur);q(this,Br);q(this,rt);q(this,Oe);q(this,di);q(this,Gn);K(this,Gn,!1),K(this,di,t.defaultOptions),this.setOptions(t.options),this.observers=[],K(this,rt,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,K(this,Ur,xE(this.options)),this.state=t.state??N(this,Ur),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=N(this,Oe))==null?void 0:t.promise}setOptions(t){this.options={...N(this,di),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&N(this,rt).remove(this)}setData(t,n){const r=cE(this.state.data,t,this.options);return Te(this,vt,zt).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){Te(this,vt,zt).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r,o;const n=(r=N(this,Oe))==null?void 0:r.promise;return(o=N(this,Oe))==null||o.cancel(t),n?n.then(mt).catch(mt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(N(this,Ur))}isActive(){return this.observers.some(t=>aE(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===zc||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!lE(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=N(this,Oe))==null||n.continue()}onOnline(){var n;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=N(this,Oe))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),N(this,rt).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(N(this,Oe)&&(N(this,Gn)?N(this,Oe).cancel({revert:!0}):N(this,Oe).cancelRetry()),this.scheduleGc()),N(this,rt).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||Te(this,vt,zt).call(this,{type:"invalidate"})}fetch(t,n){var a,u,f;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(N(this,Oe))return N(this,Oe).continueRetry(),N(this,Oe).promise}if(t&&this.setOptions(t),!this.options.queryFn){const d=this.observers.find(c=>c.options.queryFn);d&&this.setOptions(d.options)}const r=new AbortController,o=d=>{Object.defineProperty(d,"signal",{enumerable:!0,get:()=>(K(this,Gn,!0),r.signal)})},i=()=>{const d=Fv(this.options,n),c={queryKey:this.queryKey,meta:this.meta};return o(c),K(this,Gn,!1),this.options.persister?this.options.persister(d,c,this):d(c)},s={fetchOptions:n,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:i};o(s),(a=this.options.behavior)==null||a.onFetch(s,this),K(this,Br,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((u=s.fetchOptions)==null?void 0:u.meta))&&Te(this,vt,zt).call(this,{type:"fetch",meta:(f=s.fetchOptions)==null?void 0:f.meta});const l=d=>{var c,y,x,v;ua(d)&&d.silent||Te(this,vt,zt).call(this,{type:"error",error:d}),ua(d)||((y=(c=N(this,rt).config).onError)==null||y.call(c,d,this),(v=(x=N(this,rt).config).onSettled)==null||v.call(x,this.state.data,d,this)),this.scheduleGc()};return K(this,Oe,Bv({initialPromise:n==null?void 0:n.initialPromise,fn:s.fetchFn,abort:r.abort.bind(r),onSuccess:d=>{var c,y,x,v;if(d===void 0){l(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(d)}catch(w){l(w);return}(y=(c=N(this,rt).config).onSuccess)==null||y.call(c,d,this),(v=(x=N(this,rt).config).onSettled)==null||v.call(x,d,this.state.error,this),this.scheduleGc()},onError:l,onFail:(d,c)=>{Te(this,vt,zt).call(this,{type:"failed",failureCount:d,error:c})},onPause:()=>{Te(this,vt,zt).call(this,{type:"pause"})},onContinue:()=>{Te(this,vt,zt).call(this,{type:"continue"})},retry:s.options.retry,retryDelay:s.options.retryDelay,networkMode:s.options.networkMode,canRun:()=>!0})),N(this,Oe).start()}},Ur=new WeakMap,Br=new WeakMap,rt=new WeakMap,Oe=new WeakMap,di=new WeakMap,Gn=new WeakMap,vt=new WeakSet,zt=function(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...wE(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const o=t.error;return ua(o)&&o.revert&&N(this,Br)?{...N(this,Br),fetchStatus:"idle"}:{...r,error:o,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:o,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),Le.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),N(this,rt).notify({query:this,type:"updated",action:t})})},rp);function wE(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:$v(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function xE(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var Rt,op,SE=(op=class extends wl{constructor(t={}){super();q(this,Rt);this.config=t,K(this,Rt,new Map)}build(t,n,r){const o=n.queryKey,i=n.queryHash??Fc(o,n);let s=this.get(i);return s||(s=new yE({cache:this,queryKey:o,queryHash:i,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(o)}),this.add(s)),s}add(t){N(this,Rt).has(t.queryHash)||(N(this,Rt).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=N(this,Rt).get(t.queryHash);n&&(t.destroy(),n===t&&N(this,Rt).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){Le.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return N(this,Rt).get(t)}getAll(){return[...N(this,Rt).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>Rf(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>Rf(t,r)):n}notify(t){Le.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){Le.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){Le.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},Rt=new WeakMap,op),Ot,Me,Yn,_t,ln,ip,EE=(ip=class extends Wv{constructor(t){super();q(this,_t);q(this,Ot);q(this,Me);q(this,Yn);this.mutationId=t.mutationId,K(this,Me,t.mutationCache),K(this,Ot,[]),this.state=t.state||CE(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){N(this,Ot).includes(t)||(N(this,Ot).push(t),this.clearGcTimeout(),N(this,Me).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){K(this,Ot,N(this,Ot).filter(n=>n!==t)),this.scheduleGc(),N(this,Me).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){N(this,Ot).length||(this.state.status==="pending"?this.scheduleGc():N(this,Me).remove(this))}continue(){var t;return((t=N(this,Yn))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var o,i,s,l,a,u,f,d,c,y,x,v,w,h,p,g,E,C,P,b;K(this,Yn,Bv({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(T,D)=>{Te(this,_t,ln).call(this,{type:"failed",failureCount:T,error:D})},onPause:()=>{Te(this,_t,ln).call(this,{type:"pause"})},onContinue:()=>{Te(this,_t,ln).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>N(this,Me).canRun(this)}));const n=this.state.status==="pending",r=!N(this,Yn).canStart();try{if(!n){Te(this,_t,ln).call(this,{type:"pending",variables:t,isPaused:r}),await((i=(o=N(this,Me).config).onMutate)==null?void 0:i.call(o,t,this));const D=await((l=(s=this.options).onMutate)==null?void 0:l.call(s,t));D!==this.state.context&&Te(this,_t,ln).call(this,{type:"pending",context:D,variables:t,isPaused:r})}const T=await N(this,Yn).start();return await((u=(a=N(this,Me).config).onSuccess)==null?void 0:u.call(a,T,t,this.state.context,this)),await((d=(f=this.options).onSuccess)==null?void 0:d.call(f,T,t,this.state.context)),await((y=(c=N(this,Me).config).onSettled)==null?void 0:y.call(c,T,null,this.state.variables,this.state.context,this)),await((v=(x=this.options).onSettled)==null?void 0:v.call(x,T,null,t,this.state.context)),Te(this,_t,ln).call(this,{type:"success",data:T}),T}catch(T){try{throw await((h=(w=N(this,Me).config).onError)==null?void 0:h.call(w,T,t,this.state.context,this)),await((g=(p=this.options).onError)==null?void 0:g.call(p,T,t,this.state.context)),await((C=(E=N(this,Me).config).onSettled)==null?void 0:C.call(E,void 0,T,this.state.variables,this.state.context,this)),await((b=(P=this.options).onSettled)==null?void 0:b.call(P,void 0,T,t,this.state.context)),T}finally{Te(this,_t,ln).call(this,{type:"error",error:T})}}finally{N(this,Me).runNext(this)}}},Ot=new WeakMap,Me=new WeakMap,Yn=new WeakMap,_t=new WeakSet,ln=function(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),Le.batch(()=>{N(this,Ot).forEach(r=>{r.onMutationUpdate(t)}),N(this,Me).notify({mutation:this,type:"updated",action:t})})},ip);function CE(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var Ke,fi,sp,kE=(sp=class extends wl{constructor(t={}){super();q(this,Ke);q(this,fi);this.config=t,K(this,Ke,new Map),K(this,fi,Date.now())}build(t,n,r){const o=new EE({mutationCache:this,mutationId:++ki(this,fi)._,options:t.defaultMutationOptions(n),state:r});return this.add(o),o}add(t){const n=Qi(t),r=N(this,Ke).get(n)??[];r.push(t),N(this,Ke).set(n,r),this.notify({type:"added",mutation:t})}remove(t){var r;const n=Qi(t);if(N(this,Ke).has(n)){const o=(r=N(this,Ke).get(n))==null?void 0:r.filter(i=>i!==t);o&&(o.length===0?N(this,Ke).delete(n):N(this,Ke).set(n,o))}this.notify({type:"removed",mutation:t})}canRun(t){var r;const n=(r=N(this,Ke).get(Qi(t)))==null?void 0:r.find(o=>o.state.status==="pending");return!n||n===t}runNext(t){var r;const n=(r=N(this,Ke).get(Qi(t)))==null?void 0:r.find(o=>o!==t&&o.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}clear(){Le.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}getAll(){return[...N(this,Ke).values()].flat()}find(t){const n={exact:!0,...t};return this.getAll().find(r=>Of(n,r))}findAll(t={}){return this.getAll().filter(n=>Of(t,n))}notify(t){Le.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return Le.batch(()=>Promise.all(t.map(n=>n.continue().catch(mt))))}},Ke=new WeakMap,fi=new WeakMap,sp);function Qi(e){var t;return((t=e.options.scope)==null?void 0:t.id)??String(e.mutationId)}function Mf(e){return{onFetch:(t,n)=>{var f,d,c,y,x;const r=t.options,o=(c=(d=(f=t.fetchOptions)==null?void 0:f.meta)==null?void 0:d.fetchMore)==null?void 0:c.direction,i=((y=t.state.data)==null?void 0:y.pages)||[],s=((x=t.state.data)==null?void 0:x.pageParams)||[];let l={pages:[],pageParams:[]},a=0;const u=async()=>{let v=!1;const w=g=>{Object.defineProperty(g,"signal",{enumerable:!0,get:()=>(t.signal.aborted?v=!0:t.signal.addEventListener("abort",()=>{v=!0}),t.signal)})},h=Fv(t.options,t.fetchOptions),p=async(g,E,C)=>{if(v)return Promise.reject();if(E==null&&g.pages.length)return Promise.resolve(g);const P={queryKey:t.queryKey,pageParam:E,direction:C?"backward":"forward",meta:t.options.meta};w(P);const b=await h(P),{maxPages:T}=t.options,D=C?fE:dE;return{pages:D(g.pages,b,T),pageParams:D(g.pageParams,E,T)}};if(o&&i.length){const g=o==="backward",E=g?PE:jf,C={pages:i,pageParams:s},P=E(r,C);l=await p(C,P,g)}else{const g=e??i.length;do{const E=a===0?s[0]??r.initialPageParam:jf(r,l);if(a>0&&E==null)break;l=await p(l,E),a++}while(a<g)}return l};t.options.persister?t.fetchFn=()=>{var v,w;return(w=(v=t.options).persister)==null?void 0:w.call(v,u,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=u}}}function jf(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function PE(e,{pages:t,pageParams:n}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n):void 0}var fe,mn,vn,Wr,Hr,gn,Vr,Qr,lp,bE=(lp=class{constructor(e={}){q(this,fe);q(this,mn);q(this,vn);q(this,Wr);q(this,Hr);q(this,gn);q(this,Vr);q(this,Qr);K(this,fe,e.queryCache||new SE),K(this,mn,e.mutationCache||new kE),K(this,vn,e.defaultOptions||{}),K(this,Wr,new Map),K(this,Hr,new Map),K(this,gn,0)}mount(){ki(this,gn)._++,N(this,gn)===1&&(K(this,Vr,zv.subscribe(async e=>{e&&(await this.resumePausedMutations(),N(this,fe).onFocus())})),K(this,Qr,Ws.subscribe(async e=>{e&&(await this.resumePausedMutations(),N(this,fe).onOnline())})))}unmount(){var e,t;ki(this,gn)._--,N(this,gn)===0&&((e=N(this,Vr))==null||e.call(this),K(this,Vr,void 0),(t=N(this,Qr))==null||t.call(this),K(this,Qr,void 0))}isFetching(e){return N(this,fe).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return N(this,mn).findAll({...e,status:"pending"}).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=N(this,fe).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);if(t===void 0)return this.fetchQuery(e);{const n=this.defaultQueryOptions(e),r=N(this,fe).build(this,n);return e.revalidateIfStale&&r.isStaleByTime(Nf(n.staleTime,r))&&this.prefetchQuery(n),Promise.resolve(t)}}getQueriesData(e){return N(this,fe).findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),o=N(this,fe).get(r.queryHash),i=o==null?void 0:o.state.data,s=iE(t,i);if(s!==void 0)return N(this,fe).build(this,r).setData(s,{...n,manual:!0})}setQueriesData(e,t,n){return Le.batch(()=>N(this,fe).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=N(this,fe).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=N(this,fe);Le.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=N(this,fe),r={type:"active",...e};return Le.batch(()=>(n.findAll(e).forEach(o=>{o.reset()}),this.refetchQueries(r,t)))}cancelQueries(e={},t={}){const n={revert:!0,...t},r=Le.batch(()=>N(this,fe).findAll(e).map(o=>o.cancel(n)));return Promise.all(r).then(mt).catch(mt)}invalidateQueries(e={},t={}){return Le.batch(()=>{if(N(this,fe).findAll(e).forEach(r=>{r.invalidate()}),e.refetchType==="none")return Promise.resolve();const n={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(n,t)})}refetchQueries(e={},t){const n={...t,cancelRefetch:(t==null?void 0:t.cancelRefetch)??!0},r=Le.batch(()=>N(this,fe).findAll(e).filter(o=>!o.isDisabled()).map(o=>{let i=o.fetch(void 0,n);return n.throwOnError||(i=i.catch(mt)),o.state.fetchStatus==="paused"?Promise.resolve():i}));return Promise.all(r).then(mt)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=N(this,fe).build(this,t);return n.isStaleByTime(Nf(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(mt).catch(mt)}fetchInfiniteQuery(e){return e.behavior=Mf(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(mt).catch(mt)}ensureInfiniteQueryData(e){return e.behavior=Mf(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Ws.isOnline()?N(this,mn).resumePausedMutations():Promise.resolve()}getQueryCache(){return N(this,fe)}getMutationCache(){return N(this,mn)}getDefaultOptions(){return N(this,vn)}setDefaultOptions(e){K(this,vn,e)}setQueryDefaults(e,t){N(this,Wr).set(li(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...N(this,Wr).values()];let n={};return t.forEach(r=>{ai(e,r.queryKey)&&(n={...n,...r.defaultOptions})}),n}setMutationDefaults(e,t){N(this,Hr).set(li(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...N(this,Hr).values()];let n={};return t.forEach(r=>{ai(e,r.mutationKey)&&(n={...n,...r.defaultOptions})}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...N(this,vn).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=Fc(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.enabled!==!0&&t.queryFn===zc&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...N(this,vn).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){N(this,fe).clear(),N(this,mn).clear()}},fe=new WeakMap,mn=new WeakMap,vn=new WeakMap,Wr=new WeakMap,Hr=new WeakMap,gn=new WeakMap,Vr=new WeakMap,Qr=new WeakMap,lp),TE=m.createContext(void 0),NE=({client:e,children:t})=>(m.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),S.jsx(TE.Provider,{value:e,children:t}));/**
 * @remix-run/router v1.20.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ui(){return ui=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ui.apply(this,arguments)}var xn;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(xn||(xn={}));const Lf="popstate";function RE(e){e===void 0&&(e={});function t(r,o){let{pathname:i,search:s,hash:l}=r.location;return ku("",{pathname:i,search:s,hash:l},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:Vv(o)}return _E(t,n,null,e)}function xe(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Hv(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function OE(){return Math.random().toString(36).substr(2,8)}function If(e,t){return{usr:e.state,key:e.key,idx:t}}function ku(e,t,n,r){return n===void 0&&(n=null),ui({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?co(t):t,{state:n,key:t&&t.key||r||OE()})}function Vv(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function co(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function _E(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:i=!1}=r,s=o.history,l=xn.Pop,a=null,u=f();u==null&&(u=0,s.replaceState(ui({},s.state,{idx:u}),""));function f(){return(s.state||{idx:null}).idx}function d(){l=xn.Pop;let w=f(),h=w==null?null:w-u;u=w,a&&a({action:l,location:v.location,delta:h})}function c(w,h){l=xn.Push;let p=ku(v.location,w,h);u=f()+1;let g=If(p,u),E=v.createHref(p);try{s.pushState(g,"",E)}catch(C){if(C instanceof DOMException&&C.name==="DataCloneError")throw C;o.location.assign(E)}i&&a&&a({action:l,location:v.location,delta:1})}function y(w,h){l=xn.Replace;let p=ku(v.location,w,h);u=f();let g=If(p,u),E=v.createHref(p);s.replaceState(g,"",E),i&&a&&a({action:l,location:v.location,delta:0})}function x(w){let h=o.location.origin!=="null"?o.location.origin:o.location.href,p=typeof w=="string"?w:Vv(w);return p=p.replace(/ $/,"%20"),xe(h,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,h)}let v={get action(){return l},get location(){return e(o,s)},listen(w){if(a)throw new Error("A history only accepts one active listener");return o.addEventListener(Lf,d),a=w,()=>{o.removeEventListener(Lf,d),a=null}},createHref(w){return t(o,w)},createURL:x,encodeLocation(w){let h=x(w);return{pathname:h.pathname,search:h.search,hash:h.hash}},push:c,replace:y,go(w){return s.go(w)}};return v}var Df;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Df||(Df={}));function AE(e,t,n){return n===void 0&&(n="/"),ME(e,t,n,!1)}function ME(e,t,n,r){let o=typeof t=="string"?co(t):t,i=Gv(o.pathname||"/",n);if(i==null)return null;let s=Qv(e);jE(s);let l=null;for(let a=0;l==null&&a<s.length;++a){let u=VE(i);l=WE(s[a],u,r)}return l}function Qv(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(i,s,l)=>{let a={relativePath:l===void 0?i.path||"":l,caseSensitive:i.caseSensitive===!0,childrenIndex:s,route:i};a.relativePath.startsWith("/")&&(xe(a.relativePath.startsWith(r),'Absolute route path "'+a.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),a.relativePath=a.relativePath.slice(r.length));let u=Zn([r,a.relativePath]),f=n.concat(a);i.children&&i.children.length>0&&(xe(i.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Qv(i.children,t,f,u)),!(i.path==null&&!i.index)&&t.push({path:u,score:UE(u,i.index),routesMeta:f})};return e.forEach((i,s)=>{var l;if(i.path===""||!((l=i.path)!=null&&l.includes("?")))o(i,s);else for(let a of Kv(i.path))o(i,s,a)}),t}function Kv(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),i=n.replace(/\?$/,"");if(r.length===0)return o?[i,""]:[i];let s=Kv(r.join("/")),l=[];return l.push(...s.map(a=>a===""?i:[i,a].join("/"))),o&&l.push(...s),l.map(a=>e.startsWith("/")&&a===""?"/":a)}function jE(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:BE(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const LE=/^:[\w-]+$/,IE=3,DE=2,FE=1,zE=10,$E=-2,Ff=e=>e==="*";function UE(e,t){let n=e.split("/"),r=n.length;return n.some(Ff)&&(r+=$E),t&&(r+=DE),n.filter(o=>!Ff(o)).reduce((o,i)=>o+(LE.test(i)?IE:i===""?FE:zE),r)}function BE(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function WE(e,t,n){let{routesMeta:r}=e,o={},i="/",s=[];for(let l=0;l<r.length;++l){let a=r[l],u=l===r.length-1,f=i==="/"?t:t.slice(i.length)||"/",d=zf({path:a.relativePath,caseSensitive:a.caseSensitive,end:u},f),c=a.route;if(!d&&u&&n&&!r[r.length-1].route.index&&(d=zf({path:a.relativePath,caseSensitive:a.caseSensitive,end:!1},f)),!d)return null;Object.assign(o,d.params),s.push({params:o,pathname:Zn([i,d.pathname]),pathnameBase:qE(Zn([i,d.pathnameBase])),route:c}),d.pathnameBase!=="/"&&(i=Zn([i,d.pathnameBase]))}return s}function zf(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=HE(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let i=o[0],s=i.replace(/(.)\/+$/,"$1"),l=o.slice(1);return{params:r.reduce((u,f,d)=>{let{paramName:c,isOptional:y}=f;if(c==="*"){let v=l[d]||"";s=i.slice(0,i.length-v.length).replace(/(.)\/+$/,"$1")}const x=l[d];return y&&!x?u[c]=void 0:u[c]=(x||"").replace(/%2F/g,"/"),u},{}),pathname:i,pathnameBase:s,pattern:e}}function HE(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),Hv(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(s,l,a)=>(r.push({paramName:l,isOptional:a!=null}),a?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function VE(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Hv(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Gv(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function QE(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:o=""}=typeof e=="string"?co(e):e;return{pathname:n?n.startsWith("/")?n:KE(n,t):t,search:ZE(r),hash:JE(o)}}function KE(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(o=>{o===".."?n.length>1&&n.pop():o!=="."&&n.push(o)}),n.length>1?n.join("/"):"/"}function ca(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function GE(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function YE(e,t){let n=GE(e);return t?n.map((r,o)=>o===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function XE(e,t,n,r){r===void 0&&(r=!1);let o;typeof e=="string"?o=co(e):(o=ui({},e),xe(!o.pathname||!o.pathname.includes("?"),ca("?","pathname","search",o)),xe(!o.pathname||!o.pathname.includes("#"),ca("#","pathname","hash",o)),xe(!o.search||!o.search.includes("#"),ca("#","search","hash",o)));let i=e===""||o.pathname==="",s=i?"/":o.pathname,l;if(s==null)l=n;else{let d=t.length-1;if(!r&&s.startsWith("..")){let c=s.split("/");for(;c[0]==="..";)c.shift(),d-=1;o.pathname=c.join("/")}l=d>=0?t[d]:"/"}let a=QE(o,l),u=s&&s!=="/"&&s.endsWith("/"),f=(i||s===".")&&n.endsWith("/");return!a.pathname.endsWith("/")&&(u||f)&&(a.pathname+="/"),a}const Zn=e=>e.join("/").replace(/\/\/+/g,"/"),qE=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),ZE=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,JE=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function eC(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Yv=["post","put","patch","delete"];new Set(Yv);const tC=["get",...Yv];new Set(tC);/**
 * React Router v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ci(){return ci=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ci.apply(this,arguments)}const $c=m.createContext(null),nC=m.createContext(null),Sl=m.createContext(null),El=m.createContext(null),fo=m.createContext({outlet:null,matches:[],isDataRoute:!1}),Xv=m.createContext(null);function Cl(){return m.useContext(El)!=null}function qv(){return Cl()||xe(!1),m.useContext(El).location}function Zv(e){m.useContext(Sl).static||m.useLayoutEffect(e)}function Jv(){let{isDataRoute:e}=m.useContext(fo);return e?mC():rC()}function rC(){Cl()||xe(!1);let e=m.useContext($c),{basename:t,future:n,navigator:r}=m.useContext(Sl),{matches:o}=m.useContext(fo),{pathname:i}=qv(),s=JSON.stringify(YE(o,n.v7_relativeSplatPath)),l=m.useRef(!1);return Zv(()=>{l.current=!0}),m.useCallback(function(u,f){if(f===void 0&&(f={}),!l.current)return;if(typeof u=="number"){r.go(u);return}let d=XE(u,JSON.parse(s),i,f.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:Zn([t,d.pathname])),(f.replace?r.replace:r.push)(d,f.state,f)},[t,r,s,i,e])}function oC(e,t){return iC(e,t)}function iC(e,t,n,r){Cl()||xe(!1);let{navigator:o}=m.useContext(Sl),{matches:i}=m.useContext(fo),s=i[i.length-1],l=s?s.params:{};s&&s.pathname;let a=s?s.pathnameBase:"/";s&&s.route;let u=qv(),f;if(t){var d;let w=typeof t=="string"?co(t):t;a==="/"||(d=w.pathname)!=null&&d.startsWith(a)||xe(!1),f=w}else f=u;let c=f.pathname||"/",y=c;if(a!=="/"){let w=a.replace(/^\//,"").split("/");y="/"+c.replace(/^\//,"").split("/").slice(w.length).join("/")}let x=AE(e,{pathname:y}),v=cC(x&&x.map(w=>Object.assign({},w,{params:Object.assign({},l,w.params),pathname:Zn([a,o.encodeLocation?o.encodeLocation(w.pathname).pathname:w.pathname]),pathnameBase:w.pathnameBase==="/"?a:Zn([a,o.encodeLocation?o.encodeLocation(w.pathnameBase).pathname:w.pathnameBase])})),i,n,r);return t&&v?m.createElement(El.Provider,{value:{location:ci({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:xn.Pop}},v):v}function sC(){let e=hC(),t=eC(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return m.createElement(m.Fragment,null,m.createElement("h2",null,"Unexpected Application Error!"),m.createElement("h3",{style:{fontStyle:"italic"}},t),n?m.createElement("pre",{style:o},n):null,null)}const lC=m.createElement(sC,null);class aC extends m.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?m.createElement(fo.Provider,{value:this.props.routeContext},m.createElement(Xv.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function uC(e){let{routeContext:t,match:n,children:r}=e,o=m.useContext($c);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),m.createElement(fo.Provider,{value:t},r)}function cC(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var i;if(!n)return null;if(n.errors)e=n.matches;else if((i=r)!=null&&i.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let s=e,l=(o=n)==null?void 0:o.errors;if(l!=null){let f=s.findIndex(d=>d.route.id&&(l==null?void 0:l[d.route.id])!==void 0);f>=0||xe(!1),s=s.slice(0,Math.min(s.length,f+1))}let a=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let f=0;f<s.length;f++){let d=s[f];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(u=f),d.route.id){let{loaderData:c,errors:y}=n,x=d.route.loader&&c[d.route.id]===void 0&&(!y||y[d.route.id]===void 0);if(d.route.lazy||x){a=!0,u>=0?s=s.slice(0,u+1):s=[s[0]];break}}}return s.reduceRight((f,d,c)=>{let y,x=!1,v=null,w=null;n&&(y=l&&d.route.id?l[d.route.id]:void 0,v=d.route.errorElement||lC,a&&(u<0&&c===0?(x=!0,w=null):u===c&&(x=!0,w=d.route.hydrateFallbackElement||null)));let h=t.concat(s.slice(0,c+1)),p=()=>{let g;return y?g=v:x?g=w:d.route.Component?g=m.createElement(d.route.Component,null):d.route.element?g=d.route.element:g=f,m.createElement(uC,{match:d,routeContext:{outlet:f,matches:h,isDataRoute:n!=null},children:g})};return n&&(d.route.ErrorBoundary||d.route.errorElement||c===0)?m.createElement(aC,{location:n.location,revalidation:n.revalidation,component:v,error:y,children:p(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):p()},null)}var eg=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(eg||{}),Hs=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Hs||{});function dC(e){let t=m.useContext($c);return t||xe(!1),t}function fC(e){let t=m.useContext(nC);return t||xe(!1),t}function pC(e){let t=m.useContext(fo);return t||xe(!1),t}function tg(e){let t=pC(),n=t.matches[t.matches.length-1];return n.route.id||xe(!1),n.route.id}function hC(){var e;let t=m.useContext(Xv),n=fC(Hs.UseRouteError),r=tg(Hs.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function mC(){let{router:e}=dC(eg.UseNavigateStable),t=tg(Hs.UseNavigateStable),n=m.useRef(!1);return Zv(()=>{n.current=!0}),m.useCallback(function(o,i){i===void 0&&(i={}),n.current&&(typeof o=="number"?e.navigate(o):e.navigate(o,ci({fromRouteId:t},i)))},[e,t])}function Pu(e){xe(!1)}function vC(e){let{basename:t="/",children:n=null,location:r,navigationType:o=xn.Pop,navigator:i,static:s=!1,future:l}=e;Cl()&&xe(!1);let a=t.replace(/^\/*/,"/"),u=m.useMemo(()=>({basename:a,navigator:i,static:s,future:ci({v7_relativeSplatPath:!1},l)}),[a,l,i,s]);typeof r=="string"&&(r=co(r));let{pathname:f="/",search:d="",hash:c="",state:y=null,key:x="default"}=r,v=m.useMemo(()=>{let w=Gv(f,a);return w==null?null:{location:{pathname:w,search:d,hash:c,state:y,key:x},navigationType:o}},[a,f,d,c,y,x,o]);return v==null?null:m.createElement(Sl.Provider,{value:u},m.createElement(El.Provider,{children:n,value:v}))}function gC(e){let{children:t,location:n}=e;return oC(bu(t),n)}new Promise(()=>{});function bu(e,t){t===void 0&&(t=[]);let n=[];return m.Children.forEach(e,(r,o)=>{if(!m.isValidElement(r))return;let i=[...t,o];if(r.type===m.Fragment){n.push.apply(n,bu(r.props.children,i));return}r.type!==Pu&&xe(!1),!r.props.index||!r.props.children||xe(!1);let s={id:r.props.id||i.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(s.children=bu(r.props.children,i)),n.push(s)}),n}/**
 * React Router DOM v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */const yC="6";try{window.__reactRouterVersion=yC}catch{}const wC="startTransition",$f=wp[wC];function xC(e){let{basename:t,children:n,future:r,window:o}=e,i=m.useRef();i.current==null&&(i.current=RE({window:o,v5Compat:!0}));let s=i.current,[l,a]=m.useState({action:s.action,location:s.location}),{v7_startTransition:u}=r||{},f=m.useCallback(d=>{u&&$f?$f(()=>a(d)):a(d)},[a,u]);return m.useLayoutEffect(()=>s.listen(f),[s,f]),m.createElement(vC,{basename:t,children:n,location:l.location,navigationType:l.action,navigator:s,future:r})}var Uf;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Uf||(Uf={}));var Bf;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Bf||(Bf={}));const SC=Nc("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),$e=m.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},i)=>{const s=r?eo:"button";return S.jsx(s,{className:ve(SC({variant:t,size:n,className:e})),ref:i,...o})});$e.displayName="Button";const Vs=m.forwardRef(({className:e,type:t,...n},r)=>S.jsx("input",{type:t,className:ve("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...n}));Vs.displayName="Input";var EC="Label",ng=m.forwardRef((e,t)=>S.jsx(me.label,{...e,ref:t,onMouseDown:n=>{var o;n.target.closest("button, input, select, textarea")||((o=e.onMouseDown)==null||o.call(e,n),!n.defaultPrevented&&n.detail>1&&n.preventDefault())}}));ng.displayName=EC;var rg=ng;const CC=Nc("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),Tu=m.forwardRef(({className:e,...t},n)=>S.jsx(rg,{ref:n,className:ve(CC(),e),...t}));Tu.displayName=rg.displayName;const Uc=m.forwardRef(({className:e,...t},n)=>S.jsx("div",{ref:n,className:ve("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));Uc.displayName="Card";const kC=m.forwardRef(({className:e,...t},n)=>S.jsx("div",{ref:n,className:ve("flex flex-col space-y-1.5 p-6",e),...t}));kC.displayName="CardHeader";const PC=m.forwardRef(({className:e,...t},n)=>S.jsx("h3",{ref:n,className:ve("text-2xl font-semibold leading-none tracking-tight",e),...t}));PC.displayName="CardTitle";const bC=m.forwardRef(({className:e,...t},n)=>S.jsx("p",{ref:n,className:ve("text-sm text-muted-foreground",e),...t}));bC.displayName="CardDescription";const TC=m.forwardRef(({className:e,...t},n)=>S.jsx("div",{ref:n,className:ve("p-6 pt-0",e),...t}));TC.displayName="CardContent";const NC=m.forwardRef(({className:e,...t},n)=>S.jsx("div",{ref:n,className:ve("flex items-center p-6 pt-0",e),...t}));NC.displayName="CardFooter";const Sn={GROQ:"VITE_GROQ_API_KEY",HUGGINGFACE:"VITE_HUGGINGFACE_API_KEY"},Qs=e=>{switch(e){case Sn.GROQ:return"********************************************************";case Sn.HUGGINGFACE:return"*************************************";default:return}},Wf=(e,t)=>{localStorage.setItem(e,t)},RC=()=>{const e=Jv(),[t,n]=m.useState(""),[r,o]=m.useState("");m.useEffect(()=>{const s=Qs(Sn.GROQ),l=Qs(Sn.HUGGINGFACE);s&&l?e("/chat"):(n(s||""),o(l||""))},[e]);const i=()=>{Wf(Sn.GROQ,t),Wf(Sn.HUGGINGFACE,r),pt.success("API keys saved successfully!"),e("/chat")};return S.jsx("div",{className:"min-h-screen bg-gray-50 p-8",children:S.jsxs("div",{className:"max-w-2xl mx-auto space-y-8",children:[S.jsx("h1",{className:"text-3xl font-bold text-center mb-8",children:"API Settings"}),S.jsxs(Uc,{className:"p-6 space-y-6",children:[S.jsxs("div",{className:"space-y-4",children:[S.jsxs("div",{className:"space-y-2",children:[S.jsx(Tu,{htmlFor:"groq",children:"Groq API Key"}),S.jsx(Vs,{id:"groq",type:"password",value:t,onChange:s=>n(s.target.value),placeholder:"Enter your Groq API key"})]}),S.jsxs("div",{className:"space-y-2",children:[S.jsx(Tu,{htmlFor:"huggingface",children:"Hugging Face API Token"}),S.jsx(Vs,{id:"huggingface",type:"password",value:r,onChange:s=>o(s.target.value),placeholder:"Enter your Hugging Face API token"})]})]}),S.jsx($e,{className:"w-full",onClick:i,children:"Save API Keys"})]}),S.jsxs("div",{className:"text-center text-sm text-gray-500",children:[S.jsx("p",{children:"Your API keys are stored securely in your browser's local storage."}),S.jsx("p",{children:"They are never sent to our servers."})]})]})})};var da="focusScope.autoFocusOnMount",fa="focusScope.autoFocusOnUnmount",Hf={bubbles:!1,cancelable:!0},OC="FocusScope",og=m.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:i,...s}=e,[l,a]=m.useState(null),u=ct(o),f=ct(i),d=m.useRef(null),c=De(t,v=>a(v)),y=m.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;m.useEffect(()=>{if(r){let v=function(g){if(y.paused||!l)return;const E=g.target;l.contains(E)?d.current=E:an(d.current,{select:!0})},w=function(g){if(y.paused||!l)return;const E=g.relatedTarget;E!==null&&(l.contains(E)||an(d.current,{select:!0}))},h=function(g){if(document.activeElement===document.body)for(const C of g)C.removedNodes.length>0&&an(l)};document.addEventListener("focusin",v),document.addEventListener("focusout",w);const p=new MutationObserver(h);return l&&p.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",v),document.removeEventListener("focusout",w),p.disconnect()}}},[r,l,y.paused]),m.useEffect(()=>{if(l){Qf.add(y);const v=document.activeElement;if(!l.contains(v)){const h=new CustomEvent(da,Hf);l.addEventListener(da,u),l.dispatchEvent(h),h.defaultPrevented||(_C(IC(ig(l)),{select:!0}),document.activeElement===v&&an(l))}return()=>{l.removeEventListener(da,u),setTimeout(()=>{const h=new CustomEvent(fa,Hf);l.addEventListener(fa,f),l.dispatchEvent(h),h.defaultPrevented||an(v??document.body,{select:!0}),l.removeEventListener(fa,f),Qf.remove(y)},0)}}},[l,u,f,y]);const x=m.useCallback(v=>{if(!n&&!r||y.paused)return;const w=v.key==="Tab"&&!v.altKey&&!v.ctrlKey&&!v.metaKey,h=document.activeElement;if(w&&h){const p=v.currentTarget,[g,E]=AC(p);g&&E?!v.shiftKey&&h===E?(v.preventDefault(),n&&an(g,{select:!0})):v.shiftKey&&h===g&&(v.preventDefault(),n&&an(E,{select:!0})):h===p&&v.preventDefault()}},[n,r,y.paused]);return S.jsx(me.div,{tabIndex:-1,...s,ref:c,onKeyDown:x})});og.displayName=OC;function _C(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(an(r,{select:t}),document.activeElement!==n)return}function AC(e){const t=ig(e),n=Vf(t,e),r=Vf(t.reverse(),e);return[n,r]}function ig(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Vf(e,t){for(const n of e)if(!MC(n,{upTo:t}))return n}function MC(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function jC(e){return e instanceof HTMLInputElement&&"select"in e}function an(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&jC(e)&&t&&e.select()}}var Qf=LC();function LC(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=Kf(e,t),e.unshift(t)},remove(t){var n;e=Kf(e,t),(n=e[0])==null||n.resume()}}}function Kf(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function IC(e){return e.filter(t=>t.tagName!=="A")}var pa=0;function DC(){m.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Gf()),document.body.insertAdjacentElement("beforeend",e[1]??Gf()),pa++,()=>{pa===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),pa--}},[])}function Gf(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Mt=function(){return Mt=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},Mt.apply(this,arguments)};function sg(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function FC(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,i;r<o;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}var ps="right-scroll-bar-position",hs="width-before-scroll-bar",zC="with-scroll-bars-hidden",$C="--removed-body-scroll-bar-size";function ha(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function UC(e,t){var n=m.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var BC=typeof window<"u"?m.useLayoutEffect:m.useEffect,Yf=new WeakMap;function WC(e,t){var n=UC(null,function(r){return e.forEach(function(o){return ha(o,r)})});return BC(function(){var r=Yf.get(n);if(r){var o=new Set(r),i=new Set(e),s=n.current;o.forEach(function(l){i.has(l)||ha(l,null)}),i.forEach(function(l){o.has(l)||ha(l,s)})}Yf.set(n,e)},[e]),n}function HC(e){return e}function VC(e,t){t===void 0&&(t=HC);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(i){var s=t(i,r);return n.push(s),function(){n=n.filter(function(l){return l!==s})}},assignSyncMedium:function(i){for(r=!0;n.length;){var s=n;n=[],s.forEach(i)}n={push:function(l){return i(l)},filter:function(){return n}}},assignMedium:function(i){r=!0;var s=[];if(n.length){var l=n;n=[],l.forEach(i),s=n}var a=function(){var f=s;s=[],f.forEach(i)},u=function(){return Promise.resolve().then(a)};u(),n={push:function(f){s.push(f),u()},filter:function(f){return s=s.filter(f),n}}}};return o}function QC(e){e===void 0&&(e={});var t=VC(null);return t.options=Mt({async:!0,ssr:!1},e),t}var lg=function(e){var t=e.sideCar,n=sg(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return m.createElement(r,Mt({},n))};lg.isSideCarExport=!0;function KC(e,t){return e.useMedium(t),lg}var ag=QC(),ma=function(){},kl=m.forwardRef(function(e,t){var n=m.useRef(null),r=m.useState({onScrollCapture:ma,onWheelCapture:ma,onTouchMoveCapture:ma}),o=r[0],i=r[1],s=e.forwardProps,l=e.children,a=e.className,u=e.removeScrollBar,f=e.enabled,d=e.shards,c=e.sideCar,y=e.noIsolation,x=e.inert,v=e.allowPinchZoom,w=e.as,h=w===void 0?"div":w,p=e.gapMode,g=sg(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),E=c,C=WC([n,t]),P=Mt(Mt({},g),o);return m.createElement(m.Fragment,null,f&&m.createElement(E,{sideCar:ag,removeScrollBar:u,shards:d,noIsolation:y,inert:x,setCallbacks:i,allowPinchZoom:!!v,lockRef:n,gapMode:p}),s?m.cloneElement(m.Children.only(l),Mt(Mt({},P),{ref:C})):m.createElement(h,Mt({},P,{className:a,ref:C}),l))});kl.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};kl.classNames={fullWidth:hs,zeroRight:ps};var GC=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function YC(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=GC();return t&&e.setAttribute("nonce",t),e}function XC(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function qC(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var ZC=function(){var e=0,t=null;return{add:function(n){e==0&&(t=YC())&&(XC(t,n),qC(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},JC=function(){var e=ZC();return function(t,n){m.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},ug=function(){var e=JC(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},ek={left:0,top:0,right:0,gap:0},va=function(e){return parseInt(e||"",10)||0},tk=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[va(n),va(r),va(o)]},nk=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return ek;var t=tk(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},rk=ug(),Dr="data-scroll-locked",ok=function(e,t,n,r){var o=e.left,i=e.top,s=e.right,l=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(zC,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(l,"px ").concat(r,`;
  }
  body[`).concat(Dr,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(i,`px;
    padding-right: `).concat(s,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(l,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(ps,` {
    right: `).concat(l,"px ").concat(r,`;
  }
  
  .`).concat(hs,` {
    margin-right: `).concat(l,"px ").concat(r,`;
  }
  
  .`).concat(ps," .").concat(ps,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(hs," .").concat(hs,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Dr,`] {
    `).concat($C,": ").concat(l,`px;
  }
`)},Xf=function(){var e=parseInt(document.body.getAttribute(Dr)||"0",10);return isFinite(e)?e:0},ik=function(){m.useEffect(function(){return document.body.setAttribute(Dr,(Xf()+1).toString()),function(){var e=Xf()-1;e<=0?document.body.removeAttribute(Dr):document.body.setAttribute(Dr,e.toString())}},[])},sk=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;ik();var i=m.useMemo(function(){return nk(o)},[o]);return m.createElement(rk,{styles:ok(i,!t,o,n?"":"!important")})},Nu=!1;if(typeof window<"u")try{var Ki=Object.defineProperty({},"passive",{get:function(){return Nu=!0,!0}});window.addEventListener("test",Ki,Ki),window.removeEventListener("test",Ki,Ki)}catch{Nu=!1}var hr=Nu?{passive:!1}:!1,lk=function(e){return e.tagName==="TEXTAREA"},cg=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!lk(e)&&n[t]==="visible")},ak=function(e){return cg(e,"overflowY")},uk=function(e){return cg(e,"overflowX")},qf=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=dg(e,r);if(o){var i=fg(e,r),s=i[1],l=i[2];if(s>l)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},ck=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},dk=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},dg=function(e,t){return e==="v"?ak(t):uk(t)},fg=function(e,t){return e==="v"?ck(t):dk(t)},fk=function(e,t){return e==="h"&&t==="rtl"?-1:1},pk=function(e,t,n,r,o){var i=fk(e,window.getComputedStyle(t).direction),s=i*r,l=n.target,a=t.contains(l),u=!1,f=s>0,d=0,c=0;do{var y=fg(e,l),x=y[0],v=y[1],w=y[2],h=v-w-i*x;(x||h)&&dg(e,l)&&(d+=h,c+=x),l instanceof ShadowRoot?l=l.host:l=l.parentNode}while(!a&&l!==document.body||a&&(t.contains(l)||t===l));return(f&&(Math.abs(d)<1||!o)||!f&&(Math.abs(c)<1||!o))&&(u=!0),u},Gi=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Zf=function(e){return[e.deltaX,e.deltaY]},Jf=function(e){return e&&"current"in e?e.current:e},hk=function(e,t){return e[0]===t[0]&&e[1]===t[1]},mk=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},vk=0,mr=[];function gk(e){var t=m.useRef([]),n=m.useRef([0,0]),r=m.useRef(),o=m.useState(vk++)[0],i=m.useState(ug)[0],s=m.useRef(e);m.useEffect(function(){s.current=e},[e]),m.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var v=FC([e.lockRef.current],(e.shards||[]).map(Jf),!0).filter(Boolean);return v.forEach(function(w){return w.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),v.forEach(function(w){return w.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=m.useCallback(function(v,w){if("touches"in v&&v.touches.length===2||v.type==="wheel"&&v.ctrlKey)return!s.current.allowPinchZoom;var h=Gi(v),p=n.current,g="deltaX"in v?v.deltaX:p[0]-h[0],E="deltaY"in v?v.deltaY:p[1]-h[1],C,P=v.target,b=Math.abs(g)>Math.abs(E)?"h":"v";if("touches"in v&&b==="h"&&P.type==="range")return!1;var T=qf(b,P);if(!T)return!0;if(T?C=b:(C=b==="v"?"h":"v",T=qf(b,P)),!T)return!1;if(!r.current&&"changedTouches"in v&&(g||E)&&(r.current=C),!C)return!0;var D=r.current||C;return pk(D,w,v,D==="h"?g:E,!0)},[]),a=m.useCallback(function(v){var w=v;if(!(!mr.length||mr[mr.length-1]!==i)){var h="deltaY"in w?Zf(w):Gi(w),p=t.current.filter(function(C){return C.name===w.type&&(C.target===w.target||w.target===C.shadowParent)&&hk(C.delta,h)})[0];if(p&&p.should){w.cancelable&&w.preventDefault();return}if(!p){var g=(s.current.shards||[]).map(Jf).filter(Boolean).filter(function(C){return C.contains(w.target)}),E=g.length>0?l(w,g[0]):!s.current.noIsolation;E&&w.cancelable&&w.preventDefault()}}},[]),u=m.useCallback(function(v,w,h,p){var g={name:v,delta:w,target:h,should:p,shadowParent:yk(h)};t.current.push(g),setTimeout(function(){t.current=t.current.filter(function(E){return E!==g})},1)},[]),f=m.useCallback(function(v){n.current=Gi(v),r.current=void 0},[]),d=m.useCallback(function(v){u(v.type,Zf(v),v.target,l(v,e.lockRef.current))},[]),c=m.useCallback(function(v){u(v.type,Gi(v),v.target,l(v,e.lockRef.current))},[]);m.useEffect(function(){return mr.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:c}),document.addEventListener("wheel",a,hr),document.addEventListener("touchmove",a,hr),document.addEventListener("touchstart",f,hr),function(){mr=mr.filter(function(v){return v!==i}),document.removeEventListener("wheel",a,hr),document.removeEventListener("touchmove",a,hr),document.removeEventListener("touchstart",f,hr)}},[]);var y=e.removeScrollBar,x=e.inert;return m.createElement(m.Fragment,null,x?m.createElement(i,{styles:mk(o)}):null,y?m.createElement(sk,{gapMode:e.gapMode}):null)}function yk(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const wk=KC(ag,gk);var pg=m.forwardRef(function(e,t){return m.createElement(kl,Mt({},e,{ref:t,sideCar:wk}))});pg.classNames=kl.classNames;var xk=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},vr=new WeakMap,Yi=new WeakMap,Xi={},ga=0,hg=function(e){return e&&(e.host||hg(e.parentNode))},Sk=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=hg(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},Ek=function(e,t,n,r){var o=Sk(t,Array.isArray(e)?e:[e]);Xi[n]||(Xi[n]=new WeakMap);var i=Xi[n],s=[],l=new Set,a=new Set(o),u=function(d){!d||l.has(d)||(l.add(d),u(d.parentNode))};o.forEach(u);var f=function(d){!d||a.has(d)||Array.prototype.forEach.call(d.children,function(c){if(l.has(c))f(c);else try{var y=c.getAttribute(r),x=y!==null&&y!=="false",v=(vr.get(c)||0)+1,w=(i.get(c)||0)+1;vr.set(c,v),i.set(c,w),s.push(c),v===1&&x&&Yi.set(c,!0),w===1&&c.setAttribute(n,"true"),x||c.setAttribute(r,"true")}catch(h){console.error("aria-hidden: cannot operate on ",c,h)}})};return f(t),l.clear(),ga++,function(){s.forEach(function(d){var c=vr.get(d)-1,y=i.get(d)-1;vr.set(d,c),i.set(d,y),c||(Yi.has(d)||d.removeAttribute(r),Yi.delete(d)),y||d.removeAttribute(n)}),ga--,ga||(vr=new WeakMap,vr=new WeakMap,Yi=new WeakMap,Xi={})}},Ck=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=xk(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),Ek(r,o,n,"aria-hidden")):function(){return null}},Bc="Dialog",[mg,Qk]=Cc(Bc),[kk,kt]=mg(Bc),vg=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:i,modal:s=!0}=e,l=m.useRef(null),a=m.useRef(null),[u=!1,f]=Om({prop:r,defaultProp:o,onChange:i});return S.jsx(kk,{scope:t,triggerRef:l,contentRef:a,contentId:sa(),titleId:sa(),descriptionId:sa(),open:u,onOpenChange:f,onOpenToggle:m.useCallback(()=>f(d=>!d),[f]),modal:s,children:n})};vg.displayName=Bc;var gg="DialogTrigger",Pk=m.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=kt(gg,n),i=De(t,o.triggerRef);return S.jsx(me.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Vc(o.open),...r,ref:i,onClick:le(e.onClick,o.onOpenToggle)})});Pk.displayName=gg;var Wc="DialogPortal",[bk,yg]=mg(Wc,{forceMount:void 0}),wg=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,i=kt(Wc,t);return S.jsx(bk,{scope:t,forceMount:n,children:m.Children.map(r,s=>S.jsx(so,{present:n||i.open,children:S.jsx(kc,{asChild:!0,container:o,children:s})}))})};wg.displayName=Wc;var Ks="DialogOverlay",xg=m.forwardRef((e,t)=>{const n=yg(Ks,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=kt(Ks,e.__scopeDialog);return i.modal?S.jsx(so,{present:r||i.open,children:S.jsx(Tk,{...o,ref:t})}):null});xg.displayName=Ks;var Tk=m.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=kt(Ks,n);return S.jsx(pg,{as:eo,allowPinchZoom:!0,shards:[o.contentRef],children:S.jsx(me.div,{"data-state":Vc(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),ir="DialogContent",Sg=m.forwardRef((e,t)=>{const n=yg(ir,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=kt(ir,e.__scopeDialog);return S.jsx(so,{present:r||i.open,children:i.modal?S.jsx(Nk,{...o,ref:t}):S.jsx(Rk,{...o,ref:t})})});Sg.displayName=ir;var Nk=m.forwardRef((e,t)=>{const n=kt(ir,e.__scopeDialog),r=m.useRef(null),o=De(t,n.contentRef,r);return m.useEffect(()=>{const i=r.current;if(i)return Ck(i)},[]),S.jsx(Eg,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:le(e.onCloseAutoFocus,i=>{var s;i.preventDefault(),(s=n.triggerRef.current)==null||s.focus()}),onPointerDownOutside:le(e.onPointerDownOutside,i=>{const s=i.detail.originalEvent,l=s.button===0&&s.ctrlKey===!0;(s.button===2||l)&&i.preventDefault()}),onFocusOutside:le(e.onFocusOutside,i=>i.preventDefault())})}),Rk=m.forwardRef((e,t)=>{const n=kt(ir,e.__scopeDialog),r=m.useRef(!1),o=m.useRef(!1);return S.jsx(Eg,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:i=>{var s,l;(s=e.onCloseAutoFocus)==null||s.call(e,i),i.defaultPrevented||(r.current||(l=n.triggerRef.current)==null||l.focus(),i.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:i=>{var a,u;(a=e.onInteractOutside)==null||a.call(e,i),i.defaultPrevented||(r.current=!0,i.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const s=i.target;((u=n.triggerRef.current)==null?void 0:u.contains(s))&&i.preventDefault(),i.detail.originalEvent.type==="focusin"&&o.current&&i.preventDefault()}})}),Eg=m.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i,...s}=e,l=kt(ir,n),a=m.useRef(null),u=De(t,a);return DC(),S.jsxs(S.Fragment,{children:[S.jsx(og,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i,children:S.jsx(cl,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":Vc(l.open),...s,ref:u,onDismiss:()=>l.onOpenChange(!1)})}),S.jsxs(S.Fragment,{children:[S.jsx(Ok,{titleId:l.titleId}),S.jsx(Ak,{contentRef:a,descriptionId:l.descriptionId})]})]})}),Hc="DialogTitle",Cg=m.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=kt(Hc,n);return S.jsx(me.h2,{id:o.titleId,...r,ref:t})});Cg.displayName=Hc;var kg="DialogDescription",Pg=m.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=kt(kg,n);return S.jsx(me.p,{id:o.descriptionId,...r,ref:t})});Pg.displayName=kg;var bg="DialogClose",Tg=m.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=kt(bg,n);return S.jsx(me.button,{type:"button",...r,ref:t,onClick:le(e.onClick,()=>o.onOpenChange(!1))})});Tg.displayName=bg;function Vc(e){return e?"open":"closed"}var Ng="DialogTitleWarning",[Kk,Rg]=Tw(Ng,{contentName:ir,titleName:Hc,docsSlug:"dialog"}),Ok=({titleId:e})=>{const t=Rg(Ng),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return m.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},_k="DialogDescriptionWarning",Ak=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Rg(_k).contentName}}.`;return m.useEffect(()=>{var i;const o=(i=e.current)==null?void 0:i.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},Mk=vg,jk=wg,Og=xg,_g=Sg,Ag=Cg,Mg=Pg,Lk=Tg;const qi=Mk,Ik=jk,jg=m.forwardRef(({className:e,...t},n)=>S.jsx(Og,{ref:n,className:ve("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));jg.displayName=Og.displayName;const No=m.forwardRef(({className:e,children:t,...n},r)=>S.jsxs(Ik,{children:[S.jsx(jg,{}),S.jsxs(_g,{ref:r,className:ve("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...n,children:[t,S.jsxs(Lk,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[S.jsx(Jm,{className:"h-4 w-4"}),S.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));No.displayName=_g.displayName;const Ro=({className:e,...t})=>S.jsx("div",{className:ve("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});Ro.displayName="DialogHeader";const Oo=m.forwardRef(({className:e,...t},n)=>S.jsx(Ag,{ref:n,className:ve("text-lg font-semibold leading-none tracking-tight",e),...t}));Oo.displayName=Ag.displayName;const _o=m.forwardRef(({className:e,...t},n)=>S.jsx(Mg,{ref:n,className:ve("text-sm text-muted-foreground",e),...t}));_o.displayName=Mg.displayName;const ya="chat_messages",Dk=()=>{const e=Jv(),[t,n]=m.useState([]),[r,o]=m.useState(""),[i,s]=m.useState(!1),[l,a]=m.useState(!1),[u,f]=m.useState(null),[d,c]=m.useState(null),[y,x]=m.useState(null),[v,w]=m.useState(!1),[h,p]=m.useState(!1);m.useEffect(()=>{const R=localStorage.getItem(ya);if(R)try{n(JSON.parse(R))}catch(j){console.error("Error parsing saved messages:",j)}},[]);const g=()=>{n([]),localStorage.removeItem(ya),p(!1),pt.success("Riwayat chat telah dihapus")};m.useEffect(()=>{localStorage.setItem(ya,JSON.stringify(t))},[t]);const E=async R=>{if(R.preventDefault(),!r.trim())return;const j={role:"user",content:r};n(B=>[...B,j]),o(""),s(!0);const L=Qs(Sn.GROQ);if(!L){pt.error("API key tidak ditemukan. Silakan atur API key terlebih dahulu"),s(!1),e("/");return}try{const B=await fetch("https://api.groq.com/openai/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${L}`},body:JSON.stringify({model:"llama-3.2-90b-vision-preview",messages:[...t.map(({role:I,content:F})=>({role:I,content:F})),{role:j.role,content:j.content}],temperature:.7,max_tokens:2048})});if(!B.ok)throw new Error("Failed to get response from Groq API");const H=(await B.json()).choices[0].message.content,U=/```([a-zA-Z]*)\n([\s\S]*?)```/g;let W,k=0;const _=[];for(;(W=U.exec(H))!==null;){if(W.index>k){const I=H.slice(k,W.index).trim();I&&_.push({role:"assistant",content:I,type:"text"})}_.push({role:"assistant",content:W[2].trim(),type:"code",language:W[1]||"plaintext"}),k=W.index+W[0].length}if(k<H.length){const I=H.slice(k).trim();I&&_.push({role:"assistant",content:I,type:"text"})}n(I=>[...I,..._])}catch(B){console.error("Error:",B),pt.error("Gagal mengirim pesan ke Groq API")}finally{s(!1)}},C=async R=>{const j=Qs(Sn.HUGGINGFACE);if(!j){pt.error("API token tidak ditemukan. Silakan atur Hugging Face token terlebih dahulu"),e("/");return}a(!0);try{const L=await fetch("https://api-inference.huggingface.co/models/stabilityai/stable-diffusion-3.5-large",{method:"POST",headers:{Authorization:`Bearer ${j}`,"Content-Type":"application/json"},body:JSON.stringify({inputs:R,parameters:{width:1024,height:576,negative_prompt:"lowres, bad anatomy, bad hands, cropped, worst quality",num_inference_steps:30,guidance_scale:7.5}})});if(!L.ok)throw new Error(`HTTP error! status: ${L.status}`);const B=await L.blob(),M=URL.createObjectURL(B);n(H=>[...H,{role:"assistant",content:"Here's your generated image:",type:"image",imageUrl:M}]),pt.success("Gambar berhasil dibuat")}catch(L){console.error("Error:",L),pt.error("Gagal menghasilkan gambar. Mohon coba lagi.")}finally{a(!1)}},P=()=>{if(!r.trim()){pt.error("Mohon masukkan deskripsi gambar");return}C(r),o("")},b=async R=>{try{await navigator.clipboard.writeText(R),pt.success("Kode berhasil disalin")}catch{pt.error("Gagal menyalin kode")}},T=R=>{const j=document.createElement("a");j.href=R,j.download="generated-image.png",document.body.appendChild(j),j.click(),document.body.removeChild(j)},D=()=>{d!==null&&(n(R=>R.map(j=>j.type==="code"&&j.content===y?{...j,content:d}:j)),c(null),x(d),pt.success("Kode berhasil disimpan"))};return S.jsx("div",{className:"min-h-screen bg-gray-50 p-4",children:S.jsxs("div",{className:"max-w-4xl mx-auto",children:[S.jsxs(Uc,{className:"h-[80vh] flex flex-col",children:[S.jsxs("div",{className:"p-4 border-b",children:[S.jsx("h1",{className:"text-2xl font-bold",children:"KIKAZE - AI"}),S.jsx($e,{variant:"outline",color:"danger",onClick:()=>p(!0),children:"Hapus History"})]}),S.jsx(qi,{open:h,onOpenChange:p,children:S.jsxs(No,{children:[S.jsxs(Ro,{children:[S.jsx(Oo,{children:"Konfirmasi Hapus"}),S.jsx(_o,{children:"Apakah Anda yakin ingin menghapus seluruh riwayat chat?"})]}),S.jsxs("div",{className:"flex justify-end gap-2",children:[S.jsx($e,{variant:"outline",onClick:()=>p(!1),children:"Batal"}),S.jsx($e,{color:"danger",onClick:g,children:"Hapus"})]})]})}),S.jsx("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:t.map((R,j)=>S.jsx("div",{className:`flex ${R.role==="user"?"justify-end":"justify-start"}`,children:S.jsx("div",{className:`max-w-[80%] rounded-lg p-3 ${R.role==="user"?"bg-blue-500 text-white":R.type==="code"?"bg-gray-800 text-white font-mono":"bg-gray-200 text-gray-800"}`,children:R.type==="code"?S.jsxs("div",{className:"relative",children:[S.jsx("pre",{className:"overflow-x-auto p-2",children:S.jsx("code",{children:R.content})}),S.jsxs("div",{className:"absolute top-2 right-2 space-x-2",children:[S.jsx($e,{variant:"ghost",size:"sm",onClick:()=>{x(R.content),w(!0)},children:S.jsx(hf,{className:"w-4 h-4"})}),S.jsx($e,{variant:"ghost",size:"sm",onClick:()=>b(R.content),children:S.jsx(p1,{className:"w-4 h-4"})}),S.jsx($e,{variant:"ghost",size:"sm",onClick:()=>{c(R.content),x(R.content)},children:S.jsx(g1,{className:"w-4 h-4"})})]})]}):R.type==="image"?S.jsxs("div",{className:"space-y-2",children:[R.content,R.imageUrl&&S.jsxs("div",{className:"relative group",children:[S.jsx("img",{src:R.imageUrl,alt:"Generated",className:"rounded-lg max-w-full cursor-pointer hover:opacity-90 transition-opacity",onClick:()=>f(R.imageUrl)}),S.jsxs("div",{className:"absolute top-2 right-2 space-x-2 opacity-0 group-hover:opacity-100 transition-opacity",children:[S.jsx($e,{variant:"secondary",size:"sm",onClick:()=>f(R.imageUrl),children:S.jsx(hf,{className:"w-4 h-4"})}),S.jsx($e,{variant:"secondary",size:"sm",onClick:()=>R.imageUrl&&T(R.imageUrl),children:S.jsx(h1,{className:"w-4 h-4"})})]})]})]}):R.content})},j))}),S.jsx("form",{onSubmit:E,className:"p-4 border-t",children:S.jsxs("div",{className:"flex gap-2",children:[S.jsx(Vs,{value:r,onChange:R=>o(R.target.value),placeholder:"Ketik pesan Anda...",disabled:i||l}),S.jsx($e,{type:"submit",disabled:i||l,onClick:R=>{R.preventDefault(),E(R)},children:i?"Memproses...":"Kirim"}),S.jsxs($e,{type:"button",variant:"secondary",disabled:i||l,onClick:P,children:[S.jsx(m1,{className:"w-4 h-4 mr-2"}),l?"Membuat...":"Buat Gambar"]})]})})]}),S.jsx(qi,{open:v,onOpenChange:w,children:S.jsxs(No,{className:"max-w-[90vw] max-h-[90vh]",children:[S.jsxs(Ro,{children:[S.jsx(Oo,{children:"Preview Kode"}),S.jsx(_o,{children:"Preview dari kode yang dipilih"})]}),S.jsx("div",{className:"bg-white rounded-lg overflow-hidden",children:y&&S.jsx("iframe",{srcDoc:`
                    <!DOCTYPE html>
                    <html>
                      <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <script src="https://cdn.tailwindcss.com"><\/script>
                      </head>
                      <body>
                        ${y}
                      </body>
                    </html>
                  `,className:"w-full h-[60vh] bg-white rounded-lg",title:"Code Preview"})})]})}),S.jsx(qi,{open:!!u,onOpenChange:()=>f(null),children:S.jsxs(No,{className:"max-w-[90vw] max-h-[90vh]",children:[S.jsxs(Ro,{children:[S.jsx(Oo,{children:"Preview Gambar"}),S.jsx(_o,{children:"Preview dari gambar yang dipilih"})]}),u&&S.jsx("img",{src:u,alt:"Preview",className:"w-full h-full object-contain"})]})}),S.jsx(qi,{open:!!d,onOpenChange:()=>c(null),children:S.jsxs(No,{children:[S.jsxs(Ro,{children:[S.jsx(Oo,{children:"Edit Kode"}),S.jsx(_o,{children:"Edit kode yang dipilih"})]}),S.jsxs("div",{className:"flex flex-col gap-4",children:[S.jsx("textarea",{value:d||"",onChange:R=>c(R.target.value),className:"w-full h-[400px] font-mono p-4 bg-gray-800 text-white rounded-lg"}),S.jsxs("div",{className:"flex justify-end gap-2",children:[S.jsx($e,{variant:"outline",onClick:()=>c(null),children:"Batal"}),S.jsxs($e,{onClick:D,children:[S.jsx(v1,{className:"w-4 h-4 mr-2"}),"Simpan"]})]})]})]})})]})})},Fk=new bE,zk=()=>S.jsx(NE,{client:Fk,children:S.jsxs(rE,{children:[S.jsx(J1,{}),S.jsx(Nx,{}),S.jsx(xC,{children:S.jsxs(gC,{children:[S.jsx(Pu,{path:"/",element:S.jsx(RC,{})}),S.jsx(Pu,{path:"/chat",element:S.jsx(Dk,{})})]})})]})});Cm(document.getElementById("root")).render(S.jsx(zk,{}));

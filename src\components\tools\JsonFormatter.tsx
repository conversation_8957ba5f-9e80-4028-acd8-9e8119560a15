import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { Copy, Download, Upload } from 'lucide-react';

export const JsonFormatter: React.FC = () => {
  const [input, setInput] = useState('');
  const [output, setOutput] = useState('');
  const [isValid, setIsValid] = useState<boolean | null>(null);

  const formatJson = () => {
    try {
      const parsed = JSON.parse(input);
      const formatted = JSON.stringify(parsed, null, 2);
      setOutput(formatted);
      setIsValid(true);
      toast.success('JSON berhasil diformat!');
    } catch (error) {
      setIsValid(false);
      setOutput(`Error: ${error instanceof Error ? error.message : 'Invalid JSON'}`);
      toast.error('JSON tidak valid!');
    }
  };

  const minifyJson = () => {
    try {
      const parsed = JSON.parse(input);
      const minified = JSON.stringify(parsed);
      setOutput(minified);
      setIsValid(true);
      toast.success('JSON berhasil diminify!');
    } catch (error) {
      setIsValid(false);
      setOutput(`Error: ${error instanceof Error ? error.message : 'Invalid JSON'}`);
      toast.error('JSON tidak valid!');
    }
  };

  const validateJson = () => {
    try {
      JSON.parse(input);
      setIsValid(true);
      toast.success('JSON valid!');
    } catch (error) {
      setIsValid(false);
      toast.error(`JSON tidak valid: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(output);
      toast.success('Berhasil disalin ke clipboard!');
    } catch (error) {
      toast.error('Gagal menyalin ke clipboard');
    }
  };

  const downloadJson = () => {
    const blob = new Blob([output], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'formatted.json';
    a.click();
    URL.revokeObjectURL(url);
    toast.success('File JSON berhasil didownload!');
  };

  const loadSample = () => {
    const sample = {
      "name": "John Doe",
      "age": 30,
      "city": "Jakarta",
      "hobbies": ["reading", "coding", "gaming"],
      "address": {
        "street": "Jl. Sudirman No. 123",
        "zipCode": "12345"
      },
      "isActive": true
    };
    setInput(JSON.stringify(sample));
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            📝 JSON Formatter & Validator
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Input Section */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium">Input JSON:</label>
              <Button variant="outline" size="sm" onClick={loadSample}>
                📄 Load Sample
              </Button>
            </div>
            <Textarea
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="Paste your JSON here..."
              className="min-h-[200px] font-mono text-sm"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 flex-wrap">
            <Button onClick={formatJson} className="flex items-center gap-2">
              ✨ Format
            </Button>
            <Button onClick={minifyJson} variant="outline" className="flex items-center gap-2">
              🗜️ Minify
            </Button>
            <Button onClick={validateJson} variant="outline" className="flex items-center gap-2">
              ✅ Validate
            </Button>
          </div>

          {/* Validation Status */}
          {isValid !== null && (
            <div className={`p-3 rounded-lg ${isValid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
              {isValid ? '✅ JSON Valid' : '❌ JSON Invalid'}
            </div>
          )}

          {/* Output Section */}
          {output && (
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <label className="text-sm font-medium">Output:</label>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={copyToClipboard}>
                    <Copy className="w-4 h-4 mr-1" />
                    Copy
                  </Button>
                  <Button variant="outline" size="sm" onClick={downloadJson}>
                    <Download className="w-4 h-4 mr-1" />
                    Download
                  </Button>
                </div>
              </div>
              <Textarea
                value={output}
                readOnly
                className="min-h-[200px] font-mono text-sm bg-gray-50"
              />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

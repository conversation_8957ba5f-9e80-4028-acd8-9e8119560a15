// KIKAZE-AI Browser Extension Background Script

// Context menu items
const contextMenuItems = [
  {
    id: 'kikaze-explain',
    title: '🤖 Explain with KIKAZE-AI',
    contexts: ['selection']
  },
  {
    id: 'kikaze-translate',
    title: '🌐 Translate with KIKAZE-AI',
    contexts: ['selection']
  },
  {
    id: 'kikaze-summarize',
    title: '📄 Summarize Page',
    contexts: ['page']
  },
  {
    id: 'kikaze-extract-data',
    title: '📊 Extract Data',
    contexts: ['page']
  },
  {
    id: 'kikaze-generate-code',
    title: '💻 Generate Code',
    contexts: ['selection']
  },
  {
    id: 'kikaze-check-grammar',
    title: '✏️ Check Grammar',
    contexts: ['selection']
  }
];

// Install context menus
chrome.runtime.onInstalled.addListener(() => {
  contextMenuItems.forEach(item => {
    chrome.contextMenus.create(item);
  });
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener(async (info, tab) => {
  const action = info.menuItemId;
  const selectedText = info.selectionText;
  
  try {
    // Inject content script if needed
    await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      files: ['content.js']
    });

    // Send message to content script
    chrome.tabs.sendMessage(tab.id, {
      action: action,
      text: selectedText,
      pageUrl: info.pageUrl,
      pageTitle: tab.title
    });
  } catch (error) {
    console.error('Error executing context menu action:', error);
  }
});

// Handle messages from content script and popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'openKikazeApp') {
    chrome.tabs.create({
      url: 'http://localhost:5173', // Development URL
      active: true
    });
  }
  
  if (request.action === 'processWithAI') {
    processWithAI(request.data)
      .then(result => sendResponse({ success: true, result }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true; // Keep message channel open for async response
  }
});

// AI Processing Function
async function processWithAI(data) {
  const { action, text, context } = data;
  
  const prompts = {
    'kikaze-explain': `Explain this text in simple terms: "${text}"`,
    'kikaze-translate': `Translate this text to Indonesian: "${text}"`,
    'kikaze-summarize': `Summarize this webpage content: "${text}"`,
    'kikaze-extract-data': `Extract structured data from this content: "${text}"`,
    'kikaze-generate-code': `Generate code based on this description: "${text}"`,
    'kikaze-check-grammar': `Check grammar and suggest improvements: "${text}"`
  };

  try {
    const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_GROQ_API_KEY' // This should be stored securely
      },
      body: JSON.stringify({
        model: 'llama-3.1-8b-instant',
        messages: [
          {
            role: 'system',
            content: 'You are KIKAZE-AI, a helpful Indonesian AI assistant. Respond in Indonesian unless asked otherwise.'
          },
          {
            role: 'user',
            content: prompts[action] || text
          }
        ],
        max_tokens: 1024,
        temperature: 0.7
      })
    });

    const result = await response.json();
    return result.choices[0].message.content;
  } catch (error) {
    throw new Error('Failed to process with AI: ' + error.message);
  }
}

// Keyboard shortcuts
chrome.commands.onCommand.addListener((command) => {
  if (command === 'open-kikaze') {
    chrome.tabs.create({
      url: 'http://localhost:5173'
    });
  }
});

// Badge management
function updateBadge(text, color = '#4285f4') {
  chrome.action.setBadgeText({ text });
  chrome.action.setBadgeBackgroundColor({ color });
}

// Update badge on extension load
chrome.runtime.onStartup.addListener(() => {
  updateBadge('AI');
});

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { saveApiKey, getApiKey, API_KEYS } from "@/utils/apiKeys";

const Index = () => {
  const navigate = useNavigate();
  const [groqKey, setGroqKey] = useState("");
  const [huggingfaceToken, setHuggingfaceToken] = useState("");

  // Check if API keys are already saved
  useEffect(() => {
    const savedGroqKey = getApiKey(API_KEYS.GROQ);
    const savedHuggingfaceToken = getApiKey(API_KEYS.HUGGINGFACE);

    if (savedGroqKey && savedHuggingfaceToken) {
      navigate("/chat"); // Redirect to chat if keys exist
    } else {
      setGroqKey(savedGroqKey || "");
      setHuggingfaceToken(savedHuggingfaceToken || "");
    }
  }, [navigate]);

  const handleSaveKeys = () => {
    saveApiKey(API_KEYS.GROQ, groqKey);
    saveApiKey(API_KEYS.HUGGINGFACE, huggingfaceToken);
    toast.success("Kunci API berhasil disimpan!");
    navigate("/chat");
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-2xl mx-auto space-y-8">
        <h1 className="text-3xl font-bold text-center mb-8">API Settings</h1>

        <Card className="p-6 space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="groq">Kunci API Groq</Label>
              <Input
                id="groq"
                type="password"
                value={groqKey}
                onChange={(e) => setGroqKey(e.target.value)}
                placeholder="Masukkan kunci API Groq Anda"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="huggingface">Token API Hugging Face</Label>
              <Input
                id="huggingface"
                type="password"
                value={huggingfaceToken}
                onChange={(e) => setHuggingfaceToken(e.target.value)}
                placeholder="Masukkan token API Hugging Face Anda"
              />
            </div>
          </div>
          <Button className="w-full" onClick={handleSaveKeys}>
            Simpan Kunci API
          </Button>
        </Card>
        <div className="text-center text-sm text-gray-500">
          <p>Kunci API Anda disimpan dengan aman di penyimpanan lokal browser.</p>
          <p>They are never sent to our servers.</p>
        </div>
      </div>
    </div>
  );
};

export default Index;
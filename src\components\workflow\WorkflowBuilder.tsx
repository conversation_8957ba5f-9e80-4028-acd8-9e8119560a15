import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { Play, Plus, Trash2, Save, Download, Upload, Zap, ArrowRight } from 'lucide-react';

interface WorkflowStep {
  id: string;
  type: 'input' | 'process' | 'output';
  tool: string;
  config: any;
  position: { x: number; y: number };
}

interface Workflow {
  id: string;
  name: string;
  description: string;
  steps: WorkflowStep[];
  connections: { from: string; to: string }[];
  created: Date;
  lastRun?: Date;
}

export const WorkflowBuilder: React.FC = () => {
  const [workflows, setWorkflows] = useState<Workflow[]>([]);
  const [currentWorkflow, setCurrentWorkflow] = useState<Workflow | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [selectedTool, setSelectedTool] = useState('');

  const availableTools = [
    { id: 'text-input', name: 'Text Input', type: 'input', icon: '📝' },
    { id: 'file-input', name: 'File Input', type: 'input', icon: '📁' },
    { id: 'url-input', name: 'URL Input', type: 'input', icon: '🌐' },
    
    { id: 'json-formatter', name: 'JSON Formatter', type: 'process', icon: '🔧' },
    { id: 'base64-encoder', name: 'Base64 Encoder', type: 'process', icon: '🔐' },
    { id: 'hash-generator', name: 'Hash Generator', type: 'process', icon: '#️⃣' },
    { id: 'qr-generator', name: 'QR Generator', type: 'process', icon: '📱' },
    { id: 'image-compressor', name: 'Image Compressor', type: 'process', icon: '🗜️' },
    { id: 'format-converter', name: 'Format Converter', type: 'process', icon: '🔄' },
    { id: 'text-to-speech', name: 'Text-to-Speech', type: 'process', icon: '🔊' },
    { id: 'mood-detector', name: 'Mood Detector', type: 'process', icon: '🧠' },
    { id: 'face-detection', name: 'Face Detection', type: 'process', icon: '👤' },
    { id: 'object-detection', name: 'Object Detection', type: 'process', icon: '🎯' },
    { id: 'background-removal', name: 'Background Removal', type: 'process', icon: '✂️' },
    
    { id: 'download-file', name: 'Download File', type: 'output', icon: '💾' },
    { id: 'copy-clipboard', name: 'Copy to Clipboard', type: 'output', icon: '📋' },
    { id: 'send-email', name: 'Send Email', type: 'output', icon: '📧' },
    { id: 'save-cloud', name: 'Save to Cloud', type: 'output', icon: '☁️' }
  ];

  const workflowTemplates = [
    {
      name: 'Image Processing Pipeline',
      description: 'Compress images, convert format, and remove background',
      steps: ['file-input', 'image-compressor', 'format-converter', 'background-removal', 'download-file']
    },
    {
      name: 'Document Analysis',
      description: 'Extract text, analyze mood, and generate summary',
      steps: ['file-input', 'text-extraction', 'mood-detector', 'ai-summary', 'copy-clipboard']
    },
    {
      name: 'QR Code Batch Generator',
      description: 'Generate QR codes from text list and download as ZIP',
      steps: ['text-input', 'qr-generator', 'batch-processor', 'zip-creator', 'download-file']
    },
    {
      name: 'Social Media Content',
      description: 'Create content, generate QR code, and optimize images',
      steps: ['text-input', 'ai-content-generator', 'qr-generator', 'image-compressor', 'download-file']
    }
  ];

  const createNewWorkflow = () => {
    const newWorkflow: Workflow = {
      id: Date.now().toString(),
      name: 'New Workflow',
      description: 'Describe your workflow here',
      steps: [],
      connections: [],
      created: new Date()
    };
    setCurrentWorkflow(newWorkflow);
  };

  const addStep = (toolId: string) => {
    if (!currentWorkflow) return;

    const tool = availableTools.find(t => t.id === toolId);
    if (!tool) return;

    const newStep: WorkflowStep = {
      id: Date.now().toString(),
      type: tool.type as any,
      tool: toolId,
      config: {},
      position: { x: 100 + currentWorkflow.steps.length * 200, y: 100 }
    };

    setCurrentWorkflow({
      ...currentWorkflow,
      steps: [...currentWorkflow.steps, newStep]
    });

    toast.success(`Added ${tool.name} to workflow`);
  };

  const removeStep = (stepId: string) => {
    if (!currentWorkflow) return;

    setCurrentWorkflow({
      ...currentWorkflow,
      steps: currentWorkflow.steps.filter(s => s.id !== stepId),
      connections: currentWorkflow.connections.filter(c => c.from !== stepId && c.to !== stepId)
    });
  };

  const saveWorkflow = () => {
    if (!currentWorkflow) return;

    const updatedWorkflows = workflows.filter(w => w.id !== currentWorkflow.id);
    setWorkflows([...updatedWorkflows, currentWorkflow]);
    toast.success('Workflow saved successfully!');
  };

  const runWorkflow = async () => {
    if (!currentWorkflow || currentWorkflow.steps.length === 0) {
      toast.error('No workflow to run!');
      return;
    }

    setIsRunning(true);
    toast.info('Running workflow...');

    try {
      // Simulate workflow execution
      for (let i = 0; i < currentWorkflow.steps.length; i++) {
        const step = currentWorkflow.steps[i];
        const tool = availableTools.find(t => t.id === step.tool);
        
        await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate processing time
        toast.info(`Processing: ${tool?.name}`);
      }

      setCurrentWorkflow({
        ...currentWorkflow,
        lastRun: new Date()
      });

      toast.success('Workflow completed successfully!');
    } catch (error) {
      toast.error('Workflow execution failed');
    } finally {
      setIsRunning(false);
    }
  };

  const exportWorkflow = () => {
    if (!currentWorkflow) return;

    const dataStr = JSON.stringify(currentWorkflow, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${currentWorkflow.name.replace(/\s+/g, '-')}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
    toast.success('Workflow exported!');
  };

  const importWorkflow = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const workflow = JSON.parse(e.target?.result as string);
        setCurrentWorkflow(workflow);
        toast.success('Workflow imported successfully!');
      } catch (error) {
        toast.error('Invalid workflow file');
      }
    };
    reader.readAsText(file);
  };

  const loadTemplate = (template: typeof workflowTemplates[0]) => {
    const newWorkflow: Workflow = {
      id: Date.now().toString(),
      name: template.name,
      description: template.description,
      steps: template.steps.map((toolId, index) => ({
        id: `${Date.now()}-${index}`,
        type: availableTools.find(t => t.id === toolId)?.type as any || 'process',
        tool: toolId,
        config: {},
        position: { x: 100 + index * 200, y: 100 }
      })),
      connections: template.steps.slice(0, -1).map((_, index) => ({
        from: `${Date.now()}-${index}`,
        to: `${Date.now()}-${index + 1}`
      })),
      created: new Date()
    };

    setCurrentWorkflow(newWorkflow);
    toast.success(`Template "${template.name}" loaded!`);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            ⚙️ Workflow Automation Builder
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Workflow Controls */}
          <div className="flex gap-2 flex-wrap">
            <Button onClick={createNewWorkflow}>
              <Plus className="w-4 h-4 mr-2" />
              New Workflow
            </Button>
            
            {currentWorkflow && (
              <>
                <Button onClick={runWorkflow} disabled={isRunning} variant="default">
                  <Play className="w-4 h-4 mr-2" />
                  {isRunning ? 'Running...' : 'Run Workflow'}
                </Button>
                
                <Button onClick={saveWorkflow} variant="outline">
                  <Save className="w-4 h-4 mr-2" />
                  Save
                </Button>
                
                <Button onClick={exportWorkflow} variant="outline">
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
                
                <label className="cursor-pointer">
                  <Button variant="outline" asChild>
                    <span>
                      <Upload className="w-4 h-4 mr-2" />
                      Import
                    </span>
                  </Button>
                  <input
                    type="file"
                    accept=".json"
                    onChange={importWorkflow}
                    className="hidden"
                  />
                </label>
              </>
            )}
          </div>

          {/* Current Workflow Info */}
          {currentWorkflow && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Workflow Name:</label>
                <Input
                  value={currentWorkflow.name}
                  onChange={(e) => setCurrentWorkflow({
                    ...currentWorkflow,
                    name: e.target.value
                  })}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Description:</label>
                <Input
                  value={currentWorkflow.description}
                  onChange={(e) => setCurrentWorkflow({
                    ...currentWorkflow,
                    description: e.target.value
                  })}
                />
              </div>
            </div>
          )}

          {/* Tool Selector */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Add Tool to Workflow:</label>
            <div className="flex gap-2">
              <Select value={selectedTool} onValueChange={setSelectedTool}>
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="Select a tool..." />
                </SelectTrigger>
                <SelectContent>
                  {availableTools.map((tool) => (
                    <SelectItem key={tool.id} value={tool.id}>
                      <div className="flex items-center gap-2">
                        <span>{tool.icon}</span>
                        <span>{tool.name}</span>
                        <span className="text-xs text-gray-500">({tool.type})</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button 
                onClick={() => {
                  if (selectedTool) {
                    addStep(selectedTool);
                    setSelectedTool('');
                  }
                }}
                disabled={!selectedTool || !currentWorkflow}
              >
                <Plus className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Workflow Visualization */}
          {currentWorkflow && currentWorkflow.steps.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Workflow Steps:</h3>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 flex-wrap">
                  {currentWorkflow.steps.map((step, index) => {
                    const tool = availableTools.find(t => t.id === step.tool);
                    return (
                      <React.Fragment key={step.id}>
                        <div className="flex items-center gap-2 bg-white p-3 rounded-lg border shadow-sm">
                          <span className="text-xl">{tool?.icon}</span>
                          <div>
                            <div className="font-medium text-sm">{tool?.name}</div>
                            <div className="text-xs text-gray-500">{tool?.type}</div>
                          </div>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => removeStep(step.id)}
                            className="ml-2"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                        {index < currentWorkflow.steps.length - 1 && (
                          <ArrowRight className="w-4 h-4 text-gray-400" />
                        )}
                      </React.Fragment>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {/* Workflow Templates */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Quick Start Templates:</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {workflowTemplates.map((template, index) => (
                <Card key={index} className="cursor-pointer hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-medium">{template.name}</h4>
                      <Button
                        size="sm"
                        onClick={() => loadTemplate(template)}
                      >
                        <Zap className="w-3 h-3 mr-1" />
                        Use
                      </Button>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{template.description}</p>
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      {template.steps.slice(0, 3).map((stepId, i) => {
                        const tool = availableTools.find(t => t.id === stepId);
                        return (
                          <React.Fragment key={stepId}>
                            <span>{tool?.icon}</span>
                            {i < 2 && <ArrowRight className="w-3 h-3" />}
                          </React.Fragment>
                        );
                      })}
                      {template.steps.length > 3 && <span>...</span>}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Saved Workflows */}
          {workflows.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Saved Workflows:</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {workflows.map((workflow) => (
                  <Card key={workflow.id} className="cursor-pointer hover:shadow-md transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium">{workflow.name}</h4>
                        <Button
                          size="sm"
                          onClick={() => setCurrentWorkflow(workflow)}
                        >
                          Load
                        </Button>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{workflow.description}</p>
                      <div className="text-xs text-gray-500">
                        <div>Steps: {workflow.steps.length}</div>
                        <div>Created: {workflow.created.toLocaleDateString()}</div>
                        {workflow.lastRun && (
                          <div>Last run: {workflow.lastRun.toLocaleDateString()}</div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Info */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">ℹ️ Workflow Automation Features:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• <strong>Drag & Drop Builder</strong>: Visual workflow creation</li>
              <li>• <strong>Pre-built Templates</strong>: Quick start with common workflows</li>
              <li>• <strong>Tool Integration</strong>: Connect all KIKAZE-AI tools</li>
              <li>• <strong>Batch Processing</strong>: Process multiple files at once</li>
              <li>• <strong>Export/Import</strong>: Share workflows with team</li>
              <li>• <strong>Automation</strong>: Run workflows with single click</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

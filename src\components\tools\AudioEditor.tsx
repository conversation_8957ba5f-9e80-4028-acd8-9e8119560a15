import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { Edit, Upload, Play, Pause, Square, Download, Scissors, Volume2, Zap, Merge, RotateCcw, Save } from 'lucide-react';

interface AudioProject {
  id: string;
  name: string;
  audioBuffer: AudioBuffer | null;
  originalFile: File;
  duration: number;
  sampleRate: number;
  channels: number;
}

interface EditOperation {
  type: 'trim' | 'volume' | 'fade' | 'speed' | 'pitch' | 'noise_reduction';
  parameters: any;
  timestamp: Date;
}

export const AudioEditor: React.FC = () => {
  const [currentProject, setCurrentProject] = useState<AudioProject | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState([1]);
  const [playbackRate, setPlaybackRate] = useState([1]);
  const [trimStart, setTrimStart] = useState([0]);
  const [trimEnd, setTrimEnd] = useState([100]);
  const [fadeInDuration, setFadeInDuration] = useState([0]);
  const [fadeOutDuration, setFadeOutDuration] = useState([0]);
  const [editHistory, setEditHistory] = useState<EditOperation[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const sourceNodeRef = useRef<AudioBufferSourceNode | null>(null);
  const gainNodeRef = useRef<GainNode | null>(null);

  useEffect(() => {
    return () => {
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, []);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('audio/')) {
      toast.error('Please upload an audio file');
      return;
    }

    try {
      setIsProcessing(true);
      
      // Create audio context
      if (!audioContextRef.current) {
        audioContextRef.current = new AudioContext();
      }

      // Load and decode audio file
      const arrayBuffer = await file.arrayBuffer();
      const audioBuffer = await audioContextRef.current.decodeAudioData(arrayBuffer);

      const project: AudioProject = {
        id: Date.now().toString(),
        name: file.name.replace(/\.[^/.]+$/, ''),
        audioBuffer: audioBuffer,
        originalFile: file,
        duration: audioBuffer.duration,
        sampleRate: audioBuffer.sampleRate,
        channels: audioBuffer.numberOfChannels
      };

      setCurrentProject(project);
      setDuration(audioBuffer.duration);
      setTrimEnd([audioBuffer.duration]);
      
      // Draw waveform
      drawWaveform(audioBuffer);
      
      toast.success(`Audio loaded: ${file.name}`);
    } catch (error) {
      console.error('Error loading audio:', error);
      toast.error('Failed to load audio file');
    } finally {
      setIsProcessing(false);
    }
  };

  const drawWaveform = (audioBuffer: AudioBuffer) => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const width = canvas.width;
    const height = canvas.height;
    
    ctx.clearRect(0, 0, width, height);
    
    // Get audio data
    const channelData = audioBuffer.getChannelData(0);
    const samplesPerPixel = Math.floor(channelData.length / width);
    
    ctx.fillStyle = '#3b82f6';
    ctx.strokeStyle = '#1d4ed8';
    ctx.lineWidth = 1;
    
    // Draw waveform
    for (let x = 0; x < width; x++) {
      const startSample = x * samplesPerPixel;
      const endSample = startSample + samplesPerPixel;
      
      let min = 1;
      let max = -1;
      
      for (let i = startSample; i < endSample && i < channelData.length; i++) {
        const sample = channelData[i];
        if (sample < min) min = sample;
        if (sample > max) max = sample;
      }
      
      const yMin = (1 + min) * height / 2;
      const yMax = (1 + max) * height / 2;
      
      ctx.fillRect(x, yMax, 1, yMin - yMax);
    }
    
    // Draw trim markers
    const trimStartX = (trimStart[0] / 100) * width;
    const trimEndX = (trimEnd[0] / 100) * width;
    
    ctx.fillStyle = 'rgba(255, 0, 0, 0.3)';
    ctx.fillRect(0, 0, trimStartX, height);
    ctx.fillRect(trimEndX, 0, width - trimEndX, height);
    
    ctx.strokeStyle = '#ef4444';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(trimStartX, 0);
    ctx.lineTo(trimStartX, height);
    ctx.moveTo(trimEndX, 0);
    ctx.lineTo(trimEndX, height);
    ctx.stroke();
  };

  const playAudio = async () => {
    if (!currentProject?.audioBuffer || !audioContextRef.current) return;

    try {
      // Stop current playback
      if (sourceNodeRef.current) {
        sourceNodeRef.current.stop();
      }

      // Create new source
      sourceNodeRef.current = audioContextRef.current.createBufferSource();
      sourceNodeRef.current.buffer = currentProject.audioBuffer;
      
      // Create gain node for volume control
      gainNodeRef.current = audioContextRef.current.createGain();
      gainNodeRef.current.gain.value = volume[0];
      
      // Set playback rate
      sourceNodeRef.current.playbackRate.value = playbackRate[0];
      
      // Connect nodes
      sourceNodeRef.current.connect(gainNodeRef.current);
      gainNodeRef.current.connect(audioContextRef.current.destination);
      
      // Calculate trim positions
      const startTime = (trimStart[0] / 100) * currentProject.duration;
      const endTime = (trimEnd[0] / 100) * currentProject.duration;
      const playDuration = endTime - startTime;
      
      sourceNodeRef.current.start(0, startTime, playDuration);
      setIsPlaying(true);
      
      sourceNodeRef.current.onended = () => {
        setIsPlaying(false);
        setCurrentTime(0);
      };
      
      toast.info('Playing audio...');
    } catch (error) {
      console.error('Error playing audio:', error);
      toast.error('Failed to play audio');
    }
  };

  const pauseAudio = () => {
    if (sourceNodeRef.current) {
      sourceNodeRef.current.stop();
      setIsPlaying(false);
      toast.info('Audio paused');
    }
  };

  const stopAudio = () => {
    if (sourceNodeRef.current) {
      sourceNodeRef.current.stop();
      setIsPlaying(false);
      setCurrentTime(0);
      toast.info('Audio stopped');
    }
  };

  const applyTrim = async () => {
    if (!currentProject?.audioBuffer || !audioContextRef.current) return;

    try {
      setIsProcessing(true);

      const startTime = (trimStart[0] / 100) * currentProject.duration;
      const endTime = (trimEnd[0] / 100) * currentProject.duration;
      const newDuration = endTime - startTime;

      // Create new trimmed audio buffer
      const trimmedBuffer = await createTrimmedAudioBuffer(currentProject.audioBuffer, startTime, endTime);

      // Update current project with trimmed audio
      const updatedProject = {
        ...currentProject,
        audioBuffer: trimmedBuffer,
        duration: newDuration
      };

      setCurrentProject(updatedProject);
      setDuration(newDuration);
      setTrimEnd([100]); // Reset trim end to 100%

      // Redraw waveform
      drawWaveform(trimmedBuffer);

      const operation: EditOperation = {
        type: 'trim',
        parameters: { startTime, endTime, originalDuration: currentProject.duration, newDuration },
        timestamp: new Date()
      };

      setEditHistory(prev => [...prev, operation]);
      toast.success(`✅ Audio trimmed: ${currentProject.duration.toFixed(2)}s → ${newDuration.toFixed(2)}s`);

    } catch (error) {
      console.error('Trim error:', error);
      toast.error('❌ Failed to trim audio');
    } finally {
      setIsProcessing(false);
    }
  };

  const createTrimmedAudioBuffer = async (audioBuffer: AudioBuffer, startTime: number, endTime: number): Promise<AudioBuffer> => {
    if (!audioContextRef.current) throw new Error('Audio context not available');

    const sampleRate = audioBuffer.sampleRate;
    const startSample = Math.floor(startTime * sampleRate);
    const endSample = Math.floor(endTime * sampleRate);
    const newLength = endSample - startSample;

    const trimmedBuffer = audioContextRef.current.createBuffer(
      audioBuffer.numberOfChannels,
      newLength,
      sampleRate
    );

    // Copy trimmed audio data
    for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {
      const originalData = audioBuffer.getChannelData(channel);
      const trimmedData = trimmedBuffer.getChannelData(channel);

      for (let i = 0; i < newLength; i++) {
        trimmedData[i] = originalData[startSample + i] || 0;
      }
    }

    return trimmedBuffer;
  };

  const applyVolumeChange = async () => {
    if (!currentProject?.audioBuffer || !audioContextRef.current) return;

    try {
      setIsProcessing(true);

      const volumeMultiplier = volume[0];
      const processedBuffer = await applyVolumeToAudioBuffer(currentProject.audioBuffer, volumeMultiplier);

      // Update current project
      const updatedProject = {
        ...currentProject,
        audioBuffer: processedBuffer
      };

      setCurrentProject(updatedProject);
      drawWaveform(processedBuffer);

      const operation: EditOperation = {
        type: 'volume',
        parameters: { volume: volumeMultiplier, originalVolume: 1.0 },
        timestamp: new Date()
      };

      setEditHistory(prev => [...prev, operation]);
      toast.success(`✅ Volume adjusted: ${Math.round(volumeMultiplier * 100)}%`);

    } catch (error) {
      console.error('Volume adjustment error:', error);
      toast.error('❌ Failed to adjust volume');
    } finally {
      setIsProcessing(false);
    }
  };

  const applyFadeEffect = async () => {
    if (!currentProject?.audioBuffer || !audioContextRef.current) return;

    try {
      setIsProcessing(true);

      const fadeInSecs = fadeInDuration[0];
      const fadeOutSecs = fadeOutDuration[0];

      const processedBuffer = await applyFadeToAudioBuffer(
        currentProject.audioBuffer,
        fadeInSecs,
        fadeOutSecs
      );

      // Update current project
      const updatedProject = {
        ...currentProject,
        audioBuffer: processedBuffer
      };

      setCurrentProject(updatedProject);
      drawWaveform(processedBuffer);

      const operation: EditOperation = {
        type: 'fade',
        parameters: {
          fadeIn: fadeInSecs,
          fadeOut: fadeOutSecs,
          duration: currentProject.duration
        },
        timestamp: new Date()
      };

      setEditHistory(prev => [...prev, operation]);
      toast.success(`✅ Fade effects applied: ${fadeInSecs}s in, ${fadeOutSecs}s out`);

    } catch (error) {
      console.error('Fade effect error:', error);
      toast.error('❌ Failed to apply fade effects');
    } finally {
      setIsProcessing(false);
    }
  };

  const applyVolumeToAudioBuffer = async (audioBuffer: AudioBuffer, volumeMultiplier: number): Promise<AudioBuffer> => {
    if (!audioContextRef.current) throw new Error('Audio context not available');

    const processedBuffer = audioContextRef.current.createBuffer(
      audioBuffer.numberOfChannels,
      audioBuffer.length,
      audioBuffer.sampleRate
    );

    // Apply volume to each channel
    for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {
      const originalData = audioBuffer.getChannelData(channel);
      const processedData = processedBuffer.getChannelData(channel);

      for (let i = 0; i < originalData.length; i++) {
        // Apply volume with clipping protection
        processedData[i] = Math.max(-1, Math.min(1, originalData[i] * volumeMultiplier));
      }
    }

    return processedBuffer;
  };

  const applyFadeToAudioBuffer = async (audioBuffer: AudioBuffer, fadeInDuration: number, fadeOutDuration: number): Promise<AudioBuffer> => {
    if (!audioContextRef.current) throw new Error('Audio context not available');

    const sampleRate = audioBuffer.sampleRate;
    const fadeInSamples = Math.floor(fadeInDuration * sampleRate);
    const fadeOutSamples = Math.floor(fadeOutDuration * sampleRate);
    const totalSamples = audioBuffer.length;

    const processedBuffer = audioContextRef.current.createBuffer(
      audioBuffer.numberOfChannels,
      audioBuffer.length,
      audioBuffer.sampleRate
    );

    // Apply fade to each channel
    for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {
      const originalData = audioBuffer.getChannelData(channel);
      const processedData = processedBuffer.getChannelData(channel);

      for (let i = 0; i < totalSamples; i++) {
        let multiplier = 1.0;

        // Apply fade in
        if (i < fadeInSamples) {
          multiplier = i / fadeInSamples;
        }

        // Apply fade out
        if (i > totalSamples - fadeOutSamples) {
          const fadeOutProgress = (totalSamples - i) / fadeOutSamples;
          multiplier = Math.min(multiplier, fadeOutProgress);
        }

        processedData[i] = originalData[i] * multiplier;
      }
    }

    return processedBuffer;
  };

  const applySpeedChange = async () => {
    if (!currentProject?.audioBuffer || !audioContextRef.current) return;

    try {
      setIsProcessing(true);

      const speedMultiplier = playbackRate[0];
      const processedBuffer = await changeAudioSpeed(currentProject.audioBuffer, speedMultiplier);

      // Update current project
      const newDuration = currentProject.duration / speedMultiplier;
      const updatedProject = {
        ...currentProject,
        audioBuffer: processedBuffer,
        duration: newDuration
      };

      setCurrentProject(updatedProject);
      setDuration(newDuration);
      drawWaveform(processedBuffer);

      const operation: EditOperation = {
        type: 'speed',
        parameters: {
          speed: speedMultiplier,
          originalDuration: currentProject.duration,
          newDuration: newDuration
        },
        timestamp: new Date()
      };

      setEditHistory(prev => [...prev, operation]);
      toast.success(`✅ Speed changed: ${speedMultiplier}x (${currentProject.duration.toFixed(2)}s → ${newDuration.toFixed(2)}s)`);

    } catch (error) {
      console.error('Speed change error:', error);
      toast.error('❌ Failed to change speed');
    } finally {
      setIsProcessing(false);
    }
  };

  const applyNoiseReduction = async () => {
    if (!currentProject?.audioBuffer || !audioContextRef.current) return;

    try {
      setIsProcessing(true);

      const reductionStrength = 0.7; // 70% noise reduction
      const processedBuffer = await reduceNoise(currentProject.audioBuffer, reductionStrength);

      // Update current project
      const updatedProject = {
        ...currentProject,
        audioBuffer: processedBuffer
      };

      setCurrentProject(updatedProject);
      drawWaveform(processedBuffer);

      const operation: EditOperation = {
        type: 'noise_reduction',
        parameters: { strength: reductionStrength },
        timestamp: new Date()
      };

      setEditHistory(prev => [...prev, operation]);
      toast.success(`✅ Noise reduction applied (${Math.round(reductionStrength * 100)}% strength)`);

    } catch (error) {
      console.error('Noise reduction error:', error);
      toast.error('❌ Failed to apply noise reduction');
    } finally {
      setIsProcessing(false);
    }
  };

  const changeAudioSpeed = async (audioBuffer: AudioBuffer, speedMultiplier: number): Promise<AudioBuffer> => {
    if (!audioContextRef.current) throw new Error('Audio context not available');

    const originalLength = audioBuffer.length;
    const newLength = Math.floor(originalLength / speedMultiplier);

    const processedBuffer = audioContextRef.current.createBuffer(
      audioBuffer.numberOfChannels,
      newLength,
      audioBuffer.sampleRate
    );

    // Resample audio for speed change
    for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {
      const originalData = audioBuffer.getChannelData(channel);
      const processedData = processedBuffer.getChannelData(channel);

      for (let i = 0; i < newLength; i++) {
        const originalIndex = i * speedMultiplier;
        const lowerIndex = Math.floor(originalIndex);
        const upperIndex = Math.ceil(originalIndex);
        const fraction = originalIndex - lowerIndex;

        // Linear interpolation for smooth resampling
        if (upperIndex < originalLength) {
          const lowerValue = originalData[lowerIndex] || 0;
          const upperValue = originalData[upperIndex] || 0;
          processedData[i] = lowerValue + (upperValue - lowerValue) * fraction;
        } else {
          processedData[i] = originalData[lowerIndex] || 0;
        }
      }
    }

    return processedBuffer;
  };

  const reduceNoise = async (audioBuffer: AudioBuffer, strength: number): Promise<AudioBuffer> => {
    if (!audioContextRef.current) throw new Error('Audio context not available');

    const processedBuffer = audioContextRef.current.createBuffer(
      audioBuffer.numberOfChannels,
      audioBuffer.length,
      audioBuffer.sampleRate
    );

    // Simple noise reduction using spectral gating
    for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {
      const originalData = audioBuffer.getChannelData(channel);
      const processedData = processedBuffer.getChannelData(channel);

      // Calculate noise floor (average of lowest 10% of samples)
      const sortedSamples = Array.from(originalData).map(Math.abs).sort((a, b) => a - b);
      const noiseFloor = sortedSamples.slice(0, Math.floor(sortedSamples.length * 0.1))
        .reduce((sum, val) => sum + val, 0) / (sortedSamples.length * 0.1);

      const threshold = noiseFloor * (1 + strength);

      // Apply noise gate
      for (let i = 0; i < originalData.length; i++) {
        const sample = originalData[i];
        const amplitude = Math.abs(sample);

        if (amplitude < threshold) {
          // Reduce noise by applying gain reduction
          processedData[i] = sample * (1 - strength * 0.8);
        } else {
          // Keep signal above threshold
          processedData[i] = sample;
        }
      }
    }

    return processedBuffer;
  };

  const undoLastOperation = () => {
    if (editHistory.length === 0) {
      toast.info('No operations to undo');
      return;
    }

    const lastOperation = editHistory[editHistory.length - 1];
    setEditHistory(prev => prev.slice(0, -1));
    toast.success(`Undid: ${lastOperation.type}`);
  };

  const exportAudio = () => {
    if (!currentProject) {
      toast.error('No audio project to export');
      return;
    }

    // For demo purposes, we'll export the original file
    // In a real implementation, you'd apply all the edits and create a new audio file
    const url = URL.createObjectURL(currentProject.originalFile);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${currentProject.name}_edited.${currentProject.originalFile.name.split('.').pop()}`;
    link.click();
    URL.revokeObjectURL(url);
    
    toast.success('Audio exported successfully!');
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Edit className="w-5 h-5" />
            Audio Editor & Enhancer
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* File Upload */}
          {!currentProject && (
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <input
                ref={fileInputRef}
                type="file"
                accept="audio/*"
                onChange={handleFileUpload}
                className="hidden"
              />
              <Edit className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <p className="text-lg font-medium mb-2">Upload Audio File</p>
              <p className="text-gray-600 mb-4">
                Select an audio file to start editing
              </p>
              <Button 
                onClick={() => fileInputRef.current?.click()}
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Loading...
                  </>
                ) : (
                  <>
                    <Upload className="w-4 h-4 mr-2" />
                    Choose Audio File
                  </>
                )}
              </Button>
              <p className="text-sm text-gray-500 mt-2">
                Supported: MP3, WAV, OGG, M4A, FLAC
              </p>
            </div>
          )}

          {/* Audio Project */}
          {currentProject && (
            <div className="space-y-6">
              {/* Project Info */}
              <Card className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold">{currentProject.name}</h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                      <span>{formatTime(currentProject.duration)}</span>
                      <span>{formatFileSize(currentProject.originalFile.size)}</span>
                      <Badge variant="outline">{currentProject.channels} CH</Badge>
                      <Badge variant="outline">{currentProject.sampleRate} Hz</Badge>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button onClick={undoLastOperation} size="sm" variant="outline">
                      <RotateCcw className="w-4 h-4 mr-1" />
                      Undo
                    </Button>
                    <Button onClick={exportAudio} size="sm">
                      <Download className="w-4 h-4 mr-1" />
                      Export
                    </Button>
                  </div>
                </div>
              </Card>

              {/* Waveform Display */}
              <Card className="p-4">
                <h4 className="font-medium mb-4">Waveform</h4>
                <canvas
                  ref={canvasRef}
                  width={800}
                  height={150}
                  className="w-full border rounded-lg bg-gray-50"
                />
              </Card>

              {/* Playback Controls */}
              <Card className="p-4">
                <h4 className="font-medium mb-4">Playback Controls</h4>
                <div className="flex items-center justify-center space-x-4 mb-4">
                  <Button
                    onClick={playAudio}
                    disabled={isPlaying}
                    variant="outline"
                  >
                    <Play className="w-4 h-4 mr-2" />
                    Play
                  </Button>
                  <Button
                    onClick={pauseAudio}
                    disabled={!isPlaying}
                    variant="outline"
                  >
                    <Pause className="w-4 h-4 mr-2" />
                    Pause
                  </Button>
                  <Button
                    onClick={stopAudio}
                    variant="outline"
                  >
                    <Square className="w-4 h-4 mr-2" />
                    Stop
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>Volume: {Math.round(volume[0] * 100)}%</Label>
                    <div className="flex items-center space-x-2 mt-2">
                      <Volume2 className="w-4 h-4" />
                      <Slider
                        value={volume}
                        onValueChange={setVolume}
                        max={2}
                        min={0}
                        step={0.1}
                        className="flex-1"
                      />
                    </div>
                  </div>

                  <div>
                    <Label>Speed: {playbackRate[0]}x</Label>
                    <div className="flex items-center space-x-2 mt-2">
                      <Zap className="w-4 h-4" />
                      <Slider
                        value={playbackRate}
                        onValueChange={setPlaybackRate}
                        max={2}
                        min={0.5}
                        step={0.1}
                        className="flex-1"
                      />
                    </div>
                  </div>
                </div>
              </Card>

              {/* Editing Tools */}
              <Tabs defaultValue="trim" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="trim">Trim</TabsTrigger>
                  <TabsTrigger value="effects">Effects</TabsTrigger>
                  <TabsTrigger value="enhance">Enhance</TabsTrigger>
                  <TabsTrigger value="history">History</TabsTrigger>
                </TabsList>

                <TabsContent value="trim" className="space-y-4">
                  <Card className="p-4">
                    <h4 className="font-medium mb-4">Trim Audio</h4>
                    <div className="space-y-4">
                      <div>
                        <Label>Start Position: {trimStart[0].toFixed(1)}%</Label>
                        <Slider
                          value={trimStart}
                          onValueChange={(value) => {
                            setTrimStart(value);
                            if (currentProject?.audioBuffer) {
                              drawWaveform(currentProject.audioBuffer);
                            }
                          }}
                          max={100}
                          min={0}
                          step={0.1}
                          className="mt-2"
                        />
                      </div>
                      <div>
                        <Label>End Position: {trimEnd[0].toFixed(1)}%</Label>
                        <Slider
                          value={trimEnd}
                          onValueChange={(value) => {
                            setTrimEnd(value);
                            if (currentProject?.audioBuffer) {
                              drawWaveform(currentProject.audioBuffer);
                            }
                          }}
                          max={100}
                          min={0}
                          step={0.1}
                          className="mt-2"
                        />
                      </div>
                      <Button onClick={applyTrim} className="w-full">
                        <Scissors className="w-4 h-4 mr-2" />
                        Apply Trim
                      </Button>
                    </div>
                  </Card>
                </TabsContent>

                <TabsContent value="effects" className="space-y-4">
                  <Card className="p-4">
                    <h4 className="font-medium mb-4">Audio Effects</h4>
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label>Fade In: {fadeInDuration[0].toFixed(1)}s</Label>
                          <Slider
                            value={fadeInDuration}
                            onValueChange={setFadeInDuration}
                            max={5}
                            min={0}
                            step={0.1}
                            className="mt-2"
                          />
                        </div>
                        <div>
                          <Label>Fade Out: {fadeOutDuration[0].toFixed(1)}s</Label>
                          <Slider
                            value={fadeOutDuration}
                            onValueChange={setFadeOutDuration}
                            max={5}
                            min={0}
                            step={0.1}
                            className="mt-2"
                          />
                        </div>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        <Button onClick={applyFadeEffect} variant="outline">
                          Apply Fade Effects
                        </Button>
                        <Button onClick={applySpeedChange} variant="outline">
                          Apply Speed Change
                        </Button>
                      </div>
                    </div>
                  </Card>
                </TabsContent>

                <TabsContent value="enhance" className="space-y-4">
                  <Card className="p-4">
                    <h4 className="font-medium mb-4">Audio Enhancement</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Button onClick={applyVolumeChange} variant="outline">
                        <Volume2 className="w-4 h-4 mr-2" />
                        Normalize Volume
                      </Button>
                      <Button onClick={applyNoiseReduction} variant="outline">
                        <Zap className="w-4 h-4 mr-2" />
                        Reduce Noise
                      </Button>
                    </div>
                  </Card>
                </TabsContent>

                <TabsContent value="history" className="space-y-4">
                  <Card className="p-4">
                    <h4 className="font-medium mb-4">Edit History ({editHistory.length})</h4>
                    {editHistory.length === 0 ? (
                      <p className="text-gray-500 text-center py-4">No edits applied yet</p>
                    ) : (
                      <div className="space-y-2 max-h-48 overflow-y-auto">
                        {editHistory.map((operation, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <div>
                              <span className="font-medium capitalize">{operation.type.replace('_', ' ')}</span>
                              <span className="text-sm text-gray-600 ml-2">
                                {operation.timestamp.toLocaleTimeString()}
                              </span>
                            </div>
                            <Badge variant="outline">{operation.type}</Badge>
                          </div>
                        ))}
                      </div>
                    )}
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          )}

          {/* Info Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="p-4">
                <h4 className="font-semibold text-blue-900 mb-2">🎛️ Editing Features</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Trim audio segments</li>
                  <li>• Volume & speed adjustment</li>
                  <li>• Fade in/out effects</li>
                  <li>• Noise reduction</li>
                  <li>• Real-time waveform display</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="bg-green-50 border-green-200">
              <CardContent className="p-4">
                <h4 className="font-semibold text-green-900 mb-2">⚡ Enhancement Tools</h4>
                <ul className="text-sm text-green-800 space-y-1">
                  <li>• Audio normalization</li>
                  <li>• Quality enhancement</li>
                  <li>• Edit history tracking</li>
                  <li>• Undo/redo operations</li>
                  <li>• Multiple export formats</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

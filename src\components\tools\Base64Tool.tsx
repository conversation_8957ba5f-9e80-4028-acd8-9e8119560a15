import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { Copy, Upload, Download } from 'lucide-react';

export const Base64Tool: React.FC = () => {
  const [textInput, setTextInput] = useState('');
  const [base64Output, setBase64Output] = useState('');
  const [base64Input, setBase64Input] = useState('');
  const [textOutput, setTextOutput] = useState('');
  const [fileBase64, setFileBase64] = useState('');
  const [fileName, setFileName] = useState('');

  const encodeToBase64 = () => {
    try {
      const encoded = btoa(unescape(encodeURIComponent(textInput)));
      setBase64Output(encoded);
      toast.success('Teks berhasil di-encode ke Base64!');
    } catch (error) {
      toast.error('Gagal encode teks');
    }
  };

  const decodeFromBase64 = () => {
    try {
      const decoded = decodeURIComponent(escape(atob(base64Input)));
      setTextOutput(decoded);
      toast.success('Base64 berhasil di-decode!');
    } catch (error) {
      toast.error('Base64 tidak valid atau gagal decode');
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      const base64 = result.split(',')[1]; // Remove data:type;base64, prefix
      setFileBase64(base64);
      setFileName(file.name);
      toast.success(`File ${file.name} berhasil di-encode ke Base64!`);
    };
    reader.readAsDataURL(file);
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Berhasil disalin ke clipboard!');
    } catch (error) {
      toast.error('Gagal menyalin ke clipboard');
    }
  };

  const downloadBase64 = (content: string, filename: string) => {
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.click();
    URL.revokeObjectURL(url);
    toast.success('File berhasil didownload!');
  };

  const clearAll = () => {
    setTextInput('');
    setBase64Output('');
    setBase64Input('');
    setTextOutput('');
    setFileBase64('');
    setFileName('');
    toast.success('Semua field berhasil dibersihkan!');
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔐 Base64 Encoder/Decoder
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Text to Base64 */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">📝 Text to Base64</h3>
            <Textarea
              value={textInput}
              onChange={(e) => setTextInput(e.target.value)}
              placeholder="Masukkan teks yang ingin di-encode..."
              className="min-h-[100px]"
            />
            <div className="flex gap-2">
              <Button onClick={encodeToBase64} disabled={!textInput}>
                🔒 Encode
              </Button>
              <Button variant="outline" onClick={() => setTextInput('')}>
                🗑️ Clear
              </Button>
            </div>
            {base64Output && (
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <label className="text-sm font-medium">Base64 Output:</label>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={() => copyToClipboard(base64Output)}>
                      <Copy className="w-4 h-4 mr-1" />
                      Copy
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => downloadBase64(base64Output, 'encoded.txt')}>
                      <Download className="w-4 h-4 mr-1" />
                      Download
                    </Button>
                  </div>
                </div>
                <Textarea
                  value={base64Output}
                  readOnly
                  className="min-h-[100px] font-mono text-sm bg-gray-50"
                />
              </div>
            )}
          </div>

          {/* Base64 to Text */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">🔓 Base64 to Text</h3>
            <Textarea
              value={base64Input}
              onChange={(e) => setBase64Input(e.target.value)}
              placeholder="Masukkan Base64 yang ingin di-decode..."
              className="min-h-[100px] font-mono text-sm"
            />
            <div className="flex gap-2">
              <Button onClick={decodeFromBase64} disabled={!base64Input}>
                🔓 Decode
              </Button>
              <Button variant="outline" onClick={() => setBase64Input('')}>
                🗑️ Clear
              </Button>
            </div>
            {textOutput && (
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <label className="text-sm font-medium">Decoded Text:</label>
                  <Button variant="outline" size="sm" onClick={() => copyToClipboard(textOutput)}>
                    <Copy className="w-4 h-4 mr-1" />
                    Copy
                  </Button>
                </div>
                <Textarea
                  value={textOutput}
                  readOnly
                  className="min-h-[100px] bg-gray-50"
                />
              </div>
            )}
          </div>

          {/* File to Base64 */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">📁 File to Base64</h3>
            <div className="flex items-center gap-2">
              <Input
                type="file"
                onChange={handleFileUpload}
                className="flex-1"
              />
              <Button variant="outline" onClick={() => {
                setFileBase64('');
                setFileName('');
              }}>
                🗑️ Clear
              </Button>
            </div>
            {fileBase64 && (
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <label className="text-sm font-medium">File: {fileName}</label>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={() => copyToClipboard(fileBase64)}>
                      <Copy className="w-4 h-4 mr-1" />
                      Copy
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => downloadBase64(fileBase64, `${fileName}.base64`)}>
                      <Download className="w-4 h-4 mr-1" />
                      Download
                    </Button>
                  </div>
                </div>
                <Textarea
                  value={fileBase64}
                  readOnly
                  className="min-h-[150px] font-mono text-xs bg-gray-50"
                />
              </div>
            )}
          </div>

          {/* Clear All Button */}
          <div className="pt-4 border-t">
            <Button variant="destructive" onClick={clearAll} className="w-full">
              🗑️ Clear All
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

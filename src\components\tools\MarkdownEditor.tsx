import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import { Copy, Download, Upload, Eye, EyeOff, Split, Maximize2 } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import 'highlight.js/styles/github.css';

export const MarkdownEditor: React.FC = () => {
  const [markdown, setMarkdown] = useState('');
  const [viewMode, setViewMode] = useState<'split' | 'edit' | 'preview'>('split');
  const [fileName, setFileName] = useState('document.md');

  const sampleMarkdown = `# 📝 Markdown Editor Demo

## Fitur Utama

### Text Formatting
- **Bold text** dengan \`**bold**\`
- *Italic text* dengan \`*italic*\`
- ~~Strikethrough~~ dengan \`~~text~~\`
- \`Inline code\` dengan backticks

### Lists
1. Numbered list item 1
2. Numbered list item 2
   - Nested bullet point
   - Another nested item

### Code Blocks
\`\`\`javascript
function greet(name) {
  console.log(\`Hello, \${name}!\`);
}

greet("World");
\`\`\`

### Tables
| Feature | Status | Priority |
|---------|--------|----------|
| Editor | ✅ Done | High |
| Preview | ✅ Done | High |
| Export | ✅ Done | Medium |

### Links & Images
- [Link to Google](https://google.com)
- ![Alt text](https://via.placeholder.com/150x100?text=Image)

### Blockquotes
> This is a blockquote.
> It can span multiple lines.

### Horizontal Rule
---

### Task Lists
- [x] Completed task
- [ ] Pending task
- [ ] Another pending task

## Math (if supported)
Inline math: $E = mc^2$

Block math:
$$
\\sum_{i=1}^{n} x_i = x_1 + x_2 + \\cdots + x_n
$$
`;

  const loadSample = () => {
    setMarkdown(sampleMarkdown);
    toast.success('Sample markdown dimuat!');
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files && event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = (e.target && e.target.result) as string;
      setMarkdown(content);
      setFileName(file.name);
      toast.success(`File ${file.name} berhasil dimuat!`);
    };
    reader.readAsText(file);
  };

  const downloadMarkdown = () => {
    const blob = new Blob([markdown], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
    URL.revokeObjectURL(url);
    toast.success('Markdown berhasil didownload!');
  };

  const downloadHTML = () => {
    const htmlContent = `<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${fileName.replace('.md', '')}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; line-height: 1.6; }
        code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 5px; overflow-x: auto; }
        blockquote { border-left: 4px solid #ddd; margin: 0; padding-left: 20px; color: #666; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        img { max-width: 100%; height: auto; }
    </style>
</head>
<body>
    <div id="content"></div>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script>
        document.getElementById('content').innerHTML = marked.parse(\`${markdown.replace(/`/g, '\\`')}\`);
    </script>
</body>
</html>`;

    const blob = new Blob([htmlContent], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName.replace('.md', '.html');
    a.click();
    URL.revokeObjectURL(url);
    toast.success('HTML berhasil didownload!');
  };

  const copyMarkdown = async () => {
    try {
      await navigator.clipboard.writeText(markdown);
      toast.success('Markdown berhasil disalin!');
    } catch (error) {
      toast.error('Gagal menyalin markdown');
    }
  };

  const insertTemplate = (template: string) => {
    const textarea = document.querySelector('textarea');
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const newText = markdown.substring(0, start) + template + markdown.substring(end);
    setMarkdown(newText);

    // Set cursor position after template
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + template.length, start + template.length);
    }, 0);
  };

  const templates = [
    { name: 'Header', template: '# ' },
    { name: 'Bold', template: '**text**' },
    { name: 'Italic', template: '*text*' },
    { name: 'Code', template: '`code`' },
    { name: 'Link', template: '[text](url)' },
    { name: 'Image', template: '![alt](url)' },
    { name: 'List', template: '- item\n- item\n- item' },
    { name: 'Table', template: '| Header 1 | Header 2 |\n|----------|----------|\n| Cell 1   | Cell 2   |' },
    { name: 'Quote', template: '> Quote text' },
    { name: 'Code Block', template: '```\ncode here\n```' }
  ];

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            📝 Markdown Editor
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Toolbar */}
          <div className="flex flex-wrap gap-2 p-3 bg-gray-50 rounded-lg">
            <div className="flex gap-1">
              <Button
                variant={viewMode === 'edit' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('edit')}
              >
                <EyeOff className="w-4 h-4 mr-1" />
                Edit
              </Button>
              <Button
                variant={viewMode === 'split' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('split')}
              >
                <Split className="w-4 h-4 mr-1" />
                Split
              </Button>
              <Button
                variant={viewMode === 'preview' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('preview')}
              >
                <Eye className="w-4 h-4 mr-1" />
                Preview
              </Button>
            </div>

            <div className="border-l border-gray-300 mx-2"></div>

            <div className="flex gap-1 flex-wrap">
              {templates.slice(0, 6).map((template) => (
                <Button
                  key={template.name}
                  variant="outline"
                  size="sm"
                  onClick={() => insertTemplate(template.template)}
                  title={`Insert ${template.name}`}
                >
                  {template.name}
                </Button>
              ))}
            </div>
          </div>

          {/* File Name */}
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">File Name:</label>
            <Input
              value={fileName}
              onChange={(e) => setFileName(e.target.value)}
              className="flex-1 max-w-xs"
            />
          </div>

          {/* Editor/Preview Area */}
          <div className={`grid gap-4 ${viewMode === 'split' ? 'md:grid-cols-2' : 'grid-cols-1'}`}>
            {/* Editor */}
            {(viewMode === 'edit' || viewMode === 'split') && (
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <label className="text-sm font-medium">Markdown Editor:</label>
                  <div className="flex gap-1">
                    <Button variant="outline" size="sm" onClick={loadSample}>
                      📄 Sample
                    </Button>
                    <label className="cursor-pointer">
                      <Button variant="outline" size="sm" asChild>
                        <span>
                          <Upload className="w-4 h-4" />
                        </span>
                      </Button>
                      <Input
                        type="file"
                        accept=".md,.txt"
                        onChange={handleFileUpload}
                        className="hidden"
                      />
                    </label>
                  </div>
                </div>
                <Textarea
                  value={markdown}
                  onChange={(e) => setMarkdown(e.target.value)}
                  placeholder="# Tulis markdown Anda di sini..."
                  className="min-h-[500px] font-mono text-sm"
                />
              </div>
            )}

            {/* Preview */}
            {(viewMode === 'preview' || viewMode === 'split') && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Live Preview:</label>
                <div className="border rounded-lg p-4 min-h-[500px] bg-white overflow-auto prose prose-sm max-w-none">
                  <ReactMarkdown
                    remarkPlugins={[remarkGfm]}
                    rehypePlugins={[rehypeHighlight]}
                  >
                    {markdown || '*Preview akan muncul di sini...*'}
                  </ReactMarkdown>
                </div>
              </div>
            )}
          </div>

          {/* Quick Templates */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Quick Templates:</label>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
              {templates.map((template) => (
                <Button
                  key={template.name}
                  variant="outline"
                  size="sm"
                  onClick={() => insertTemplate(template.template)}
                  className="text-xs"
                >
                  {template.name}
                </Button>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4 border-t">
            <Button onClick={copyMarkdown} disabled={!markdown.trim()}>
              <Copy className="w-4 h-4 mr-2" />
              Copy MD
            </Button>
            <Button variant="outline" onClick={downloadMarkdown} disabled={!markdown.trim()}>
              <Download className="w-4 h-4 mr-2" />
              Download MD
            </Button>
            <Button variant="outline" onClick={downloadHTML} disabled={!markdown.trim()}>
              <Download className="w-4 h-4 mr-2" />
              Export HTML
            </Button>
          </div>

          {/* Info */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">ℹ️ Fitur Markdown Editor:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• <strong>Live Preview</strong>: Lihat hasil real-time</li>
              <li>• <strong>Split View</strong>: Edit dan preview bersamaan</li>
              <li>• <strong>Quick Templates</strong>: Insert elemen markdown cepat</li>
              <li>• <strong>Export</strong>: Download sebagai .md atau .html</li>
              <li>• <strong>GitHub Flavored Markdown</strong>: Support tables, task lists, dll</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

@echo off
echo ========================================
echo KIKAZE-AI Desktop Quick Test
echo ========================================
echo.

echo 1. Checking Node.js...
node --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found!
    pause
    exit /b 1
)

echo 2. Checking build files...
if not exist "dist\index.html" (
    echo ERROR: Build files not found! Running build...
    npm run build
    if %errorlevel% neq 0 (
        echo ERROR: Build failed!
        pause
        exit /b 1
    )
)

echo 3. Killing existing processes...
taskkill /f /im electron.exe >nul 2>&1
taskkill /f /im KIKAZE-AI.exe >nul 2>&1

echo 4. Starting KIKAZE-AI test...
echo.
echo If a window opens, the app is working!
echo Press Ctrl+C to stop the test.
echo.

npx electron test-app.js

echo.
echo Test completed!
pause

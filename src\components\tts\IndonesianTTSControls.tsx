import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import { Volume2, Play, Pause, Square, Settings, TestTube, User, MapPin, Star } from 'lucide-react';
import { IndonesianTTSManager, IndonesianVoice, TTSSettings } from '../../utils/indonesianTTS';

interface IndonesianTTSControlsProps {
  text?: string;
  onSpeak?: (text: string) => void;
  compact?: boolean;
  showAdvanced?: boolean;
}

export const IndonesianTTSControls: React.FC<IndonesianTTSControlsProps> = ({
  text = '',
  onSpeak,
  compact = false,
  showAdvanced = false
}) => {
  const [ttsManager] = useState(() => IndonesianTTSManager.getInstance());
  const [voices, setVoices] = useState<IndonesianVoice[]>([]);
  const [settings, setSettings] = useState<TTSSettings>(ttsManager.getSettings());
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [showSettings, setShowSettings] = useState(showAdvanced);

  useEffect(() => {
    const loadVoices = async () => {
      // Wait a bit for voices to load
      setTimeout(() => {
        const availableVoices = ttsManager.getAvailableVoices();
        setVoices(availableVoices);
        
        // Set recommended voice for chat context
        if (!settings.voice && availableVoices.length > 0) {
          const recommendedVoice = ttsManager.getVoiceRecommendation('chat');
          if (recommendedVoice) {
            updateSetting('voice', recommendedVoice);
          }
        }
      }, 500);
    };

    loadVoices();

    // Listen for voices changed event
    const handleVoicesChanged = () => {
      setTimeout(loadVoices, 100);
    };

    if (speechSynthesis.onvoiceschanged !== undefined) {
      speechSynthesis.onvoiceschanged = handleVoicesChanged;
    }

    return () => {
      if (speechSynthesis.onvoiceschanged !== undefined) {
        speechSynthesis.onvoiceschanged = null;
      }
    };
  }, []);

  const updateSetting = (key: keyof TTSSettings, value: any) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    ttsManager.updateSettings(newSettings);
  };

  const handleSpeak = async () => {
    if (!text.trim()) {
      toast.error('Tidak ada teks untuk dibacakan');
      return;
    }

    try {
      setIsPlaying(true);
      setIsPaused(false);
      
      if (onSpeak) {
        onSpeak(text);
      } else {
        await ttsManager.speak(text);
      }
      
      toast.success('Mulai membacakan teks...');
    } catch (error) {
      console.error('TTS Error:', error);
      toast.error('Gagal membacakan teks');
    } finally {
      setIsPlaying(false);
      setIsPaused(false);
    }
  };

  const handlePause = () => {
    ttsManager.pause();
    setIsPaused(true);
    toast.info('Dijeda');
  };

  const handleResume = () => {
    ttsManager.resume();
    setIsPaused(false);
    toast.info('Dilanjutkan');
  };

  const handleStop = () => {
    ttsManager.stop();
    setIsPlaying(false);
    setIsPaused(false);
    toast.info('Dihentikan');
  };

  const testVoice = (voiceId: string) => {
    ttsManager.testVoice(voiceId);
    toast.info('Testing voice...');
  };

  const getVoiceQualityColor = (quality: string) => {
    switch (quality) {
      case 'neural': return 'bg-green-100 text-green-800';
      case 'premium': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getGenderIcon = (gender: string) => {
    return gender === 'female' ? '👩' : '👨';
  };

  const getAgeDescription = (age: string) => {
    switch (age) {
      case 'young': return 'Muda';
      case 'adult': return 'Dewasa';
      case 'mature': return 'Matang';
      default: return 'Dewasa';
    }
  };

  if (compact) {
    return (
      <div className="flex items-center gap-2">
        <Select value={settings.voice} onValueChange={(value) => updateSetting('voice', value)}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Pilih suara..." />
          </SelectTrigger>
          <SelectContent>
            {voices.map((voice) => (
              <SelectItem key={voice.id} value={voice.id}>
                <div className="flex items-center gap-2">
                  <span>{getGenderIcon(voice.gender)}</span>
                  <span>{voice.name}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <div className="flex items-center gap-1">
          {!isPlaying ? (
            <Button onClick={handleSpeak} size="sm" disabled={!text.trim()}>
              <Play className="w-4 h-4" />
            </Button>
          ) : (
            <>
              {!isPaused ? (
                <Button onClick={handlePause} size="sm" variant="outline">
                  <Pause className="w-4 h-4" />
                </Button>
              ) : (
                <Button onClick={handleResume} size="sm" variant="outline">
                  <Play className="w-4 h-4" />
                </Button>
              )}
              <Button onClick={handleStop} size="sm" variant="destructive">
                <Square className="w-4 h-4" />
              </Button>
            </>
          )}
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Volume2 className="w-5 h-5" />
            Indonesian Text-to-Speech
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowSettings(!showSettings)}
          >
            <Settings className="w-4 h-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Voice Selection */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Pilih Suara Indonesia:</Label>
          <div className="grid gap-3">
            {voices.map((voice) => (
              <div
                key={voice.id}
                className={`p-3 border rounded-lg cursor-pointer transition-all ${
                  settings.voice === voice.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => updateSetting('voice', voice.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{getGenderIcon(voice.gender)}</span>
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{voice.displayName}</span>
                        <Badge className={getVoiceQualityColor(voice.quality)}>
                          {voice.quality}
                        </Badge>
                        {voice.priority <= 3 && (
                          <Badge variant="outline" className="text-yellow-600">
                            <Star className="w-3 h-3 mr-1" />
                            Recommended
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-4 text-xs text-gray-600 mt-1">
                        <span className="flex items-center gap-1">
                          <User className="w-3 h-3" />
                          {getAgeDescription(voice.age)}
                        </span>
                        <span className="flex items-center gap-1">
                          <MapPin className="w-3 h-3" />
                          {voice.region}
                        </span>
                        <span>{voice.accent}</span>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">{voice.description}</p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      testVoice(voice.id);
                    }}
                  >
                    <TestTube className="w-3 h-3 mr-1" />
                    Test
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Playback Controls */}
        <div className="flex items-center justify-center gap-3">
          {!isPlaying ? (
            <Button onClick={handleSpeak} disabled={!text.trim()} className="px-8">
              <Play className="w-4 h-4 mr-2" />
              Bacakan
            </Button>
          ) : (
            <>
              {!isPaused ? (
                <Button onClick={handlePause} variant="outline">
                  <Pause className="w-4 h-4 mr-2" />
                  Jeda
                </Button>
              ) : (
                <Button onClick={handleResume} variant="outline">
                  <Play className="w-4 h-4 mr-2" />
                  Lanjut
                </Button>
              )}
              <Button onClick={handleStop} variant="destructive">
                <Square className="w-4 h-4 mr-2" />
                Stop
              </Button>
            </>
          )}
        </div>

        {/* Advanced Settings */}
        {showSettings && (
          <div className="space-y-4 pt-4 border-t">
            <h4 className="font-medium">Pengaturan Lanjutan</h4>
            
            {/* Speed Control */}
            <div className="space-y-2">
              <div className="flex justify-between">
                <Label>Kecepatan: {settings.rate.toFixed(1)}x</Label>
              </div>
              <Slider
                value={[settings.rate]}
                onValueChange={([value]) => updateSetting('rate', value)}
                min={0.5}
                max={2.0}
                step={0.1}
                className="w-full"
              />
            </div>

            {/* Pitch Control */}
            <div className="space-y-2">
              <div className="flex justify-between">
                <Label>Nada: {settings.pitch.toFixed(1)}</Label>
              </div>
              <Slider
                value={[settings.pitch]}
                onValueChange={([value]) => updateSetting('pitch', value)}
                min={0.5}
                max={2.0}
                step={0.1}
                className="w-full"
              />
            </div>

            {/* Volume Control */}
            <div className="space-y-2">
              <div className="flex justify-between">
                <Label>Volume: {Math.round(settings.volume * 100)}%</Label>
              </div>
              <Slider
                value={[settings.volume]}
                onValueChange={([value]) => updateSetting('volume', value)}
                min={0.1}
                max={1.0}
                step={0.1}
                className="w-full"
              />
            </div>

            {/* Emotional Tone */}
            <div className="space-y-2">
              <Label>Nada Emosi:</Label>
              <Select value={settings.emotionalTone} onValueChange={(value: any) => updateSetting('emotionalTone', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="neutral">😐 Netral</SelectItem>
                  <SelectItem value="friendly">😊 Ramah</SelectItem>
                  <SelectItem value="professional">💼 Profesional</SelectItem>
                  <SelectItem value="enthusiastic">🎉 Antusias</SelectItem>
                  <SelectItem value="calm">😌 Tenang</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Pronunciation Style */}
            <div className="space-y-2">
              <Label>Gaya Pengucapan:</Label>
              <Select value={settings.pronunciation} onValueChange={(value: any) => updateSetting('pronunciation', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="standard">📖 Standard</SelectItem>
                  <SelectItem value="formal">🎓 Formal</SelectItem>
                  <SelectItem value="casual">😎 Santai</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Natural Pauses */}
            <div className="flex items-center justify-between">
              <Label>Jeda Natural</Label>
              <Switch
                checked={settings.naturalPauses}
                onCheckedChange={(checked) => updateSetting('naturalPauses', checked)}
              />
            </div>
          </div>
        )}

        {/* Voice Info */}
        {voices.length === 0 && (
          <div className="text-center py-4 text-gray-500">
            <Volume2 className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>Loading Indonesian voices...</p>
          </div>
        )}

        {voices.length > 0 && (
          <div className="bg-blue-50 p-3 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">🇮🇩 Suara Indonesia Tersedia</h4>
            <p className="text-sm text-blue-800">
              {voices.length} suara Indonesia ditemukan. Pilih suara yang paling natural untuk Anda!
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

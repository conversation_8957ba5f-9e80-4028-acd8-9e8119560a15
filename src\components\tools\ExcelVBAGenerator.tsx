import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTit<PERSON> } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { Download, Copy, Play, FileSpreadsheet, Code } from 'lucide-react';

interface VBATemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  code: string;
  fields: { name: string; type: string; description: string }[];
}

export const ExcelVBAGenerator: React.FC = () => {
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [generatedCode, setGeneratedCode] = useState<string>('');
  const [customFields, setCustomFields] = useState<{ [key: string]: string }>({});
  const [isGenerating, setIsGenerating] = useState(false);

  const vbaTemplates: VBATemplate[] = [
    {
      id: 'form-input',
      name: 'Form Input Generator',
      description: 'Buat form input data dengan validasi otomatis',
      category: 'Forms',
      fields: [
        { name: 'formTitle', type: 'text', description: 'Judul Form' },
        { name: 'fields', type: 'textarea', description: 'Field Names (satu per baris)' },
        { name: 'sheetName', type: 'text', description: 'Nama Sheet Target' }
      ],
      code: `Sub CreateInputForm()
    Dim ws As Worksheet
    Dim formTitle As String
    Dim fields As Variant
    Dim i As Integer
    
    ' Configuration
    formTitle = "{{formTitle}}"
    fields = Split("{{fields}}", vbCrLf)
    
    ' Create or get worksheet
    On Error Resume Next
    Set ws = Worksheets("{{sheetName}}")
    If ws Is Nothing Then
        Set ws = Worksheets.Add
        ws.Name = "{{sheetName}}"
    End If
    On Error GoTo 0
    
    ' Clear existing content
    ws.Cells.Clear
    
    ' Create header
    ws.Range("A1").Value = formTitle
    ws.Range("A1").Font.Bold = True
    ws.Range("A1").Font.Size = 16
    
    ' Create form fields
    For i = 0 To UBound(fields)
        If Trim(fields(i)) <> "" Then
            ws.Cells(i + 3, 1).Value = Trim(fields(i)) & ":"
            ws.Cells(i + 3, 1).Font.Bold = True
            ws.Cells(i + 3, 2).Interior.Color = RGB(240, 240, 240)
            ws.Cells(i + 3, 2).Borders.LineStyle = xlContinuous
        End If
    Next i
    
    ' Auto-fit columns
    ws.Columns("A:B").AutoFit
    
    MsgBox "Form berhasil dibuat di sheet " & ws.Name
End Sub`
    },
    {
      id: 'data-validation',
      name: 'Data Validation Setup',
      description: 'Setup validasi data otomatis untuk form',
      category: 'Validation',
      fields: [
        { name: 'range', type: 'text', description: 'Range Cells (contoh: B3:B10)' },
        { name: 'validationType', type: 'select', description: 'Tipe Validasi' },
        { name: 'validationList', type: 'textarea', description: 'List Options (untuk dropdown)' }
      ],
      code: `Sub SetupDataValidation()
    Dim ws As Worksheet
    Dim validationRange As Range
    Dim validationType As String
    Dim validationList As String
    
    Set ws = ActiveSheet
    validationType = "{{validationType}}"
    validationList = "{{validationList}}"
    
    ' Set validation range
    Set validationRange = ws.Range("{{range}}")
    
    ' Clear existing validation
    validationRange.Validation.Delete
    
    ' Apply validation based on type
    Select Case validationType
        Case "List"
            validationRange.Validation.Add Type:=xlValidateList, _
                AlertStyle:=xlValidAlertStop, _
                Formula1:=validationList
        Case "Number"
            validationRange.Validation.Add Type:=xlValidateWholeNumber, _
                AlertStyle:=xlValidAlertStop, _
                Operator:=xlGreater, Formula1:="0"
        Case "Date"
            validationRange.Validation.Add Type:=xlValidateDate, _
                AlertStyle:=xlValidAlertStop, _
                Operator:=xlGreaterEqual, Formula1:=Date
        Case "Email"
            validationRange.Validation.Add Type:=xlValidateCustom, _
                AlertStyle:=xlValidAlertStop, _
                Formula1:="=AND(ISERROR(FIND(" ",A1))=TRUE,LEN(A1)-LEN(SUBSTITUTE(A1,""@"",""""))=1)"
    End Select
    
    ' Set error message
    With validationRange.Validation
        .ErrorTitle = "Input Tidak Valid"
        .ErrorMessage = "Silakan masukkan data yang sesuai format."
    End With
    
    MsgBox "Validasi data berhasil diterapkan pada range " & validationRange.Address
End Sub`
    },
    {
      id: 'auto-report',
      name: 'Auto Report Generator',
      description: 'Generate laporan otomatis dengan chart',
      category: 'Reports',
      fields: [
        { name: 'dataRange', type: 'text', description: 'Range Data (contoh: A1:D100)' },
        { name: 'reportTitle', type: 'text', description: 'Judul Laporan' },
        { name: 'chartType', type: 'select', description: 'Tipe Chart' }
      ],
      code: `Sub GenerateAutoReport()
    Dim ws As Worksheet
    Dim reportWs As Worksheet
    Dim dataRange As Range
    Dim chartObj As ChartObject
    Dim reportTitle As String
    Dim chartType As String
    
    ' Configuration
    reportTitle = "{{reportTitle}}"
    chartType = "{{chartType}}"
    
    Set ws = ActiveSheet
    Set dataRange = ws.Range("{{dataRange}}")
    
    ' Create new report sheet
    Set reportWs = Worksheets.Add
    reportWs.Name = "Report_" & Format(Now, "yyyymmdd_hhmmss")
    
    ' Add report title
    reportWs.Range("A1").Value = reportTitle
    reportWs.Range("A1").Font.Bold = True
    reportWs.Range("A1").Font.Size = 18
    
    ' Copy data summary
    reportWs.Range("A3").Value = "Data Summary:"
    reportWs.Range("A4").Value = "Total Records: " & (dataRange.Rows.Count - 1)
    reportWs.Range("A5").Value = "Generated: " & Now
    
    ' Create chart
    Set chartObj = reportWs.ChartObjects.Add(Left:=50, Top:=150, Width:=400, Height:=300)
    
    With chartObj.Chart
        .SetSourceData Source:=dataRange
        Select Case chartType
            Case "Column"
                .ChartType = xlColumnClustered
            Case "Pie"
                .ChartType = xlPie
            Case "Line"
                .ChartType = xlLine
            Case "Bar"
                .ChartType = xlBarClustered
        End Select
        .HasTitle = True
        .ChartTitle.Text = reportTitle
    End With
    
    ' Auto-fit and format
    reportWs.Columns("A:D").AutoFit
    
    MsgBox "Laporan berhasil dibuat di sheet " & reportWs.Name
End Sub`
    },
    {
      id: 'data-cleaner',
      name: 'Data Cleaner & Formatter',
      description: 'Bersihkan dan format data secara otomatis',
      category: 'Utilities',
      fields: [
        { name: 'targetRange', type: 'text', description: 'Range Target (contoh: A:A)' },
        { name: 'cleaningOptions', type: 'textarea', description: 'Opsi Cleaning (satu per baris)' }
      ],
      code: `Sub CleanAndFormatData()
    Dim ws As Worksheet
    Dim targetRange As Range
    Dim cell As Range
    Dim cleaningOptions As Variant
    Dim i As Integer
    
    Set ws = ActiveSheet
    Set targetRange = ws.Range("{{targetRange}}")
    cleaningOptions = Split("{{cleaningOptions}}", vbCrLf)
    
    Application.ScreenUpdating = False
    
    For Each cell In targetRange.Cells
        If Not IsEmpty(cell.Value) Then
            For i = 0 To UBound(cleaningOptions)
                Select Case Trim(cleaningOptions(i))
                    Case "Remove Extra Spaces"
                        cell.Value = Application.Trim(cell.Value)
                    Case "Proper Case"
                        cell.Value = Application.Proper(cell.Value)
                    Case "Upper Case"
                        cell.Value = UCase(cell.Value)
                    Case "Lower Case"
                        cell.Value = LCase(cell.Value)
                    Case "Remove Numbers"
                        cell.Value = RegexReplace(cell.Value, "[0-9]", "")
                    Case "Remove Special Characters"
                        cell.Value = RegexReplace(cell.Value, "[^a-zA-Z0-9 ]", "")
                End Select
            Next i
        End If
    Next cell
    
    Application.ScreenUpdating = True
    
    MsgBox "Data cleaning selesai untuk range " & targetRange.Address
End Sub

Function RegexReplace(text As String, pattern As String, replacement As String) As String
    Dim regex As Object
    Set regex = CreateObject("VBScript.RegExp")
    regex.pattern = pattern
    regex.Global = True
    RegexReplace = regex.Replace(text, replacement)
End Function`
    },
    {
      id: 'email-automation',
      name: 'Email Automation',
      description: 'Kirim email otomatis dari data Excel',
      category: 'Automation',
      fields: [
        { name: 'emailColumn', type: 'text', description: 'Kolom Email (contoh: B)' },
        { name: 'nameColumn', type: 'text', description: 'Kolom Nama (contoh: A)' },
        { name: 'subject', type: 'text', description: 'Subject Email' },
        { name: 'bodyTemplate', type: 'textarea', description: 'Template Body Email' }
      ],
      code: `Sub SendBulkEmails()
    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim emailAddress As String
    Dim recipientName As String
    Dim emailSubject As String
    Dim emailBody As String
    Dim outlookApp As Object
    Dim mailItem As Object
    
    Set ws = ActiveSheet
    lastRow = ws.Cells(ws.Rows.Count, "{{emailColumn}}").End(xlUp).Row
    
    ' Create Outlook application
    Set outlookApp = CreateObject("Outlook.Application")
    
    emailSubject = "{{subject}}"
    
    For i = 2 To lastRow ' Assuming row 1 is header
        emailAddress = ws.Cells(i, "{{emailColumn}}").Value
        recipientName = ws.Cells(i, "{{nameColumn}}").Value
        
        If emailAddress <> "" Then
            ' Customize email body
            emailBody = Replace("{{bodyTemplate}}", "[NAMA]", recipientName)
            emailBody = Replace(emailBody, "[EMAIL]", emailAddress)
            
            ' Create mail item
            Set mailItem = outlookApp.CreateItem(0) ' olMailItem
            
            With mailItem
                .To = emailAddress
                .Subject = emailSubject
                .Body = emailBody
                .Send ' Use .Display instead of .Send to review before sending
            End With
            
            ' Add small delay
            Application.Wait (Now + TimeValue("0:00:01"))
        End If
    Next i
    
    MsgBox "Email berhasil dikirim ke " & (lastRow - 1) & " penerima"
End Sub`
    }
  ];

  const validationTypes = [
    { value: 'List', label: 'Dropdown List' },
    { value: 'Number', label: 'Number Only' },
    { value: 'Date', label: 'Date Only' },
    { value: 'Email', label: 'Email Format' }
  ];

  const chartTypes = [
    { value: 'Column', label: 'Column Chart' },
    { value: 'Pie', label: 'Pie Chart' },
    { value: 'Line', label: 'Line Chart' },
    { value: 'Bar', label: 'Bar Chart' }
  ];

  const generateVBACode = () => {
    const template = vbaTemplates.find(t => t.id === selectedTemplate);
    if (!template) {
      toast.error('Pilih template terlebih dahulu!');
      return;
    }

    setIsGenerating(true);

    setTimeout(() => {
      let code = template.code;
      
      // Replace placeholders with user input
      template.fields.forEach(field => {
        const value = customFields[field.name] || '';
        const placeholder = `{{${field.name}}}`;
        code = code.replace(new RegExp(placeholder, 'g'), value);
      });

      setGeneratedCode(code);
      setIsGenerating(false);
      toast.success('VBA Code berhasil digenerate!');
    }, 1000);
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedCode);
    toast.success('Code berhasil disalin ke clipboard!');
  };

  const downloadVBAFile = () => {
    const template = vbaTemplates.find(t => t.id === selectedTemplate);
    const fileName = `${template?.name.replace(/\s+/g, '_') || 'VBA_Code'}.bas`;
    
    const blob = new Blob([generatedCode], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
    URL.revokeObjectURL(url);
    toast.success('File VBA berhasil didownload!');
  };

  const handleFieldChange = (fieldName: string, value: string) => {
    setCustomFields(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  const selectedTemplateData = vbaTemplates.find(t => t.id === selectedTemplate);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            📊 Excel VBA Generator & Automation
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Template Selection */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Pilih Template VBA:</label>
            <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih template VBA..." />
              </SelectTrigger>
              <SelectContent>
                {vbaTemplates.map((template) => (
                  <SelectItem key={template.id} value={template.id}>
                    <div>
                      <div className="font-medium">{template.name}</div>
                      <div className="text-xs text-gray-500">{template.description}</div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Template Configuration */}
          {selectedTemplateData && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Konfigurasi Template:</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {selectedTemplateData.fields.map((field) => (
                  <div key={field.name} className="space-y-2">
                    <label className="text-sm font-medium">{field.description}:</label>
                    {field.type === 'textarea' ? (
                      <Textarea
                        value={customFields[field.name] || ''}
                        onChange={(e) => handleFieldChange(field.name, e.target.value)}
                        placeholder={`Masukkan ${field.description.toLowerCase()}...`}
                        rows={3}
                      />
                    ) : field.type === 'select' ? (
                      <Select 
                        value={customFields[field.name] || ''} 
                        onValueChange={(value) => handleFieldChange(field.name, value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={`Pilih ${field.description.toLowerCase()}...`} />
                        </SelectTrigger>
                        <SelectContent>
                          {field.name === 'validationType' && validationTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                          {field.name === 'chartType' && chartTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    ) : (
                      <Input
                        value={customFields[field.name] || ''}
                        onChange={(e) => handleFieldChange(field.name, e.target.value)}
                        placeholder={`Masukkan ${field.description.toLowerCase()}...`}
                      />
                    )}
                  </div>
                ))}
              </div>

              <Button onClick={generateVBACode} disabled={isGenerating} className="w-full">
                {isGenerating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Generating VBA Code...
                  </>
                ) : (
                  <>
                    <Code className="w-4 h-4 mr-2" />
                    Generate VBA Code
                  </>
                )}
              </Button>
            </div>
          )}

          {/* Generated Code */}
          {generatedCode && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Generated VBA Code:</h3>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={copyToClipboard}>
                    <Copy className="w-4 h-4 mr-2" />
                    Copy
                  </Button>
                  <Button variant="outline" size="sm" onClick={downloadVBAFile}>
                    <Download className="w-4 h-4 mr-2" />
                    Download .bas
                  </Button>
                </div>
              </div>
              
              <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm overflow-x-auto">
                <pre>{generatedCode}</pre>
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">📋 Cara Menggunakan VBA Code:</h4>
            <ol className="text-sm text-blue-700 space-y-1 list-decimal list-inside">
              <li>Buka Microsoft Excel</li>
              <li>Tekan <kbd>Alt + F11</kbd> untuk membuka VBA Editor</li>
              <li>Klik <strong>Insert → Module</strong> untuk membuat module baru</li>
              <li>Copy-paste code yang sudah digenerate</li>
              <li>Tekan <kbd>F5</kbd> atau klik <strong>Run</strong> untuk menjalankan macro</li>
              <li>Kembali ke Excel untuk melihat hasilnya</li>
            </ol>
          </div>

          {/* Template Categories */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {['Forms', 'Validation', 'Reports', 'Utilities', 'Automation'].map((category) => {
              const categoryTemplates = vbaTemplates.filter(t => t.category === category);
              return (
                <Card key={category} className="p-4">
                  <h4 className="font-semibold mb-2">{category}</h4>
                  <div className="space-y-2">
                    {categoryTemplates.map((template) => (
                      <div key={template.id} className="text-sm">
                        <div className="font-medium">{template.name}</div>
                        <div className="text-gray-600 text-xs">{template.description}</div>
                      </div>
                    ))}
                  </div>
                </Card>
              );
            })}
          </div>

          {/* Info */}
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-semibold text-green-800 mb-2">ℹ️ Excel VBA Features:</h4>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• <strong>Form Input Generator</strong>: Buat form input data dengan validasi</li>
              <li>• <strong>Data Validation</strong>: Setup validasi otomatis (dropdown, number, date, email)</li>
              <li>• <strong>Auto Reports</strong>: Generate laporan dengan chart otomatis</li>
              <li>• <strong>Data Cleaner</strong>: Bersihkan dan format data secara batch</li>
              <li>• <strong>Email Automation</strong>: Kirim email bulk dari data Excel</li>
              <li>• <strong>Custom Templates</strong>: Template yang bisa dikustomisasi sesuai kebutuhan</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

@echo off
echo Cleaning KIKAZE-AI build directory...

REM Kill any running Electron processes
taskkill /F /IM "KIKAZE-AI.exe" 2>nul
taskkill /F /IM "electron.exe" 2>nul
taskkill /F /IM "node.exe" 2>nul

REM Wait a moment for processes to terminate
timeout /t 3 /nobreak >nul

REM Try to remove the build directory multiple times
for /L %%i in (1,1,5) do (
    if exist "build-output" (
        echo Attempt %%i: Removing build-output directory...
        rmdir /s /q "build-output" 2>nul
        if not exist "build-output" (
            echo Build directory cleaned successfully!
            goto :success
        )
        timeout /t 2 /nobreak >nul
    ) else (
        echo Build directory does not exist.
        goto :success
    )
)

REM If still exists, try PowerShell approach
if exist "build-output" (
    echo Using PowerShell to force remove...
    powershell -Command "Get-ChildItem -Path 'build-output' -Recurse | Remove-Item -Force -Recurse -ErrorAction SilentlyContinue"
    powershell -Command "Remove-Item -Path 'build-output' -Force -Recurse -ErrorAction SilentlyContinue"
)

:success
echo Cleanup completed.

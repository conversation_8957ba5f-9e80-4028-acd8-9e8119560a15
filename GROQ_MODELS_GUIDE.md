# 🚀 Panduan Model Groq API

## 📊 **Model yang Digunakan Saat Ini**

### **llama-3.1-8b-instant**
- ✅ **Free Tier Friendly**: Limit token lebih tinggi
- ⚡ **Cepat**: Response time sangat cepat
- 💰 **Hemat**: Konsumsi token lebih sedikit
- 🎯 **Cocok untuk**: Chat umum, coding assistance, Q&A

## 🔄 **Model Alternatif Groq (Free Tier)**

### 1. **llama-3.1-8b-instant** (Saat ini digunakan)
- **<PERSON><PERSON><PERSON>han**: Sangat cepat, hemat token
- **Limit**: ~100K tokens/hari
- **Best for**: Chat sehari-hari, coding

### 2. **mixtral-8x7b-32768**
- **Ke<PERSON>bihan**: Context window besar (32K tokens)
- **Limit**: Lebih rendah dari llama-3.1-8b
- **Best for**: Analisis dokumen panjang

### 3. **llama3-8b-8192**
- **<PERSON><PERSON><PERSON><PERSON>**: Balance antara kualitas dan kecepatan
- **Limit**: Standar free tier
- **Best for**: General purpose

### 4. **gemma-7b-it**
- **Kelebihan**: Model Google, cukup akurat
- **Limit**: Standar free tier
- **Best for**: Instruction following

## ⚠️ **Model yang Dihindari (Limit Ketat)**

### ❌ **llama-3.3-70b-versatile**
- **Masalah**: Limit sangat ketat (100K tokens/hari)
- **Error**: Request too large
- **Solusi**: Gunakan model 8B

### ❌ **llama-3.1-70b-versatile**
- **Masalah**: Deprecated/decommissioned
- **Status**: Tidak lagi didukung

## 🛠️ **Optimasi Token Usage**

### 1. **Context Limiting**
```typescript
// Batasi ke 10 pesan terakhir
messages.slice(-10).map(({ role, content }) => ({ 
  role, 
  content: content.length > 2000 ? content.substring(0, 2000) + "..." : content 
}))
```

### 2. **Max Tokens Reduction**
```typescript
max_tokens: 1024, // Dari 2048 ke 1024
```

### 3. **Smart Message Filtering**
- Hapus pesan lama secara otomatis
- Compress konten panjang
- Skip system messages yang tidak perlu

## 📈 **Tips Menghemat Token**

### 💡 **Best Practices**
1. **Hapus History**: Gunakan tombol "Hapus History" secara berkala
2. **Pesan Singkat**: Buat pertanyaan yang concise
3. **Avoid Long Context**: Jangan upload file terlalu besar
4. **Use Tools**: Gunakan Developer Tools untuk task khusus

### 🔄 **Auto-Optimization**
- Context otomatis dibatasi ke 10 pesan terakhir
- Pesan panjang dipotong otomatis
- Max tokens dikurangi untuk efisiensi

## 🚨 **Error Handling**

### **413 Request Too Large**
```
Error: Request too large for model
```
**Solusi:**
1. Hapus history chat
2. Kurangi panjang pesan
3. Gunakan model 8B

### **429 Rate Limit Exceeded**
```
Error: Rate limit exceeded
```
**Solusi:**
1. Tunggu beberapa menit
2. Upgrade ke Dev Tier
3. Gunakan model yang lebih ringan

## 🎯 **Rekomendasi Model per Use Case**

### 💬 **Chat Umum**
- **Model**: `llama-3.1-8b-instant`
- **Alasan**: Cepat, hemat token

### 📄 **Analisis Dokumen**
- **Model**: `mixtral-8x7b-32768`
- **Alasan**: Context window besar

### 💻 **Coding Assistant**
- **Model**: `llama-3.1-8b-instant`
- **Alasan**: Good balance untuk code

### 🖼️ **Image Analysis**
- **Model**: `llama3-8b-8192`
- **Alasan**: Good vision capabilities

## 🔧 **Cara Ganti Model**

### Manual Change (Developer)
```typescript
// Di src/pages/Chat.tsx line ~161
model: "llama-3.1-8b-instant", // Ganti model di sini
```

### Available Models
```typescript
const GROQ_MODELS = [
  "llama-3.1-8b-instant",      // Recommended
  "mixtral-8x7b-32768",        // For long context
  "llama3-8b-8192",           // Balanced
  "gemma-7b-it",              // Google model
];
```

## 📊 **Monitoring Usage**

### Check Token Usage
- Monitor di Groq Console
- Track daily limits
- Set up alerts

### Optimize Based on Usage
- Jika sering hit limit → gunakan model 8B
- Jika butuh kualitas tinggi → upgrade tier
- Jika butuh context panjang → gunakan mixtral

---

**💡 Tip**: Untuk penggunaan optimal, gunakan `llama-3.1-8b-instant` untuk chat sehari-hari dan `mixtral-8x7b-32768` hanya untuk analisis dokumen panjang.

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>KIKAZE-AI Assistant</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      width: 380px;
      min-height: 500px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
    }

    .header {
      padding: 20px;
      text-align: center;
      background: rgba(255,255,255,0.1);
      backdrop-filter: blur(10px);
    }

    .logo {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 8px;
    }

    .tagline {
      font-size: 14px;
      opacity: 0.9;
    }

    .content {
      padding: 20px;
    }

    .quick-actions {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;
      margin-bottom: 20px;
    }

    .action-btn {
      background: rgba(255,255,255,0.2);
      border: 1px solid rgba(255,255,255,0.3);
      border-radius: 8px;
      padding: 16px 12px;
      color: white;
      text-decoration: none;
      text-align: center;
      transition: all 0.2s;
      cursor: pointer;
      font-size: 12px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
    }

    .action-btn:hover {
      background: rgba(255,255,255,0.3);
      transform: translateY(-2px);
    }

    .action-icon {
      font-size: 20px;
    }

    .current-page {
      background: rgba(255,255,255,0.1);
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 20px;
    }

    .page-title {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 8px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .page-url {
      font-size: 12px;
      opacity: 0.7;
      word-break: break-all;
    }

    .page-actions {
      display: flex;
      gap: 8px;
      margin-top: 12px;
    }

    .page-btn {
      flex: 1;
      background: rgba(255,255,255,0.2);
      border: 1px solid rgba(255,255,255,0.3);
      border-radius: 6px;
      padding: 8px;
      color: white;
      font-size: 11px;
      cursor: pointer;
      transition: all 0.2s;
    }

    .page-btn:hover {
      background: rgba(255,255,255,0.3);
    }

    .footer {
      padding: 16px 20px;
      border-top: 1px solid rgba(255,255,255,0.2);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .open-app-btn {
      background: white;
      color: #667eea;
      border: none;
      border-radius: 6px;
      padding: 10px 20px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s;
      font-size: 14px;
    }

    .open-app-btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .shortcuts {
      font-size: 11px;
      opacity: 0.7;
    }

    .loading {
      display: none;
      text-align: center;
      padding: 20px;
    }

    .spinner {
      width: 30px;
      height: 30px;
      border: 3px solid rgba(255,255,255,0.3);
      border-top: 3px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 12px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .result {
      display: none;
      background: rgba(255,255,255,0.1);
      border-radius: 8px;
      padding: 16px;
      margin-top: 16px;
      font-size: 13px;
      line-height: 1.4;
      max-height: 200px;
      overflow-y: auto;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="logo">🤖 KIKAZE-AI</div>
    <div class="tagline">Your AI-Powered Browser Assistant</div>
  </div>

  <div class="content">
    <div class="quick-actions">
      <div class="action-btn" id="explain-page">
        <div class="action-icon">🧠</div>
        <div>Explain Page</div>
      </div>
      <div class="action-btn" id="summarize-page">
        <div class="action-icon">📄</div>
        <div>Summarize</div>
      </div>
      <div class="action-btn" id="extract-data">
        <div class="action-icon">📊</div>
        <div>Extract Data</div>
      </div>
      <div class="action-btn" id="translate-page">
        <div class="action-icon">🌐</div>
        <div>Translate</div>
      </div>
    </div>

    <div class="current-page">
      <div class="page-title" id="page-title">Loading...</div>
      <div class="page-url" id="page-url">Loading...</div>
      <div class="page-actions">
        <button class="page-btn" id="analyze-seo">🔍 SEO Analysis</button>
        <button class="page-btn" id="check-accessibility">♿ Accessibility</button>
      </div>
    </div>

    <div class="loading" id="loading">
      <div class="spinner"></div>
      <div>Processing with AI...</div>
    </div>

    <div class="result" id="result"></div>
  </div>

  <div class="footer">
    <div class="shortcuts">
      Ctrl+Shift+K
    </div>
    <button class="open-app-btn" id="open-app">
      🚀 Open Full App
    </button>
  </div>

  <script src="popup.js"></script>
</body>
</html>

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { toast } from 'sonner';
import { Upload, Download, Trash2, RefreshCw } from 'lucide-react';

interface ConvertedImage {
  id: string;
  originalFile: File;
  convertedBlob: Blob;
  originalFormat: string;
  targetFormat: string;
  originalSize: number;
  convertedSize: number;
  originalUrl: string;
  convertedUrl: string;
}

export const FormatConverter: React.FC = () => {
  const [images, setImages] = useState<ConvertedImage[]>([]);
  const [isConverting, setIsConverting] = useState(false);
  const [targetFormat, setTargetFormat] = useState('png');
  const [quality, setQuality] = useState([0.9]);

  const supportedFormats = [
    { value: 'png', label: 'PNG', description: 'Lossless, support transparency' },
    { value: 'jpeg', label: 'JPEG', description: 'Lossy, smaller file size' },
    { value: 'webp', label: 'WebP', description: 'Modern format, best compression' },
    { value: 'bmp', label: 'BMP', description: 'Uncompressed bitmap' },
    { value: 'ico', label: 'ICO', description: 'Icon format' }
  ];

  const convertImage = (file: File, format: string, quality: number): Promise<Blob> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        
        if (!ctx) {
          reject(new Error('Canvas context not available'));
          return;
        }

        // For formats that don't support transparency, fill with white background
        if (format === 'jpeg' || format === 'bmp') {
          ctx.fillStyle = '#FFFFFF';
          ctx.fillRect(0, 0, canvas.width, canvas.height);
        }

        ctx.drawImage(img, 0, 0);

        canvas.toBlob(
          (blob) => {
            if (blob) {
              resolve(blob);
            } else {
              reject(new Error('Failed to convert image'));
            }
          },
          `image/${format}`,
          format === 'jpeg' ? quality : undefined
        );
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    setIsConverting(true);

    for (const file of files) {
      if (!file.type.startsWith('image/')) {
        toast.error(`${file.name} bukan file gambar!`);
        continue;
      }

      try {
        const originalFormat = file.type.split('/')[1];
        
        // Skip if already in target format
        if (originalFormat === targetFormat) {
          toast.warning(`${file.name} sudah dalam format ${targetFormat.toUpperCase()}`);
          continue;
        }

        const convertedBlob = await convertImage(file, targetFormat, quality[0]);
        
        const originalUrl = URL.createObjectURL(file);
        const convertedUrl = URL.createObjectURL(convertedBlob);

        const newImage: ConvertedImage = {
          id: Date.now().toString() + Math.random(),
          originalFile: file,
          convertedBlob,
          originalFormat,
          targetFormat,
          originalSize: file.size,
          convertedSize: convertedBlob.size,
          originalUrl,
          convertedUrl
        };

        setImages(prev => [...prev, newImage]);
        toast.success(`${file.name} berhasil dikonversi ke ${targetFormat.toUpperCase()}!`);
      } catch (error) {
        console.error('Conversion error:', error);
        toast.error(`Gagal konversi ${file.name}`);
      }
    }

    setIsConverting(false);
    event.target.value = '';
  };

  const downloadImage = (image: ConvertedImage) => {
    const link = document.createElement('a');
    link.href = image.convertedUrl;
    const fileName = image.originalFile.name.replace(/\.[^/.]+$/, '') + '.' + image.targetFormat;
    link.download = fileName;
    link.click();
    toast.success('Gambar berhasil didownload!');
  };

  const downloadAll = () => {
    images.forEach((image, index) => {
      setTimeout(() => {
        downloadImage(image);
      }, index * 500);
    });
    toast.success(`${images.length} gambar akan didownload!`);
  };

  const removeImage = (id: string) => {
    setImages(prev => {
      const imageToRemove = prev.find(img => img.id === id);
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.originalUrl);
        URL.revokeObjectURL(imageToRemove.convertedUrl);
      }
      return prev.filter(img => img.id !== id);
    });
    toast.success('Gambar berhasil dihapus!');
  };

  const clearAll = () => {
    images.forEach(image => {
      URL.revokeObjectURL(image.originalUrl);
      URL.revokeObjectURL(image.convertedUrl);
    });
    setImages([]);
    toast.success('Semua gambar berhasil dihapus!');
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFormatInfo = (format: string) => {
    return supportedFormats.find(f => f.value === format) || supportedFormats[0];
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔄 Image Format Converter
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Target Format:</label>
              <Select value={targetFormat} onValueChange={setTargetFormat}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {supportedFormats.map((format) => (
                    <SelectItem key={format.value} value={format.value}>
                      <div>
                        <div className="font-medium">{format.label}</div>
                        <div className="text-xs text-gray-500">{format.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {(targetFormat === 'jpeg' || targetFormat === 'webp') && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Quality: {(quality[0] * 100).toFixed(0)}%</label>
                <Slider
                  value={quality}
                  onValueChange={setQuality}
                  max={1}
                  min={0.1}
                  step={0.1}
                  className="w-full"
                />
                <p className="text-xs text-gray-500">
                  Higher quality = larger file size
                </p>
              </div>
            )}
          </div>

          {/* Format Info */}
          <div className="p-4 bg-blue-50 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">
              📋 {getFormatInfo(targetFormat).label} Format Info:
            </h4>
            <p className="text-sm text-blue-700">{getFormatInfo(targetFormat).description}</p>
            <div className="mt-2 text-xs text-blue-600">
              {targetFormat === 'png' && '• Best for: Images with transparency, logos, screenshots'}
              {targetFormat === 'jpeg' && '• Best for: Photos, images without transparency'}
              {targetFormat === 'webp' && '• Best for: Web images, modern browsers, best compression'}
              {targetFormat === 'bmp' && '• Best for: Simple graphics, compatibility with old software'}
              {targetFormat === 'ico' && '• Best for: Website favicons, application icons'}
            </div>
          </div>

          {/* Upload Area */}
          <div className="space-y-4">
            <label className="cursor-pointer">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors">
                <Upload className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <p className="text-lg font-medium text-gray-700">
                  {isConverting ? 'Mengkonversi gambar...' : 'Klik atau drag gambar ke sini'}
                </p>
                <p className="text-sm text-gray-500">
                  Support: JPG, PNG, WebP, GIF, BMP (Multiple files)
                </p>
                <p className="text-xs text-gray-400 mt-2">
                  Convert to: {getFormatInfo(targetFormat).label}
                </p>
                {isConverting && (
                  <div className="mt-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  </div>
                )}
              </div>
              <Input
                type="file"
                multiple
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
                disabled={isConverting}
              />
            </label>
          </div>

          {/* Quick Actions */}
          <div className="flex gap-2 flex-wrap">
            <Button variant="outline" size="sm" onClick={() => setTargetFormat('png')}>
              → PNG
            </Button>
            <Button variant="outline" size="sm" onClick={() => setTargetFormat('jpeg')}>
              → JPEG
            </Button>
            <Button variant="outline" size="sm" onClick={() => setTargetFormat('webp')}>
              → WebP
            </Button>
            <Button variant="outline" size="sm" onClick={() => setTargetFormat('ico')}>
              → ICO
            </Button>
          </div>

          {/* Action Buttons */}
          {images.length > 0 && (
            <div className="flex gap-2">
              <Button onClick={downloadAll}>
                <Download className="w-4 h-4 mr-2" />
                Download All
              </Button>
              <Button variant="outline" onClick={clearAll}>
                <Trash2 className="w-4 h-4 mr-2" />
                Clear All
              </Button>
            </div>
          )}

          {/* Results */}
          {images.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Converted Images:</h3>
              <div className="grid gap-4">
                {images.map((image) => (
                  <Card key={image.id} className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                      {/* Original Image */}
                      <div className="text-center">
                        <img 
                          src={image.originalUrl} 
                          alt="Original" 
                          className="w-20 h-20 object-cover rounded mx-auto mb-2"
                        />
                        <p className="text-xs text-gray-600">Original ({image.originalFormat.toUpperCase()})</p>
                        <p className="text-xs font-medium">{formatFileSize(image.originalSize)}</p>
                      </div>

                      {/* Arrow */}
                      <div className="text-center">
                        <RefreshCw className="w-8 h-8 mx-auto text-blue-500" />
                        <p className="text-xs text-gray-500 mt-1">Converted</p>
                      </div>

                      {/* Converted Image */}
                      <div className="text-center">
                        <img 
                          src={image.convertedUrl} 
                          alt="Converted" 
                          className="w-20 h-20 object-cover rounded mx-auto mb-2"
                        />
                        <p className="text-xs text-gray-600">New ({image.targetFormat.toUpperCase()})</p>
                        <p className="text-xs font-medium">{formatFileSize(image.convertedSize)}</p>
                      </div>

                      {/* Actions */}
                      <div className="flex gap-2 justify-center">
                        <Button size="sm" onClick={() => downloadImage(image)}>
                          <Download className="w-4 h-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => removeImage(image.id)}
                          className="text-red-600"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="mt-2 text-center">
                      <p className="text-sm font-medium truncate">{image.originalFile.name}</p>
                      <p className="text-xs text-gray-500">
                        Size change: {image.convertedSize > image.originalSize ? '+' : ''}
                        {(((image.convertedSize - image.originalSize) / image.originalSize) * 100).toFixed(1)}%
                      </p>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Info */}
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-semibold text-green-800 mb-2">ℹ️ Fitur Format Converter:</h4>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• <strong>Multiple Formats</strong>: PNG, JPEG, WebP, BMP, ICO</li>
              <li>• <strong>Batch Conversion</strong>: Convert multiple images at once</li>
              <li>• <strong>Quality Control</strong>: Adjust quality for lossy formats</li>
              <li>• <strong>Transparency Support</strong>: Maintains transparency where supported</li>
              <li>• <strong>Client-side Processing</strong>: No upload to servers</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

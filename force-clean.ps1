# KIKAZE-AI Force Clean Script
Write-Host "🔧 KIKAZE-AI Force Clean & Rebuild" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Function to kill processes safely
function Stop-ProcessSafely {
    param($ProcessName)
    try {
        $processes = Get-Process -Name $ProcessName -ErrorAction SilentlyContinue
        if ($processes) {
            Write-Host "🔪 Killing $ProcessName processes..." -ForegroundColor Yellow
            $processes | ForEach-Object { 
                try {
                    $_.Kill()
                    $_.WaitForExit(5000)
                } catch {
                    Write-Host "   Force killing $_..." -ForegroundColor Red
                    Stop-Process -Id $_.Id -Force -ErrorAction SilentlyContinue
                }
            }
            Write-Host "✅ $ProcessName processes killed" -ForegroundColor Green
        } else {
            Write-Host "ℹ️  No $ProcessName processes found" -ForegroundColor Gray
        }
    } catch {
        Write-Host "⚠️  Error killing $ProcessName : $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Function to remove directory with retry
function Remove-DirectoryForce {
    param($Path)
    if (Test-Path $Path) {
        Write-Host "🗑️  Removing $Path..." -ForegroundColor Yellow
        for ($i = 1; $i -le 5; $i++) {
            try {
                Remove-Item -Path $Path -Recurse -Force -ErrorAction Stop
                Write-Host "✅ $Path removed successfully" -ForegroundColor Green
                return
            } catch {
                Write-Host "   Attempt $i failed: $($_.Exception.Message)" -ForegroundColor Red
                if ($i -lt 5) {
                    Write-Host "   Waiting 2 seconds before retry..." -ForegroundColor Yellow
                    Start-Sleep -Seconds 2
                }
            }
        }
        Write-Host "❌ Failed to remove $Path after 5 attempts" -ForegroundColor Red
    } else {
        Write-Host "ℹ️  $Path doesn't exist" -ForegroundColor Gray
    }
}

# Step 1: Kill all related processes
Write-Host "`n1️⃣  Killing related processes..." -ForegroundColor Cyan
Stop-ProcessSafely "electron"
Stop-ProcessSafely "KIKAZE-AI"
Stop-ProcessSafely "node"

# Step 2: Wait a moment
Write-Host "`n2️⃣  Waiting for processes to fully terminate..." -ForegroundColor Cyan
Start-Sleep -Seconds 3

# Step 3: Remove build directories
Write-Host "`n3️⃣  Removing build directories..." -ForegroundColor Cyan
Remove-DirectoryForce "dist-electron"
Remove-DirectoryForce "dist"

# Step 4: Clear npm cache
Write-Host "`n4️⃣  Clearing npm cache..." -ForegroundColor Cyan
try {
    npm cache clean --force
    Write-Host "✅ NPM cache cleared" -ForegroundColor Green
} catch {
    Write-Host "⚠️  NPM cache clean failed: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Step 5: Rebuild
Write-Host "`n5️⃣  Starting fresh build..." -ForegroundColor Cyan
try {
    Write-Host "   Building React app..." -ForegroundColor Yellow
    npm run build
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ React build successful" -ForegroundColor Green
        
        Write-Host "   Building Electron app..." -ForegroundColor Yellow
        npm run dist-win
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Electron build successful" -ForegroundColor Green
            Write-Host "`n🎉 BUILD COMPLETED SUCCESSFULLY!" -ForegroundColor Green
            Write-Host "📁 Check dist-electron folder for installer" -ForegroundColor Cyan
        } else {
            Write-Host "❌ Electron build failed" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ React build failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Build error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🏁 Script completed!" -ForegroundColor Cyan
Read-Host "Press Enter to exit"


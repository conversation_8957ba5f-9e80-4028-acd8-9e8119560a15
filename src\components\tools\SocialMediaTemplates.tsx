import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { Users, Download, Copy, RefreshCw, Sparkles, Image, Type, Palette, Upload } from 'lucide-react';

interface SocialPlatform {
  id: string;
  name: string;
  icon: string;
  templates: TemplateSize[];
}

interface TemplateSize {
  id: string;
  name: string;
  width: number;
  height: number;
  description: string;
}

interface DesignTemplate {
  id: string;
  name: string;
  category: string;
  colors: string[];
  fontFamily: string;
  layout: string;
  description: string;
}

const socialPlatforms: SocialPlatform[] = [
  {
    id: 'instagram',
    name: 'Instagram',
    icon: '📷',
    templates: [
      { id: 'ig-post', name: 'Post (Square)', width: 1080, height: 1080, description: 'Standard Instagram post' },
      { id: 'ig-story', name: 'Story', width: 1080, height: 1920, description: 'Instagram Story format' },
      { id: 'ig-reel', name: 'Reel Cover', width: 1080, height: 1920, description: 'Reel thumbnail cover' },
      { id: 'ig-carousel', name: 'Carousel', width: 1080, height: 1080, description: 'Multi-slide post' }
    ]
  },
  {
    id: 'facebook',
    name: 'Facebook',
    icon: '👥',
    templates: [
      { id: 'fb-post', name: 'Post', width: 1200, height: 630, description: 'Facebook feed post' },
      { id: 'fb-cover', name: 'Cover Photo', width: 1640, height: 859, description: 'Profile cover image' },
      { id: 'fb-event', name: 'Event Banner', width: 1920, height: 1080, description: 'Event cover image' }
    ]
  },
  {
    id: 'twitter',
    name: 'Twitter/X',
    icon: '🐦',
    templates: [
      { id: 'tw-post', name: 'Post Image', width: 1200, height: 675, description: 'Tweet image attachment' },
      { id: 'tw-header', name: 'Header', width: 1500, height: 500, description: 'Profile header image' },
      { id: 'tw-card', name: 'Card', width: 800, height: 418, description: 'Twitter card preview' }
    ]
  },
  {
    id: 'linkedin',
    name: 'LinkedIn',
    icon: '💼',
    templates: [
      { id: 'li-post', name: 'Post', width: 1200, height: 627, description: 'LinkedIn feed post' },
      { id: 'li-banner', name: 'Company Banner', width: 1536, height: 768, description: 'Company page banner' },
      { id: 'li-article', name: 'Article Header', width: 1200, height: 627, description: 'Article cover image' }
    ]
  },
  {
    id: 'youtube',
    name: 'YouTube',
    icon: '🎥',
    templates: [
      { id: 'yt-thumbnail', name: 'Thumbnail', width: 1280, height: 720, description: 'Video thumbnail' },
      { id: 'yt-channel', name: 'Channel Art', width: 2560, height: 1440, description: 'Channel banner' },
      { id: 'yt-end', name: 'End Screen', width: 1280, height: 720, description: 'Video end screen' }
    ]
  },
  {
    id: 'tiktok',
    name: 'TikTok',
    icon: '🎵',
    templates: [
      { id: 'tt-cover', name: 'Video Cover', width: 1080, height: 1920, description: 'Video thumbnail' },
      { id: 'tt-profile', name: 'Profile Picture', width: 200, height: 200, description: 'Profile avatar' }
    ]
  }
];

const designTemplates: DesignTemplate[] = [
  {
    id: 'business-clean',
    name: 'Business Clean',
    category: 'Business',
    colors: ['#2563eb', '#ffffff', '#f8fafc', '#64748b'],
    fontFamily: 'Inter, sans-serif',
    layout: 'centered',
    description: 'Professional dan clean untuk business'
  },
  {
    id: 'creative-vibrant',
    name: 'Creative Vibrant',
    category: 'Creative',
    colors: ['#f59e0b', '#ef4444', '#8b5cf6', '#ffffff'],
    fontFamily: 'Poppins, sans-serif',
    layout: 'dynamic',
    description: 'Colorful dan energetic untuk creative brands'
  },
  {
    id: 'minimal-elegant',
    name: 'Minimal Elegant',
    category: 'Minimalist',
    colors: ['#000000', '#ffffff', '#f5f5f5', '#9ca3af'],
    fontFamily: 'Helvetica, sans-serif',
    layout: 'minimal',
    description: 'Simple dan elegant untuk luxury brands'
  },
  {
    id: 'bold-impact',
    name: 'Bold Impact',
    category: 'Bold',
    colors: ['#dc2626', '#000000', '#ffffff', '#fbbf24'],
    fontFamily: 'Roboto, sans-serif',
    layout: 'bold',
    description: 'High-contrast untuk maximum impact'
  },
  {
    id: 'trendy-modern',
    name: 'Trendy Modern',
    category: 'Trendy',
    colors: ['#06b6d4', '#8b5cf6', '#f59e0b', '#ffffff'],
    fontFamily: 'Nunito, sans-serif',
    layout: 'modern',
    description: 'Current trends dan modern aesthetics'
  },
  {
    id: 'vintage-retro',
    name: 'Vintage Retro',
    category: 'Vintage',
    colors: ['#92400e', '#fbbf24', '#f3f4f6', '#374151'],
    fontFamily: 'Georgia, serif',
    layout: 'vintage',
    description: 'Retro vibes dengan classic appeal'
  }
];

export const SocialMediaTemplates: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [selectedPlatform, setSelectedPlatform] = useState<SocialPlatform>(socialPlatforms[0]);
  const [selectedTemplate, setSelectedTemplate] = useState<TemplateSize>(socialPlatforms[0].templates[0]);
  const [selectedDesign, setSelectedDesign] = useState<DesignTemplate>(designTemplates[0]);
  const [headline, setHeadline] = useState('Your Headline Here');
  const [description, setDescription] = useState('Add your description or call-to-action text here');
  const [customColors, setCustomColors] = useState<string[]>(['#2563eb', '#ffffff', '#f8fafc', '#64748b']);
  const [fontSize, setFontSize] = useState(48);
  const [isGenerating, setIsGenerating] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [backgroundImage, setBackgroundImage] = useState<string | null>(null);

  useEffect(() => {
    generateTemplate();
  }, [selectedTemplate, selectedDesign, headline, description, customColors, fontSize, backgroundImage]);

  const generateTemplate = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = selectedTemplate.width;
    canvas.height = selectedTemplate.height;

    // Scale for display (max 400px width)
    const scale = Math.min(400 / canvas.width, 300 / canvas.height);
    canvas.style.width = `${canvas.width * scale}px`;
    canvas.style.height = `${canvas.height * scale}px`;

    // Clear canvas
    ctx.fillStyle = customColors[2] || selectedDesign.colors[2];
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw background image if available
    if (backgroundImage) {
      const img = new Image();
      img.onload = () => {
        ctx.globalAlpha = 0.3;
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        ctx.globalAlpha = 1;
        drawContent();
      };
      img.src = backgroundImage;
    } else {
      drawContent();
    }

    function drawContent() {
      // Draw design elements based on layout
      drawLayoutElements(ctx);

      // Draw headline
      if (headline) {
        ctx.fillStyle = customColors[0] || selectedDesign.colors[0];
        ctx.font = `bold ${fontSize}px ${selectedDesign.fontFamily}`;
        ctx.textAlign = 'center';

        // Word wrap for headline
        const words = headline.split(' ');
        const lines = [];
        let currentLine = words[0];

        for (let i = 1; i < words.length; i++) {
          const word = words[i];
          const width = ctx.measureText(currentLine + ' ' + word).width;
          if (width < canvas.width * 0.8) {
            currentLine += ' ' + word;
          } else {
            lines.push(currentLine);
            currentLine = word;
          }
        }
        lines.push(currentLine);

        const lineHeight = fontSize * 1.2;
        const startY = canvas.height * 0.3;

        lines.forEach((line, index) => {
          ctx.fillText(line, canvas.width / 2, startY + (index * lineHeight));
        });
      }

      // Draw description
      if (description) {
        ctx.fillStyle = customColors[3] || selectedDesign.colors[3];
        ctx.font = `${fontSize * 0.5}px ${selectedDesign.fontFamily}`;
        ctx.textAlign = 'center';

        // Word wrap for description
        const words = description.split(' ');
        const lines = [];
        let currentLine = words[0];

        for (let i = 1; i < words.length; i++) {
          const word = words[i];
          const width = ctx.measureText(currentLine + ' ' + word).width;
          if (width < canvas.width * 0.7) {
            currentLine += ' ' + word;
          } else {
            lines.push(currentLine);
            currentLine = word;
          }
        }
        lines.push(currentLine);

        const lineHeight = fontSize * 0.6;
        const startY = canvas.height * 0.6;

        lines.forEach((line, index) => {
          ctx.fillText(line, canvas.width / 2, startY + (index * lineHeight));
        });
      }
    }
  };

  const drawLayoutElements = (ctx: CanvasRenderingContext2D) => {
    const { width, height } = ctx.canvas;

    switch (selectedDesign.layout) {
      case 'centered':
        // Simple centered layout with subtle background
        ctx.fillStyle = customColors[1] || selectedDesign.colors[1];
        ctx.fillRect(width * 0.1, height * 0.2, width * 0.8, height * 0.6);
        break;

      case 'dynamic':
        // Dynamic layout with geometric shapes
        ctx.fillStyle = customColors[1] || selectedDesign.colors[1];
        ctx.beginPath();
        ctx.arc(width * 0.8, height * 0.2, width * 0.15, 0, 2 * Math.PI);
        ctx.fill();

        ctx.fillStyle = customColors[2] || selectedDesign.colors[2];
        ctx.fillRect(width * 0.05, height * 0.7, width * 0.3, height * 0.25);
        break;

      case 'minimal':
        // Minimal layout with thin lines
        ctx.strokeStyle = customColors[3] || selectedDesign.colors[3];
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(width * 0.2, height * 0.5);
        ctx.lineTo(width * 0.8, height * 0.5);
        ctx.stroke();
        break;

      case 'bold':
        // Bold layout with strong geometric elements
        ctx.fillStyle = customColors[1] || selectedDesign.colors[1];
        ctx.fillRect(0, 0, width * 0.3, height);

        ctx.fillStyle = customColors[3] || selectedDesign.colors[3];
        ctx.fillRect(width * 0.7, 0, width * 0.3, height);
        break;

      case 'modern':
        // Modern layout with gradients
        const gradient = ctx.createLinearGradient(0, 0, width, height);
        gradient.addColorStop(0, customColors[1] || selectedDesign.colors[1]);
        gradient.addColorStop(1, customColors[2] || selectedDesign.colors[2]);
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, width, height);
        break;

      case 'vintage':
        // Vintage layout with decorative elements
        ctx.fillStyle = customColors[1] || selectedDesign.colors[1];
        ctx.fillRect(width * 0.1, height * 0.1, width * 0.8, height * 0.8);

        // Decorative corners
        ctx.fillStyle = customColors[3] || selectedDesign.colors[3];
        ctx.fillRect(width * 0.05, height * 0.05, width * 0.1, height * 0.1);
        ctx.fillRect(width * 0.85, height * 0.05, width * 0.1, height * 0.1);
        ctx.fillRect(width * 0.05, height * 0.85, width * 0.1, height * 0.1);
        ctx.fillRect(width * 0.85, height * 0.85, width * 0.1, height * 0.1);
        break;
    }
  };

  const handlePlatformChange = (platformId: string) => {
    const platform = socialPlatforms.find(p => p.id === platformId);
    if (platform) {
      setSelectedPlatform(platform);
      setSelectedTemplate(platform.templates[0]);
    }
  };

  const handleTemplateChange = (templateId: string) => {
    const template = selectedPlatform.templates.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(template);
    }
  };

  const handleDesignChange = (designId: string) => {
    const design = designTemplates.find(d => d.id === designId);
    if (design) {
      setSelectedDesign(design);
      setCustomColors(design.colors);
    }
  };

  const generateAIContent = () => {
    setIsGenerating(true);

    setTimeout(() => {
      const headlines = [
        'Transform Your Business Today',
        'Discover Something Amazing',
        'Join the Revolution',
        'Unlock Your Potential',
        'Create. Inspire. Succeed.',
        'Your Journey Starts Here',
        'Innovation Meets Excellence',
        'Dream Big. Achieve More.'
      ];

      const descriptions = [
        'Take your business to the next level with our innovative solutions.',
        'Experience the difference that quality makes in everything we do.',
        'Join thousands of satisfied customers who trust our expertise.',
        'Ready to make a change? Start your transformation today.',
        'Professional results that exceed your expectations every time.',
        'Discover why we\'re the preferred choice for success.',
        'Quality, innovation, and excellence in every project we deliver.',
        'Your success is our mission. Let\'s achieve it together.'
      ];

      const randomHeadline = headlines[Math.floor(Math.random() * headlines.length)];
      const randomDescription = descriptions[Math.floor(Math.random() * descriptions.length)];

      setHeadline(randomHeadline);
      setDescription(randomDescription);
      setIsGenerating(false);

      toast.success('AI content berhasil digenerate!');
    }, 2000);
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setBackgroundImage(e.target?.result as string);
        toast.success('Background image berhasil diupload!');
      };
      reader.readAsDataURL(file);
    }
  };

  const downloadTemplate = (format: 'png' | 'jpg') => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const link = document.createElement('a');
    const dataURL = canvas.toDataURL(`image/${format}`, 0.9);
    link.href = dataURL;
    link.download = `${selectedPlatform.name}-${selectedTemplate.name}-${Date.now()}.${format}`;
    link.click();

    toast.success(`Template berhasil didownload sebagai ${format.toUpperCase()}`);
  };

  const copyToClipboard = async () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    try {
      canvas.toBlob(async (blob) => {
        if (blob) {
          await navigator.clipboard.write([
            new ClipboardItem({ 'image/png': blob })
          ]);
          toast.success('Template berhasil disalin ke clipboard');
        }
      });
    } catch (error) {
      toast.error('Gagal menyalin template ke clipboard');
    }
  };

  const batchExport = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    // Create a zip-like download by generating multiple formats
    const formats = ['png', 'jpg'];
    const sizes = selectedPlatform.templates;

    let downloadCount = 0;
    const totalDownloads = formats.length * sizes.length;

    sizes.forEach((template) => {
      // Temporarily switch to this template
      const originalTemplate = selectedTemplate;
      setSelectedTemplate(template);

      setTimeout(() => {
        formats.forEach((format) => {
          setTimeout(() => {
            const link = document.createElement('a');
            const dataURL = canvas.toDataURL(`image/${format}`, 0.9);
            link.href = dataURL;
            link.download = `${selectedPlatform.name}-${template.name}-${Date.now()}.${format}`;
            link.click();

            downloadCount++;
            if (downloadCount === totalDownloads) {
              setSelectedTemplate(originalTemplate);
              toast.success(`${totalDownloads} files berhasil didownload!`);
            }
          }, downloadCount * 500);
        });
      }, 100);
    });
  };

  const applyPreset = (presetType: 'business' | 'creative' | 'minimal' | 'bold') => {
    const presets = {
      business: {
        design: designTemplates.find(d => d.id === 'business-clean'),
        headline: 'Professional Solutions',
        description: 'Elevate your business with our expert services',
        colors: ['#2563eb', '#ffffff', '#f8fafc', '#64748b']
      },
      creative: {
        design: designTemplates.find(d => d.id === 'creative-vibrant'),
        headline: 'Unleash Your Creativity',
        description: 'Express yourself with bold and vibrant designs',
        colors: ['#f59e0b', '#ef4444', '#8b5cf6', '#ffffff']
      },
      minimal: {
        design: designTemplates.find(d => d.id === 'minimal-elegant'),
        headline: 'Less is More',
        description: 'Elegant simplicity for modern brands',
        colors: ['#000000', '#ffffff', '#f5f5f5', '#9ca3af']
      },
      bold: {
        design: designTemplates.find(d => d.id === 'bold-impact'),
        headline: 'Make an Impact',
        description: 'Stand out with powerful visual communication',
        colors: ['#dc2626', '#000000', '#ffffff', '#fbbf24']
      }
    };

    const preset = presets[presetType];
    if (preset.design) {
      setSelectedDesign(preset.design);
      setHeadline(preset.headline);
      setDescription(preset.description);
      setCustomColors(preset.colors);
      toast.success(`${presetType} preset applied!`);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Social Media Templates Generator
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Controls */}
            <div className="space-y-6">
              <Tabs defaultValue="platform" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="platform">Platform</TabsTrigger>
                  <TabsTrigger value="design">Design</TabsTrigger>
                  <TabsTrigger value="content">Content</TabsTrigger>
                  <TabsTrigger value="customize">Customize</TabsTrigger>
                </TabsList>

                <TabsContent value="platform" className="space-y-4">
                  <div>
                    <Label>Pilih Platform</Label>
                    <Select value={selectedPlatform.id} onValueChange={handlePlatformChange}>
                      <SelectTrigger className="mt-2">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {socialPlatforms.map((platform) => (
                          <SelectItem key={platform.id} value={platform.id}>
                            {platform.icon} {platform.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label>Template Size</Label>
                    <div className="grid grid-cols-1 gap-2 mt-2">
                      {selectedPlatform.templates.map((template) => (
                        <Card
                          key={template.id}
                          className={`cursor-pointer transition-all ${
                            selectedTemplate.id === template.id
                              ? 'ring-2 ring-blue-500 bg-blue-50'
                              : 'hover:bg-gray-50'
                          }`}
                          onClick={() => handleTemplateChange(template.id)}
                        >
                          <CardContent className="p-3">
                            <div className="flex justify-between items-center">
                              <div>
                                <h4 className="font-medium">{template.name}</h4>
                                <p className="text-sm text-gray-600">{template.description}</p>
                              </div>
                              <Badge variant="outline">
                                {template.width} × {template.height}
                              </Badge>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>

                  <div className="p-3 bg-blue-50 rounded-lg">
                    <h4 className="font-medium text-blue-900 mb-2">📱 Platform Info</h4>
                    <p className="text-sm text-blue-800">
                      {selectedPlatform.name} - {selectedTemplate.description}
                    </p>
                    <p className="text-xs text-blue-700 mt-1">
                      Optimal size: {selectedTemplate.width} × {selectedTemplate.height} pixels
                    </p>
                  </div>
                </TabsContent>

                <TabsContent value="design" className="space-y-4">
                  <div>
                    <Label>Design Style</Label>
                    <div className="grid grid-cols-1 gap-2 mt-2">
                      {designTemplates.map((design) => (
                        <Card
                          key={design.id}
                          className={`cursor-pointer transition-all ${
                            selectedDesign.id === design.id
                              ? 'ring-2 ring-blue-500 bg-blue-50'
                              : 'hover:bg-gray-50'
                          }`}
                          onClick={() => handleDesignChange(design.id)}
                        >
                          <CardContent className="p-3">
                            <div className="flex justify-between items-center">
                              <div>
                                <h4 className="font-medium">{design.name}</h4>
                                <p className="text-sm text-gray-600">{design.description}</p>
                                <Badge variant="secondary" className="mt-1">
                                  {design.category}
                                </Badge>
                              </div>
                              <div className="flex gap-1">
                                {design.colors.slice(0, 4).map((color, index) => (
                                  <div
                                    key={index}
                                    className="w-6 h-6 rounded border"
                                    style={{ backgroundColor: color }}
                                  />
                                ))}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="content" className="space-y-4">
                  <div>
                    <Label htmlFor="headline">Headline</Label>
                    <Input
                      id="headline"
                      value={headline}
                      onChange={(e) => setHeadline(e.target.value)}
                      placeholder="Enter your headline"
                      className="mt-2"
                    />
                  </div>

                  <div>
                    <Label htmlFor="description">Description / CTA</Label>
                    <Textarea
                      id="description"
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      placeholder="Enter description or call-to-action"
                      className="mt-2"
                      rows={3}
                    />
                  </div>

                  <Button
                    onClick={generateAIContent}
                    disabled={isGenerating}
                    className="w-full"
                  >
                    {isGenerating ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <Sparkles className="w-4 h-4 mr-2" />
                        Generate AI Content
                      </>
                    )}
                  </Button>

                  <div>
                    <Label>Quick Presets</Label>
                    <div className="grid grid-cols-2 gap-2 mt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => applyPreset('business')}
                      >
                        💼 Business
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => applyPreset('creative')}
                      >
                        🎨 Creative
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => applyPreset('minimal')}
                      >
                        ⚪ Minimal
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => applyPreset('bold')}
                      >
                        ⚡ Bold
                      </Button>
                    </div>
                  </div>

                  <div className="p-3 bg-green-50 rounded-lg">
                    <h4 className="font-medium text-green-900 mb-2">✍️ Content Tips</h4>
                    <ul className="text-sm text-green-800 space-y-1">
                      <li>• Keep headlines short and impactful</li>
                      <li>• Use action words in CTAs</li>
                      <li>• Consider your target audience</li>
                      <li>• Test different variations</li>
                    </ul>
                  </div>
                </TabsContent>

                <TabsContent value="customize" className="space-y-4">
                  <div>
                    <Label>Font Size: {fontSize}px</Label>
                    <input
                      type="range"
                      min="24"
                      max="72"
                      value={fontSize}
                      onChange={(e) => setFontSize(Number(e.target.value))}
                      className="w-full mt-2"
                    />
                  </div>

                  <div>
                    <Label>Custom Colors</Label>
                    <div className="grid grid-cols-4 gap-2 mt-2">
                      {customColors.map((color, index) => (
                        <div key={index} className="space-y-1">
                          <input
                            type="color"
                            value={color}
                            onChange={(e) => {
                              const newColors = [...customColors];
                              newColors[index] = e.target.value;
                              setCustomColors(newColors);
                            }}
                            className="w-full h-10 border rounded"
                          />
                          <p className="text-xs text-center">{color}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label>Background Image</Label>
                    <div className="mt-2 space-y-2">
                      <Button
                        variant="outline"
                        onClick={() => fileInputRef.current?.click()}
                        className="w-full"
                      >
                        <Upload className="w-4 h-4 mr-2" />
                        Upload Background Image
                      </Button>
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                      />
                      {backgroundImage && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setBackgroundImage(null)}
                          className="w-full"
                        >
                          Remove Background
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="p-3 bg-purple-50 rounded-lg">
                    <h4 className="font-medium text-purple-900 mb-2">🎨 Design Tips</h4>
                    <ul className="text-sm text-purple-800 space-y-1">
                      <li>• Use high contrast for readability</li>
                      <li>• Keep important text away from edges</li>
                      <li>• Consider mobile viewing</li>
                      <li>• Test on different backgrounds</li>
                    </ul>
                  </div>
                </TabsContent>
              </Tabs>
            </div>

            {/* Preview */}
            <div className="space-y-4">
              <div>
                <Label>Template Preview</Label>
                <Card className="mt-2">
                  <CardContent className="p-4">
                    <div className="flex justify-center">
                      <canvas
                        ref={canvasRef}
                        className="border rounded-lg max-w-full h-auto"
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-2">
                <Label>Export Template</Label>
                <div className="grid grid-cols-3 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => downloadTemplate('png')}
                  >
                    <Download className="w-4 h-4 mr-1" />
                    PNG
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => downloadTemplate('jpg')}
                  >
                    <Download className="w-4 h-4 mr-1" />
                    JPG
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyToClipboard}
                  >
                    <Copy className="w-4 h-4 mr-1" />
                    Copy
                  </Button>
                </div>

                <Button
                  onClick={batchExport}
                  className="w-full"
                  variant="default"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Batch Export All Sizes
                </Button>
              </div>

              <div className="p-4 bg-orange-50 rounded-lg">
                <h4 className="font-medium text-orange-900 mb-2">📱 Platform Guidelines</h4>
                <ul className="text-sm text-orange-800 space-y-1">
                  <li>• Instagram: Square posts perform best</li>
                  <li>• Facebook: Use 1.91:1 ratio for link posts</li>
                  <li>• Twitter: Keep text readable at small sizes</li>
                  <li>• LinkedIn: Professional tone works best</li>
                  <li>• YouTube: Bold thumbnails get more clicks</li>
                  <li>• TikTok: Vertical format is essential</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
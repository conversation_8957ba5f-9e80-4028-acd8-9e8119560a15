
import { useState, useEffect, lazy, Suspense } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { getApi<PERSON>ey, API_KEYS } from "@/utils/apiKeys";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Copy, Maximize2, Download, Edit, Image, Save, Moon, Sun, Settings, Search, FileText, Mic, Mic<PERSON>ff, Wrench, Bot, Gift } from "lucide-react";
// Lazy load ToolsHub for better performance
const ToolsHub = lazy(() =>
  import("@/components/tools/ToolsHub").then(module => ({
    default: module.ToolsHub
  }))
);
// Lazy load PersonalitySelector for better performance
const PersonalitySelector = lazy(() =>
  import("@/components/personality/PersonalitySelector").then(module => ({
    default: module.PersonalitySelector
  }))
);

// Define personalities inline to avoid import conflicts
const personalities = [
  {
    id: "friendly",
    name: "Friendly Assistant",
    emoji: "😊",
    description: "Ramah dan membantu",
    systemPrompt: "You are a friendly and helpful AI assistant. Always be warm, encouraging, and supportive in your responses.",
    greeting: "Halo! Saya di sini untuk membantu Anda dengan senang hati! 😊"
  },
  {
    id: "professional",
    name: "Professional Expert",
    emoji: "💼",
    description: "Profesional dan ahli",
    systemPrompt: "You are a professional expert AI assistant. Provide detailed, accurate, and well-structured responses with a formal tone.",
    greeting: "Good day. I am ready to assist you with professional expertise."
  },
  {
    id: "creative",
    name: "Creative Genius",
    emoji: "🎨",
    description: "Kreatif dan inovatif",
    systemPrompt: "You are a creative and innovative AI assistant. Think outside the box and provide imaginative, artistic solutions.",
    greeting: "Hey there, creative soul! ✨ Ready to explore some amazing ideas together?"
  },
  {
    id: "energetic",
    name: "Energetic Buddy",
    emoji: "⚡",
    description: "Energik dan antusias",
    systemPrompt: "You are an energetic and enthusiastic AI assistant. Be upbeat, motivational, and full of positive energy.",
    greeting: "WOOHOO! 🚀 Let's get this party started! What awesome thing are we doing today?"
  },
  {
    id: "wise",
    name: "Wise Mentor",
    emoji: "🧙‍♂️",
    description: "Bijaksana dan berpengalaman",
    systemPrompt: "You are a wise and experienced AI mentor. Provide thoughtful, philosophical insights and guidance.",
    greeting: "Greetings, young seeker. I am here to share wisdom and guide you on your journey."
  },
  {
    id: "casual",
    name: "Casual Friend",
    emoji: "☕",
    description: "Santai dan bersahabat",
    systemPrompt: "You are a casual and friendly AI companion. Be relaxed, conversational, and approachable.",
    greeting: "Hey! What's up? Just chilling here, ready to chat about whatever's on your mind! ☕"
  }
];

type Personality = typeof personalities[0];
// Lazy load Easter Egg components for better performance
const EasterEggSystem = lazy(() =>
  import("@/components/easter-eggs/EasterEggSystem").then(module => ({
    default: module.EasterEggSystem
  }))
);

const EasterEggCollection = lazy(() =>
  import("@/components/easter-eggs/EasterEggSystem").then(module => ({
    default: module.EasterEggCollection
  }))
);
import Tesseract from 'tesseract.js';
import * as pdfjsLib from 'pdfjs-dist';
import "highlight.js/styles/github-dark.css"; // Tema highlight


interface Message {
  role: "user" | "assistant";
  content: string;
  type?: "text" | "code" | "image";
  imageUrl?: string;
  language?: string;
  timestamp?: number;
  reactions?: { [emoji: string]: number };
}

const LOCAL_STORAGE_KEY = "chat_messages";

declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
    msSpeechRecognition: any; // Edge support
  }
}

// Edge polyfills
if (!(Array.prototype as any).findLast) {
  (Array.prototype as any).findLast = function(predicate: any) {
    for (let i = this.length - 1; i >= 0; i--) {
      if (predicate(this[i], i, this)) {
        return this[i];
      }
    }
    return undefined;
  };
}

// Edge fetch timeout wrapper
const fetchWithTimeout = (url: string, options: any = {}, timeout = 30000) => {
  return Promise.race([
    fetch(url, options),
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Request timeout')), timeout)
    )
  ]);
};

const Chat = () => {
  const navigate = useNavigate();
  const [messages, setMessages] = useState<Message[]>([]);
  const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<SpeechSynthesisVoice | null>(null);
  const [recognition, setRecognition] = useState<(typeof window.SpeechRecognition | typeof window.webkitSpeechRecognition) | null>(null);
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);
  const [editingCode, setEditingCode] = useState<string | null>(null);
  const [previewCode, setPreviewCode] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isListening, setIsListening] = useState(false);
  const [isProcessingFile, setIsProcessingFile] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [showTools, setShowTools] = useState(false);
  const [currentPersonality, setCurrentPersonality] = useState<Personality>(personalities[0]);
  const [showPersonality, setShowPersonality] = useState(false);
  const [discoveredEggs, setDiscoveredEggs] = useState<any[]>([]);
  const [showEasterEggs, setShowEasterEggs] = useState(false);

  const speakText = (text: string) => {
    if ("speechSynthesis" in window) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.lang = "id-ID";
      utterance.rate = 1.0;
      utterance.pitch = 1.0;
      speechSynthesis.speak(utterance);
    } else {
      alert("Browser tidak mendukung fitur Text-to-Speech!");
    }
  };

  const stopSpeaking = () => {
    if ("speechSynthesis" in window) {
      speechSynthesis.cancel();
    }
  };

  const startListening = () => {
    // Edge compatibility check
    const SpeechRecognition = (window as any).SpeechRecognition ||
                             (window as any).webkitSpeechRecognition ||
                             (window as any).msSpeechRecognition;

    if (SpeechRecognition) {
      try {
        const recognition = new SpeechRecognition();
        recognition.lang = "id-ID";
        recognition.continuous = false;
        recognition.interimResults = false;

        recognition.onresult = (event: any) => {
          if (event.results && event.results[0] && event.results[0][0]) {
            const transcript = event.results[0][0].transcript;
            setInput(transcript);
            toast.success("Suara berhasil dikenali!");
          }
        };

        recognition.onerror = (event: any) => {
          console.error("Speech recognition error:", event.error);
          setIsListening(false);
          toast.error("Error dalam pengenalan suara");
        };

        recognition.onend = () => {
          setIsListening(false);
        };

        recognition.start();
        setIsListening(true);
      } catch (error) {
        console.error("Speech recognition initialization error:", error);
        toast.error("Gagal memulai pengenalan suara");
        setIsListening(false);
      }
    } else {
      toast.error("Speech recognition tidak didukung di browser Edge ini");
    }
  };


  // Load messages from localStorage on initial render
  useEffect(() => {
    const savedMessages = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (savedMessages) {
      try {
        setMessages(JSON.parse(savedMessages));
      } catch (error) {
        console.error("Error parsing saved messages:", error);
      }
    }
  }, []);

  // Cleanup effect only on unmount (no dependencies to avoid re-renders)
  useEffect(() => {
    return () => {
      // Cleanup function to revoke blob URLs when component unmounts
      // Use a ref to access latest messages without causing re-renders
      const currentMessages = JSON.parse(localStorage.getItem(LOCAL_STORAGE_KEY) || '[]');
      currentMessages.forEach((message: any) => {
        if (message.imageUrl && message.imageUrl.startsWith('blob:')) {
          URL.revokeObjectURL(message.imageUrl);
        }
      });
    };
  }, []); // Empty dependency array - only run on mount/unmount

// Add this new function to delete history
const hapusHistory = () => {
  // Cleanup any blob URLs in existing messages
  messages.forEach(message => {
    if (message.imageUrl && message.imageUrl.startsWith('blob:')) {
      URL.revokeObjectURL(message.imageUrl);
    }
  });

  setMessages([]);
  localStorage.removeItem(LOCAL_STORAGE_KEY);
  setShowDeleteConfirm(false);
  toast.success("Riwayat chat telah dihapus");
};



  // TEMPORARILY DISABLED localStorage for performance testing
  // useEffect(() => {
  //   const timeoutId = setTimeout(() => {
  //     localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(messages));
  //   }, 500);
  //   return () => clearTimeout(timeoutId);
  // }, [messages]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    const userMessage: Message = {
      role: "user",
      content: input,
      timestamp: Date.now()
    };
    setMessages((prev) => [...prev, userMessage]);
    setInput("");
    setIsLoading(true);

    const groqKey = getApiKey(API_KEYS.GROQ);
    console.log("Groq API Key check:", groqKey ? "Key found" : "No key found");

    if (!groqKey) {
      toast.error("API key tidak ditemukan. Silakan atur API key terlebih dahulu");
      setIsLoading(false);
      navigate("/");
      return;
    }

    try {
      console.log("Sending request to Groq API with key:", groqKey ? "Key present" : "No key");

      const response = await fetch("https://api.groq.com/openai/v1/chat/completions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${groqKey}`,
        },
        body: JSON.stringify({
          model: "llama-3.1-8b-instant", // Lightweight model with higher free limits
          messages: [
            // Add personality system prompt
            {
              role: "system",
              content: `${currentPersonality.systemPrompt}\n\nYou are KIKAZE-AI, an Indonesian AI assistant. Always respond in Indonesian unless specifically asked to use another language. Be helpful, friendly, and engaging.`
            },
            // Limit context to last 10 messages to avoid token limit
            ...messages.slice(-10).map(({ role, content }) => ({
              role,
              content: content.length > 2000 ? content.substring(0, 2000) + "..." : content
            })),
            { role: userMessage.role, content: userMessage.content }
          ],
          temperature: 0.7,
          max_tokens: 1024, // Reduced to save tokens
        }),
      });

      console.log("Response status:", response.status);
      console.log("Response headers:", response.headers);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("API Error Response:", errorText);
        throw new Error(`Groq API Error (${response.status}): ${errorText}`);
      }

      const data = await response.json();
      const content = data.choices[0].message.content;
      
      const codeBlockRegex = /```([a-zA-Z]*)\n([\s\S]*?)```/g;
      let match;
      let lastIndex = 0;
      const processedMessages: Message[] = [];

      while ((match = codeBlockRegex.exec(content)) !== null) {
        if (match.index > lastIndex) {
          const textContent = content.slice(lastIndex, match.index).trim();
          if (textContent) {
            processedMessages.push({
              role: "assistant",
              content: textContent,
              type: "text"
            });
          }
        }

        processedMessages.push({
          role: "assistant",
          content: match[2].trim(),
          type: "code",
          language: match[1] || "plaintext"
        });

        lastIndex = match.index + match[0].length;
      }

      if (lastIndex < content.length) {
        const remainingText = content.slice(lastIndex).trim();
        if (remainingText) {
          processedMessages.push({
            role: "assistant",
            content: remainingText,
            type: "text"
          });
        }
      }

      setMessages((prev) => [...prev, ...processedMessages]);
    } catch (error) {
      console.error("Error:", error);

      // More specific error messages
      let errorMessage = "Gagal mengirim pesan ke Groq API";
      if (error instanceof Error) {
        if (error.message.includes("401")) {
          errorMessage = "API key tidak valid. Silakan periksa kembali API key Anda.";
        } else if (error.message.includes("429")) {
          errorMessage = "Terlalu banyak permintaan. Silakan coba lagi nanti.";
        } else if (error.message.includes("500")) {
          errorMessage = "Server Groq sedang bermasalah. Silakan coba lagi nanti.";
        } else if (error.message.includes("network") || error.message.includes("fetch")) {
          errorMessage = "Masalah koneksi internet. Periksa koneksi Anda.";
        } else {
          errorMessage = `Error: ${error.message}`;
        }
      }

      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const enhancePrompt = (prompt: string): string => {
    // Add quality enhancers to the prompt
    const qualityTerms = [
      "high quality", "detailed", "sharp focus", "professional",
      "masterpiece", "best quality", "ultra detailed"
    ];

    // Check if prompt already has quality terms
    const hasQualityTerms = qualityTerms.some(term =>
      prompt.toLowerCase().includes(term.toLowerCase())
    );

    if (!hasQualityTerms) {
      return `${prompt}, high quality, detailed, masterpiece`;
    }

    return prompt;
  };

  const generateImage = async (prompt: string) => {
    const hfToken = getApiKey(API_KEYS.HUGGINGFACE);
    console.log("HF Token check:", hfToken ? "Token found" : "No token found");

    if (!hfToken) {
      toast.error("API token tidak ditemukan. Silakan atur Hugging Face token terlebih dahulu");
      navigate("/");
      return;
    }

    setIsGeneratingImage(true);

    try {

    // Enhance the prompt for better results
    const enhancedPrompt = enhancePrompt(prompt);
    console.log("Original prompt:", prompt);
    console.log("Enhanced prompt:", enhancedPrompt);

    // Try multiple models in order of preference (best to fallback)
    const models = [
      "black-forest-labs/FLUX.1-schnell",        // Fastest FLUX model, very high quality
      "stabilityai/stable-diffusion-xl-base-1.0", // SDXL - excellent quality
      "stabilityai/stable-diffusion-2-1",        // Good quality, reliable
      "runwayml/stable-diffusion-v1-5",          // Most reliable fallback
      "CompVis/stable-diffusion-v1-4"            // Final fallback
    ];

    for (let i = 0; i < models.length; i++) {
      const model = models[i];
      console.log(`Trying model: ${model}`);

      try {
        // Customize parameters based on model
        let requestBody;
        if (model.includes("FLUX")) {
          // FLUX models work best with simpler parameters
          requestBody = {
            inputs: enhancedPrompt,
            parameters: {
              num_inference_steps: 4, // FLUX.1-schnell is optimized for 4 steps
              guidance_scale: 3.5,
            },
          };
        } else if (model.includes("xl")) {
          // SDXL models
          requestBody = {
            inputs: `${enhancedPrompt}, 8k, ultra detailed`,
            parameters: {
              negative_prompt: "lowres, bad anatomy, bad hands, cropped, worst quality, blurry, low quality, deformed, ugly, duplicate",
              num_inference_steps: 30,
              guidance_scale: 8.0,
              width: 1024,
              height: 1024,
            },
          };
        } else {
          // Standard SD models
          requestBody = {
            inputs: enhancedPrompt,
            parameters: {
              negative_prompt: "lowres, bad anatomy, bad hands, cropped, worst quality, blurry, deformed, ugly",
              num_inference_steps: 25,
              guidance_scale: 7.5,
              width: 512,
              height: 512,
            },
          };
        }

        const response = await fetch(
          `https://api-inference.huggingface.co/models/${model}`,
          {
            method: "POST",
            headers: {
              Authorization: `Bearer ${hfToken}`,
              "Content-Type": "application/json",
            },
            body: JSON.stringify(requestBody),
          }
        );

        console.log(`Response status for ${model}:`, response.status);
        console.log(`Response headers:`, response.headers);

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Error with ${model}:`, errorText);

          // If this is the last model, throw error
          if (i === models.length - 1) {
            throw new Error(`Semua model gagal. Status terakhir: ${response.status}, Error: ${errorText}`);
          }
          // Otherwise, continue to next model
          continue;
        }

        const blob = await response.blob();
        console.log("Blob size:", blob.size);

        if (blob.size === 0) {
          throw new Error("Received empty image data");
        }

        // Get model display name
        const modelDisplayName = model.includes("FLUX") ? "FLUX.1-schnell (Kualitas Tinggi)" :
                                model.includes("xl") ? "Stable Diffusion XL (Kualitas Tinggi)" :
                                model.includes("v1-5") ? "Stable Diffusion v1.5" :
                                model.includes("v2-1") ? "Stable Diffusion v2.1" :
                                model;

        // Convert blob to data URL for better reliability
        const reader = new FileReader();
        reader.onload = () => {
          const imageUrl = reader.result as string;

          setMessages((prev) => {
            // Remove loading message and add result
            const filteredMessages = prev.filter(msg =>
              !(msg.role === "assistant" && msg.content.includes("Sedang membuat gambar"))
            );
            return [
              ...filteredMessages,
              {
                role: "assistant",
                content: `Berikut gambar yang telah dibuat menggunakan ${modelDisplayName}:`,
                type: "image",
                imageUrl,
              },
            ];
          });
        };
        reader.readAsDataURL(blob);
        toast.success(`Gambar berhasil dibuat dengan ${modelDisplayName}!`);
        return; // Success, exit the function

      } catch (error) {
        console.error(`Error with model ${model}:`, error);

        // If this is the last model, show error
        if (i === models.length - 1) {
          let errorMessage = "Gagal menghasilkan gambar. Mohon coba lagi.";
          if (error instanceof Error) {
            if (error.message.includes("401")) {
              errorMessage = "Token Hugging Face tidak valid. Silakan periksa token Anda.";
            } else if (error.message.includes("429")) {
              errorMessage = "Terlalu banyak permintaan. Silakan coba lagi dalam beberapa menit.";
            } else if (error.message.includes("503") || error.message.includes("loading")) {
              errorMessage = "Model sedang loading. Silakan coba lagi dalam 1-2 menit.";
            } else {
              errorMessage = `Error: ${error.message}`;
            }
          }
          // Remove loading message on error
          setMessages((prev) =>
            prev.filter(msg =>
              !(msg.role === "assistant" && msg.content.includes("Sedang membuat gambar"))
            )
          );
          toast.error(errorMessage);
        }
      }
    }
    } catch (outerError) {
      console.error("Outer error in generateImage:", outerError);
      // Remove loading message on error
      setMessages((prev) =>
        prev.filter(msg =>
          !(msg.role === "assistant" && msg.content.includes("Sedang membuat gambar"))
        )
      );
      toast.error("Gagal menghasilkan gambar. Mohon coba lagi.");
    } finally {
      // Always reset loading state
      setIsGeneratingImage(false);
    }
  };

  const testHuggingFaceToken = async () => {
    const hfToken = getApiKey(API_KEYS.HUGGINGFACE);
    if (!hfToken) {
      toast.error("Token tidak ditemukan");
      return;
    }

    try {
      const response = await fetch("https://huggingface.co/api/whoami-v2", {
        headers: {
          Authorization: `Bearer ${hfToken}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log("HF Token valid, user:", data);
        toast.success("Token Hugging Face valid!");
      } else {
        console.error("Invalid HF token, status:", response.status);
        toast.error("Token Hugging Face tidak valid!");
      }
    } catch (error) {
      console.error("Error testing HF token:", error);
      toast.error("Gagal memverifikasi token");
    }
  };

  const addReaction = (messageIndex: number, emoji: string) => {
    setMessages((prev) =>
      prev.map((msg, idx) => {
        if (idx === messageIndex) {
          const reactions = msg.reactions || {};
          reactions[emoji] = (reactions[emoji] || 0) + 1;
          return { ...msg, reactions };
        }
        return msg;
      })
    );
  };

  const formatTimestamp = (timestamp?: number) => {
    if (!timestamp) return "";
    const date = new Date(timestamp);
    return date.toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' });
  };

  // File processing functions
  const processFile = async (file: File) => {
    setIsProcessingFile(true);

    try {
      const fileType = file.type;
      const fileName = file.name;
      const fileSize = (file.size / 1024 / 1024).toFixed(2); // MB

      // Add processing message
      const processingMessage: Message = {
        role: "assistant",
        content: `📎 Sedang memproses file "${fileName}" (${fileSize} MB)...`,
        type: "text",
        timestamp: Date.now()
      };
      setMessages((prev) => [...prev, processingMessage]);

      let analysisResult = "";

      if (fileType.startsWith('image/')) {
        analysisResult = await processImage(file);
      } else if (fileType === 'application/pdf') {
        analysisResult = await processPDF(file);
      } else if (fileType.startsWith('text/') || fileType.includes('document')) {
        analysisResult = await processDocument(file);
      } else if (fileType.startsWith('audio/')) {
        analysisResult = await processAudio(file);
      } else if (fileType === 'text/csv' || fileName.endsWith('.csv')) {
        analysisResult = await processCSV(file);
      } else if (fileType === 'application/json' || fileName.endsWith('.json')) {
        analysisResult = await processJSON(file);
      } else {
        analysisResult = `❌ Format file "${fileType}" belum didukung.

Format yang didukung:
📷 Gambar: JPG, PNG, GIF, WebP
📄 Dokumen: PDF, TXT, DOC, DOCX
🎵 Audio: MP3, WAV, M4A
📊 Data: CSV, JSON`;
      }

      // Remove processing message and add result
      setMessages((prev) => {
        const filtered = prev.filter(msg => !msg.content.includes("Sedang memproses file"));
        return [
          ...filtered,
          {
            role: "assistant",
            content: analysisResult,
            type: "text",
            timestamp: Date.now()
          }
        ];
      });

    } catch (error) {
      console.error("Error processing file:", error);

      // Remove processing message and show error
      setMessages((prev) => {
        const filtered = prev.filter(msg => !msg.content.includes("Sedang memproses file"));
        return [
          ...filtered,
          {
            role: "assistant",
            content: `❌ Gagal memproses file: ${error instanceof Error ? error.message : 'Unknown error'}`,
            type: "text",
            timestamp: Date.now()
          }
        ];
      });
    } finally {
      setIsProcessingFile(false);
    }
  };

  // Image preprocessing function
  const preprocessImage = (canvas: HTMLCanvasElement, ctx: CanvasRenderingContext2D): void => {
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // Convert to grayscale and increase contrast
    for (let i = 0; i < data.length; i += 4) {
      const gray = Math.round(0.299 * data[i] + 0.587 * data[i + 1] + 0.114 * data[i + 2]);

      // Increase contrast
      const contrast = 1.5;
      const factor = (259 * (contrast + 255)) / (255 * (259 - contrast));
      const newGray = Math.min(255, Math.max(0, factor * (gray - 128) + 128));

      data[i] = newGray;     // Red
      data[i + 1] = newGray; // Green
      data[i + 2] = newGray; // Blue
      // Alpha channel remains unchanged
    }

    ctx.putImageData(imageData, 0, 0);
  };

  const processImage = async (file: File): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = async (e) => {
        const imageData = (e.target && e.target.result) as string;

        // Create image element for preprocessing
        const img = document.createElement('img') as HTMLImageElement;
        img.onload = async () => {
          // Create canvas for preprocessing
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          if (!ctx) {
            // Fallback to original image if canvas not supported
            await performOCR(imageData, file, resolve);
            return;
          }

          // Set canvas size to image size
          canvas.width = img.width;
          canvas.height = img.height;

          // Draw image to canvas
          ctx.drawImage(img, 0, 0);

          // Preprocess image
          preprocessImage(canvas, ctx);

          // Get processed image data
          const processedImageData = canvas.toDataURL('image/png');

          // Perform OCR on processed image
          await performOCR(processedImageData, file, resolve);
        };

        img.src = imageData;
      };
      reader.readAsDataURL(file);
    });
  };

  const performOCR = async (imageData: string, file: File, resolve: (value: string) => void) => {

    try {
      // Try multiple OCR configurations for better results
      const ocrConfigs = [
        {
          name: "Enhanced Auto",
          options: {
            logger: m => console.log(m),
            tessedit_pageseg_mode: Tesseract.PSM.AUTO,
            tessedit_ocr_engine_mode: Tesseract.OEM.LSTM_ONLY,
            preserve_interword_spaces: '1',
          }
        },
        {
          name: "Single Block",
          options: {
            logger: m => console.log(m),
            tessedit_pageseg_mode: Tesseract.PSM.SINGLE_BLOCK,
            tessedit_ocr_engine_mode: Tesseract.OEM.LSTM_ONLY,
          }
        },
        {
          name: "Single Line",
          options: {
            logger: m => console.log(m),
            tessedit_pageseg_mode: Tesseract.PSM.SINGLE_LINE,
            tessedit_ocr_engine_mode: Tesseract.OEM.LSTM_ONLY,
          }
        }
      ];

      let bestResult = { text: '', confidence: 0, configName: '' };

      // Try each configuration and keep the best result
      for (const config of ocrConfigs) {
        try {
          console.log(`Trying OCR config: ${config.name}`);
          const { data: { text, confidence } } = await Tesseract.recognize(
            imageData,
            'ind+eng', // Indonesian and English
            config.options
          );

          if (confidence > bestResult.confidence) {
            bestResult = { text, confidence, configName: config.name };
          }
        } catch (configError) {
          console.warn(`OCR config ${config.name} failed:`, configError);
          continue;
        }
      }

      const { text, confidence } = bestResult;

      const hasText = text.trim().length > 0;
      const wordCount = text.split(/\s+/).filter(word => word.length > 0).length;
      const cleanText = text.replace(/[^\w\s\.,!?;:()\-]/g, ' ').replace(/\s+/g, ' ').trim();

      const analysis = `🖼️ **Analisis Gambar: ${file.name}**

📊 **Informasi File:**
- Ukuran: ${(file.size / 1024 / 1024).toFixed(2)} MB
- Format: ${file.type}
- OCR Confidence: ${confidence.toFixed(1)}%
- Status Preprocessing: ✅ Enhanced
- Best OCR Config: ${bestResult.configName}

${hasText ? `📝 **Teks yang Ditemukan:**
\`\`\`
${cleanText.length > 1000 ? cleanText.substring(0, 1000) + '...' : cleanText}
\`\`\`

🔍 **Analisis:**
- Berhasil mengekstrak ${wordCount} kata
- Karakter total: ${cleanText.length}
- Tingkat akurasi: ${confidence > 80 ? '🟢 Tinggi' : confidence > 60 ? '🟡 Sedang' : confidence > 40 ? '🟠 Rendah' : '🔴 Sangat Rendah'}
- Bahasa terdeteksi: Indonesia/English

${confidence < 60 ? `⚠️ **Tips untuk Akurasi Lebih Baik:**
- Gunakan gambar dengan resolusi lebih tinggi
- Pastikan teks dalam gambar jelas dan tidak buram
- Hindari gambar dengan background yang kompleks
- Coba crop gambar hanya pada bagian teks` : ''}` : `🔍 **Hasil Analisis:**
- Tidak ada teks yang terdeteksi dalam gambar
- Kemungkinan gambar tidak mengandung teks
- Atau kualitas gambar kurang jelas untuk OCR

💡 **Saran:**
- Pastikan gambar mengandung teks yang jelas
- Coba dengan gambar yang memiliki kontras lebih baik
- Gunakan gambar dengan resolusi minimal 300 DPI`}

💡 **Saran Penggunaan:**
- Tanyakan: "Apa yang ada di gambar ini?"
- Minta: "Jelaskan detail objek dalam gambar"
- Analisis: "Ringkas teks yang ditemukan"

🖼️ **Preview:**
![Uploaded Image](${imageData})`;

      resolve(analysis);
    } catch (error) {
      console.error('OCR Error:', error);
      const analysis = `🖼️ **Analisis Gambar: ${file.name}**

📊 **Informasi File:**
- Ukuran: ${(file.size / 1024 / 1024).toFixed(2)} MB
- Format: ${file.type}

❌ **OCR Error:** Gagal mengekstrak teks dari gambar
- Kemungkinan format tidak didukung
- Atau gambar terlalu buram/kecil

💡 **Saran:**
- Gunakan gambar dengan resolusi tinggi
- Pastikan teks dalam gambar jelas terbaca
- Format yang didukung: JPG, PNG, WebP

🖼️ **Preview:**
![Uploaded Image](${imageData})`;

      resolve(analysis);
    }
  };

  const processPDF = async (file: File): Promise<string> => {
    try {
      // Set worker path for PDF.js - use same version as library
      pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/4.10.38/pdf.worker.min.js`;

      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;

      let fullText = '';
      const numPages = pdf.numPages;

      // Extract text from all pages
      for (let i = 1; i <= Math.min(numPages, 10); i++) { // Limit to first 10 pages
        const page = await pdf.getPage(i);
        const textContent = await page.getTextContent();
        const pageText = textContent.items
          .map((item: any) => item.str)
          .join(' ');
        fullText += `\n\n--- Halaman ${i} ---\n${pageText}`;
      }

      const wordCount = fullText.split(/\s+/).filter(word => word.length > 0).length;
      const charCount = fullText.length;

      return `📄 **Analisis PDF: ${file.name}**

📊 **Informasi File:**
- Ukuran: ${(file.size / 1024 / 1024).toFixed(2)} MB
- Jumlah halaman: ${numPages}
- Halaman diproses: ${Math.min(numPages, 10)}
- Jumlah kata: ${wordCount.toLocaleString()}
- Jumlah karakter: ${charCount.toLocaleString()}

📝 **Konten yang Diekstrak:**
\`\`\`
${fullText.substring(0, 2000)}${fullText.length > 2000 ? '\n\n... (konten dipotong, total ' + fullText.length + ' karakter)' : ''}
\`\`\`

🔍 **Analisis yang Tersedia:**
- ✅ Ekstrak teks lengkap
- ✅ Summarize per halaman
- ✅ Q&A berdasarkan dokumen
- ✅ Analisis struktur dokumen
- ✅ Pencarian kata kunci

💡 **Contoh Pertanyaan:**
- "Ringkas dokumen PDF ini"
- "Apa poin-poin penting di halaman 1?"
- "Cari informasi tentang [topik]"
- "Buat summary executive dari PDF"`;

    } catch (error) {
      console.error('PDF Processing Error:', error);

      // Try alternative approach for PDF processing
      if (error instanceof Error && error.message.includes('API version')) {
        return `📄 **PDF Analysis: ${file.name}**

⚠️ **PDF.js Version Conflict Detected**
- Menggunakan fallback analysis method
- Ekstraksi teks tidak tersedia saat ini

📊 **Informasi File:**
- Ukuran: ${(file.size / 1024 / 1024).toFixed(2)} MB
- Format: PDF
- Status: File berhasil diupload

🔍 **Analisis yang Tersedia:**
- ✅ File validation
- ✅ Size analysis
- ✅ Format detection
- ❌ Text extraction (temporary issue)

💡 **Solusi Alternatif:**
- Convert PDF ke gambar dan gunakan OCR
- Copy-paste teks manual untuk analisis
- Gunakan PDF reader lain untuk ekstrak teks

🛠️ **Technical Note:**
Error: ${error.message}

📝 **Cara Menggunakan:**
- Tanyakan: "Bagaimana cara menganalisis PDF ini?"
- Minta: "Saran untuk ekstrak teks dari PDF"
- Alternative: Upload screenshot PDF untuk OCR`;
      }

      return `📄 **Error Memproses PDF: ${file.name}**

❌ **Gagal mengekstrak teks dari PDF**
- Error: ${error instanceof Error ? error.message : 'Unknown error'}
- Kemungkinan PDF terproteksi atau corrupt

💡 **Saran:**
- Pastikan PDF tidak terproteksi password
- Coba dengan file PDF yang lebih kecil
- Format PDF harus readable (bukan scan image)
- Convert ke gambar dan gunakan OCR

📊 **Informasi File:**
- Ukuran: ${(file.size / 1024 / 1024).toFixed(2)} MB
- Format: PDF`;
    }
  };

  const processDocument = async (file: File): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = (e.target && e.target.result) as string;
        const wordCount = content.split(/\s+/).length;
        const charCount = content.length;

        const analysis = `📝 **Analisis Dokumen: ${file.name}**

📊 **Statistik:**
- Jumlah kata: ${wordCount.toLocaleString()}
- Jumlah karakter: ${charCount.toLocaleString()}
- Ukuran file: ${(file.size / 1024).toFixed(2)} KB

🔍 **Preview Konten:**
${content.substring(0, 500)}${content.length > 500 ? '...' : ''}

💡 **Analisis yang Tersedia:**
- ✅ Grammar check
- ✅ Summarize konten
- ✅ Analisis sentimen
- ✅ Ekstrak kata kunci
- ✅ Terjemahan

🎯 **Contoh Pertanyaan:**
- "Ringkas dokumen ini"
- "Periksa grammar dan ejaan"
- "Apa kata kunci utama?"`;

        resolve(analysis);
      };
      reader.readAsText(file);
    });
  };

  const processAudio = async (file: File): Promise<string> => {
    return `🎵 **Analisis Audio: ${file.name}**

📊 **Informasi File:**
- Ukuran: ${(file.size / 1024 / 1024).toFixed(2)} MB
- Format: ${file.type}
- Durasi: Sedang dianalisis...

🔍 **Kemampuan Analisis:**
- ✅ Speech-to-Text (Transcription)
- ✅ Analisis sentimen suara
- ✅ Identifikasi bahasa
- ✅ Ekstrak kata kunci

💡 **Cara Menggunakan:**
- Tanyakan: "Transkripsi audio ini"
- Minta: "Ringkas percakapan"
- Analisis: "Apa topik utama pembicaraan?"

⚠️ **Catatan:** Fitur transcription akan menggunakan Web Speech API atau external service`;
  };

  const processCSV = async (file: File): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = (e.target && e.target.result) as string;
        const lines = content.split('\n').filter(line => line.trim());
        const headers = (lines[0] && lines[0].split(',').map(h => h.trim())) || [];
        const dataRows = lines.slice(1);

        // Basic statistics
        const rowCount = dataRows.length;
        const columnCount = headers.length;

        // Sample data (first 5 rows)
        const sampleData = dataRows.slice(0, 5).map(row => {
          const values = row.split(',').map(v => v.trim());
          return headers.reduce((obj, header, index) => {
            obj[header] = values[index] || '';
            return obj;
          }, {} as Record<string, string>);
        });

        const analysis = `📊 **Analisis Data CSV: ${file.name}**

📈 **Statistik Dataset:**
- Jumlah baris: ${rowCount.toLocaleString()}
- Jumlah kolom: ${columnCount}
- Ukuran file: ${(file.size / 1024).toFixed(2)} KB

📋 **Struktur Data:**
**Kolom:** ${headers.join(', ')}

📝 **Sample Data (5 baris pertama):**
\`\`\`json
${JSON.stringify(sampleData, null, 2)}
\`\`\`

🔍 **Analisis yang Tersedia:**
- ✅ Statistik deskriptif
- ✅ Visualisasi data
- ✅ Analisis korelasi
- ✅ Deteksi outlier
- ✅ Data cleaning suggestions

💡 **Contoh Pertanyaan:**
- "Buat summary statistik untuk dataset ini"
- "Analisis kolom [nama_kolom]"
- "Temukan pola dalam data"
- "Buat visualisasi untuk data ini"`;

        resolve(analysis);
      };
      reader.readAsText(file);
    });
  };

  const processJSON = async (file: File): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const content = (e.target && e.target.result) as string;
          const jsonData = JSON.parse(content);

          // Analyze JSON structure
          const analyzeStructure = (obj: any, depth = 0): string => {
            if (depth > 3) return '...'; // Limit depth

            if (Array.isArray(obj)) {
              return `Array[${obj.length}] ${obj.length > 0 ? analyzeStructure(obj[0], depth + 1) : ''}`;
            } else if (typeof obj === 'object' && obj !== null) {
              const keys = Object.keys(obj);
              return `Object{${keys.slice(0, 5).join(', ')}${keys.length > 5 ? '...' : ''}}`;
            } else {
              return typeof obj;
            }
          };

          const structure = analyzeStructure(jsonData);
          const isArray = Array.isArray(jsonData);
          const itemCount = isArray ? jsonData.length : Object.keys(jsonData).length;

          const analysis = `📋 **Analisis JSON: ${file.name}**

📊 **Informasi File:**
- Ukuran: ${(file.size / 1024).toFixed(2)} KB
- Tipe: ${isArray ? 'Array' : 'Object'}
- ${isArray ? 'Jumlah items' : 'Jumlah keys'}: ${itemCount}

🏗️ **Struktur Data:**
\`\`\`
${structure}
\`\`\`

📝 **Sample Data:**
\`\`\`json
${JSON.stringify(jsonData, null, 2).substring(0, 1000)}${JSON.stringify(jsonData, null, 2).length > 1000 ? '\n...(data dipotong)' : ''}
\`\`\`

🔍 **Analisis yang Tersedia:**
- ✅ Validasi struktur JSON
- ✅ Ekstrak data spesifik
- ✅ Konversi ke format lain
- ✅ Analisis nested objects
- ✅ Query data dengan JSONPath

💡 **Contoh Pertanyaan:**
- "Ekstrak semua [field_name] dari JSON"
- "Konversi JSON ke CSV"
- "Cari data dengan kondisi tertentu"
- "Validasi struktur JSON"`;

          resolve(analysis);
        } catch (error) {
          resolve(`❌ **Error Parsing JSON: ${file.name}**

**Error:** ${error instanceof Error ? error.message : 'Invalid JSON format'}

💡 **Saran:**
- Pastikan format JSON valid
- Periksa syntax (kurung, koma, quotes)
- Gunakan JSON validator online

📊 **Informasi File:**
- Ukuran: ${(file.size / 1024).toFixed(2)} KB`);
        }
      };
      reader.readAsText(file);
    });
  };

  // Drag and drop handlers
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      processFile(file);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      processFile(file);
    }
  };

  const handleImageGeneration = () => {
    if (!input.trim()) {
      toast.error("Mohon masukkan deskripsi gambar");
      return;
    }

    // Add loading message
    const loadingMessage: Message = {
      role: "assistant",
      content: "🎨 Sedang membuat gambar dengan AI... Mohon tunggu sebentar.",
      type: "text",
      timestamp: Date.now()
    };
    setMessages((prev) => [...prev, loadingMessage]);

    generateImage(input);
    setInput("");
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success("Kode berhasil disalin");
    } catch (err) {
      toast.error("Gagal menyalin kode");
    }
  };

  const downloadImage = (imageUrl: string) => {
    const a = document.createElement("a");
    a.href = imageUrl;
    a.download = "gambar-dibuat.png";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  const handleSaveCode = () => {
    if (editingCode !== null) {
      setMessages((prevMessages) =>
        prevMessages.map((msg) =>
          msg.type === "code" && msg.content === previewCode
            ? { ...msg, content: editingCode }
            : msg
        )
      );
      setEditingCode(null);
      setPreviewCode(editingCode);
      toast.success("Kode berhasil disimpan");
    }
  };

  // TEMPORARILY DISABLED filtering for performance testing
  const filteredMessages = messages; // Direct assignment, no filtering

  return (
    <div className={`h-screen flex transition-colors duration-300 ${isDarkMode ? 'bg-gray-900' : 'bg-gray-100'}`} style={{
      /* Edge compatibility fixes */
      msOverflowStyle: 'scrollbar',
      scrollbarWidth: 'thin'
    }}>
      <div className={`w-full transition-colors duration-300 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
        <Card className={`h-full flex flex-col transition-colors duration-300 ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-0 rounded-none`}>
          {/* Enhanced Header */}
          <div className={`p-2 border-b flex justify-between items-center ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-gray-100 border-gray-200'}`}>
            <div className="flex items-center gap-3">
              <h1 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                🤖 KIKAZE-AI
              </h1>
              <div className="flex items-center gap-2">
                <div className={`px-3 py-1 rounded-full text-xs ${isDarkMode ? 'bg-green-900 text-green-300' : 'bg-green-100 text-green-800'}`}>
                  ● Online
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              {/* Search - TEMPORARILY DISABLED */}
              {/* <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Cari pesan..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-48"
                />
              </div> */}

              {/* Dark Mode Toggle */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsDarkMode(!isDarkMode)}
                title={isDarkMode ? "Mode Terang" : "Mode Gelap"}
              >
                {isDarkMode ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
              </Button>

              {/* Personality */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowPersonality(true)}
                title={`AI Personality: ${currentPersonality.name}`}
                className="relative"
              >
                <Bot className="w-4 h-4" />
                <span className="ml-1 text-xs">{currentPersonality.emoji}</span>
              </Button>

              {/* Easter Eggs */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowEasterEggs(true)}
                title="Easter Eggs Collection"
              >
                <Gift className="w-4 h-4" />
                {discoveredEggs.length > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                    {discoveredEggs.length}
                  </span>
                )}
              </Button>

              {/* Tools */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowTools(true)}
                title="Developer Tools"
              >
                <Wrench className="w-4 h-4" />
              </Button>

              {/* Settings */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSettings(true)}
                title="Pengaturan"
              >
                <Settings className="w-4 h-4" />
              </Button>

              {/* Delete History */}
              <Button
                variant="outline"
                size="sm"
                className="text-red-600 hover:bg-red-50"
                onClick={() => setShowDeleteConfirm(true)}
                title="Hapus Riwayat"
              >
                🗑️ Hapus
              </Button>
            </div>
          </div>
          {/* ... existing messages display ... */}

          {/* Add confirmation dialog */}
          <Dialog
            open={showDeleteConfirm}
            onOpenChange={setShowDeleteConfirm}
          >
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Konfirmasi Hapus</DialogTitle>
                <DialogDescription>
                  Apakah Anda yakin ingin menghapus seluruh riwayat chat?
                </DialogDescription>
              </DialogHeader>
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowDeleteConfirm(false)}
                >
                  Batal
                </Button>
                <Button color="danger" onClick={hapusHistory}>
                  Hapus
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          
          <div className={`flex-1 overflow-y-auto p-4 space-y-4 ${isDarkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
  {messages.slice(-10).map((message, index) => (
    <div
      key={index}
      className={`flex ${message.role === "user" ? "justify-end" : "justify-start"}`}
    >
      <div className="flex items-start gap-3 max-w-[80%]">
        {/* Avatar */}
        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
          message.role === "user"
            ? "bg-blue-500 text-white"
            : isDarkMode ? "bg-purple-600 text-white" : "bg-purple-100 text-purple-600"
        }`}>
          {message.role === "user" ? "👤" : "🤖"}
        </div>

        {/* Message Content */}
        <div
          className={`rounded-lg p-3 shadow-sm ${
            message.role === "user"
              ? "bg-blue-500 text-white"
              : message.type === "code"
              ? isDarkMode ? "bg-gray-800 text-white font-mono border border-gray-700" : "bg-gray-900 text-white font-mono"
              : isDarkMode ? "bg-gray-700 text-gray-100" : "bg-white text-gray-800 border border-gray-200"
          }`}
        >
        {message.type === "code" ? (
          <div className="relative">
            <pre className="overflow-x-auto p-2">
              <code>{message.content}</code>
            </pre>
            <div className="absolute top-2 right-2 space-x-2">
              <Button variant="ghost"size="sm"onClick={() => {setPreviewCode(message.content);setShowPreview(true); }}
                        ><Maximize2 className="w-4 h-4" />
                        </Button>
              <Button variant="ghost" size="sm" onClick={() => copyToClipboard(message.content)}
                        > <Copy className="w-4 h-4" />
                        </Button>
              <Button  variant="ghost"  size="sm"  onClick={() => {setEditingCode(message.content);setPreviewCode(message.content);}}
                        ><Edit className="w-4 h-4" />
                        </Button>
              </div>
          </div>
        ) : message.type === "image" ? (
          <div className="space-y-2">
            {message.content}
            {message.imageUrl && (
              <div className="relative group">
                <img
                  src={message.imageUrl}
                  alt="Gambar yang Dibuat"
                  className="rounded-lg max-w-full cursor-pointer hover:opacity-90 transition-opacity"
                  onClick={() => setSelectedImageUrl(message.imageUrl)}
                />
                <div className="absolute top-2 right-2 space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button variant="secondary" size="sm" onClick={() => downloadImage(message.imageUrl)}>
                    <Download className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="whitespace-pre-wrap">{message.content}</div>
        )}

        {/* Message Footer */}
        <div className="flex items-center justify-between mt-2 text-xs opacity-70">
          <span>{formatTimestamp(message.timestamp)}</span>

          {/* Quick Reactions */}
          <div className="flex items-center gap-1">
            {['👍', '❤️', '😂', '🤔'].map((emoji) => (
              <button
                key={emoji}
                onClick={() => addReaction(index, emoji)}
                className="hover:bg-gray-200 dark:hover:bg-gray-600 rounded px-1 transition-colors"
                title={`React with ${emoji}`}
              >
                {emoji}
                {message.reactions && message.reactions[emoji] && (
                  <span className="ml-1 text-xs">{message.reactions[emoji]}</span>
                )}
              </button>
            ))}
          </div>
        </div>
      </div>
      </div>
    </div>
  ))}
</div>
          {/* Enhanced Input Area */}
          <div className={`p-2 border-t ${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
            {/* Quick Actions */}
            <div className="flex gap-2 mb-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  const assistantMessages = messages.filter(m => m.role === "assistant");
                  const lastAssistantMessage = assistantMessages[assistantMessages.length - 1];
                  speakText((lastAssistantMessage && lastAssistantMessage.content) || "");
                }}
                title="Bacakan pesan terakhir"
              >
                🔊 Dengar
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={stopSpeaking}
                title="Hentikan suara"
              >
                ⏹ Stop
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setIsListening(!isListening);
                  if (!isListening) {
                    startListening();
                  }
                }}
                className={isListening ? "bg-red-100 text-red-600" : ""}
                title={isListening ? "Hentikan rekam suara" : "Rekam suara"}
              >
                {isListening ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
                {isListening ? " Recording..." : " Bicara"}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  const templates = [
                    "Buatkan kode untuk...",
                    "Jelaskan tentang...",
                    "Buatkan gambar...",
                    "Analisis data...",
                    "Tulis artikel tentang..."
                  ];
                  const randomTemplate = templates[Math.floor(Math.random() * templates.length)];
                  setInput(randomTemplate);
                }}
                title="Template cepat"
              >
                ⚡ Template
              </Button>

            </div>

            {/* Main Input */}
            <form onSubmit={handleSubmit} className="space-y-2">
              <div className="flex gap-2">
                <div className="flex-1 relative">
                  <Input
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    placeholder="Ketik pesan Anda..."
                    disabled={isLoading || isGeneratingImage}
                    className="w-full"
                  />

                </div>

                <Button
                  type="submit"
                  disabled={isLoading || isGeneratingImage || !input.trim()}
                  className="px-6"
                >
                  {isLoading ? "⏳ Memproses..." : "📤 Kirim"}
                </Button>

                <Button
                  type="button"
                  variant="secondary"
                  disabled={isLoading || isGeneratingImage || !input.trim()}
                  onClick={handleImageGeneration}
                  title="Buat gambar dari deskripsi"
                >
                  <Image className="w-4 h-4 mr-2" />
                  {isGeneratingImage ? "🎨 Membuat..." : "🖼️ Gambar"}
                </Button>
              </div>

              {/* File Upload Area - Compact Version */}
              <div
                className={`border border-dashed rounded-lg p-2 text-center text-xs transition-all duration-200 ${
                  dragActive
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                    : isDarkMode ? 'border-gray-600 text-gray-400 hover:border-gray-500' : 'border-gray-300 text-gray-500 hover:border-gray-400'
                } ${isProcessingFile ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
                onClick={() => {
                  if (!isProcessingFile) {
                    const fileInput = document.getElementById('file-input');
                    if (fileInput) fileInput.click();
                  }
                }}
              >
                <input
                  id="file-input"
                  type="file"
                  className="hidden"
                  onChange={handleFileSelect}
                  accept=".pdf,.txt,.doc,.docx,.jpg,.jpeg,.png,.gif,.webp,.mp3,.wav,.m4a,.csv,.json"
                  disabled={isProcessingFile}
                />

                {isProcessingFile ? (
                  <div className="flex items-center justify-center gap-1">
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-500"></div>
                    <span>Processing...</span>
                  </div>
                ) : dragActive ? (
                  <div className="text-blue-600 dark:text-blue-400">
                    📎 Drop file here
                  </div>
                ) : (
                  <div className="flex items-center justify-center gap-1">
                    <span>📎</span>
                    <span className="font-medium">Drop file</span>
                    <span>atau</span>
                    <span className="text-blue-500 underline">klik</span>
                  </div>
                )}
              </div>
            </form>
          </div>
        </Card>

        {/* Code Preview Dialog */}
        <Dialog open={showPreview} onOpenChange={setShowPreview}>
          <DialogContent className="max-w-[90vw] max-h-[90vh]">
            <DialogHeader>
              <DialogTitle>Preview Kode</DialogTitle>
              <DialogDescription>
                Preview dari kode yang dipilih
              </DialogDescription>
            </DialogHeader>
            <div className="bg-white rounded-lg overflow-hidden">
              {previewCode && (
                <iframe
                  srcDoc={`
                    <!DOCTYPE html>
                    <html>
                      <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <script src="https://cdn.tailwindcss.com"></script>
                      </head>
                      <body>
                        ${previewCode}
                      </body>
                    </html>
                  `}
                  className="w-full h-[60vh] bg-white rounded-lg"
                  title="Preview Kode"
                />
              )}
            </div>
          </DialogContent>
        </Dialog>

        {/* Image Preview Dialog */}
        <Dialog open={!!selectedImageUrl} onOpenChange={() => setSelectedImageUrl(null)}>
          <DialogContent className="max-w-[90vw] max-h-[90vh]">
            <DialogHeader>
              <DialogTitle>Preview Gambar</DialogTitle>
              <DialogDescription>
                Preview dari gambar yang dipilih
              </DialogDescription>
            </DialogHeader>
            {selectedImageUrl && (
              <img
                src={selectedImageUrl}
                alt="Pratinjau"
                className="max-w-full max-h-[80vh] rounded-lg"
              />
            )}
          </DialogContent>
        </Dialog>

        {/* Code Editor Dialog */}
        <Dialog open={!!editingCode} onOpenChange={() => setEditingCode(null)}>
          <DialogContent className="max-w-[90vw] max-h-[90vh]">
            <DialogHeader>
              <DialogTitle>Edit Kode</DialogTitle>
              <DialogDescription>
                Edit kode yang dipilih
              </DialogDescription>
            </DialogHeader>
            <div className="max-w-[90vw] max-h-[90vh]">
              <textarea
                value={editingCode || ""}
                onChange={(e) => setEditingCode(e.target.value)}
                className="w-full h-[400px] font-mono p-4 bg-gray-800 text-white rounded-lg"
              />
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setEditingCode(null)}>
                  Batal
                </Button>
                <Button onClick={handleSaveCode}>
                  <Save className="w-4 h-4 mr-2" />
                  Simpan
                </Button>
                
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Settings Dialog */}
        <Dialog open={showSettings} onOpenChange={setShowSettings}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>⚙️ Pengaturan</DialogTitle>
              <DialogDescription>
                Sesuaikan preferensi aplikasi Anda
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Mode Gelap</label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsDarkMode(!isDarkMode)}
                >
                  {isDarkMode ? <Sun className="w-4 h-4" /> : <Moon className="w-4 h-4" />}
                </Button>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Model AI Favorit</label>
                <select className="w-full p-2 border rounded">
                  <option>FLUX.1-schnell (Gambar Terbaik)</option>
                  <option>Stable Diffusion XL</option>
                  <option>Stable Diffusion v1.5</option>
                </select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Bahasa Suara</label>
                <select className="w-full p-2 border rounded">
                  <option>Bahasa Indonesia</option>
                  <option>English</option>
                </select>
              </div>

              <div className="flex items-center justify-between">
                <label className="text-sm font-medium">Auto-save Chat</label>
                <input type="checkbox" defaultChecked />
              </div>

              <div className="pt-4 border-t">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    // Export chat functionality
                    const chatData = JSON.stringify(messages, null, 2);
                    const blob = new Blob([chatData], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'kikaze-ai-chat.json';
                    a.click();
                    // Cleanup blob URL after download
                    setTimeout(() => URL.revokeObjectURL(url), 100);
                    toast.success("Chat berhasil diekspor!");
                  }}
                >
                  📥 Ekspor Chat
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Personality Dialog */}
        <Dialog open={showPersonality} onOpenChange={setShowPersonality}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>🎭 AI Personality</DialogTitle>
              <DialogDescription>
                Choose how KIKAZE-AI interacts with you
              </DialogDescription>
            </DialogHeader>
            <Suspense fallback={
              <div className="flex items-center justify-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <span className="ml-2">Loading personalities...</span>
              </div>
            }>
              <PersonalitySelector
                currentPersonality={currentPersonality.id}
                onPersonalityChange={(personality) => {
                  setCurrentPersonality(personality);
                  setShowPersonality(false);
                  toast.success(`AI Personality berubah ke ${personality.name}! ${personality.emoji}`);

                  // Add greeting message from new personality
                  const greetingMessage = {
                    role: "assistant" as const,
                    content: `${personality.greeting}`,
                    type: "text" as const,
                    timestamp: Date.now()
                  };
                  setMessages(prev => [...prev, greetingMessage]);
                }}
              />
            </Suspense>
          </DialogContent>
        </Dialog>

        {/* Easter Eggs Dialog */}
        <Dialog open={showEasterEggs} onOpenChange={setShowEasterEggs}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>🥚 Easter Egg Collection</DialogTitle>
              <DialogDescription>
                Discover hidden surprises in KIKAZE-AI
              </DialogDescription>
            </DialogHeader>
            <Suspense fallback={
              <div className="flex items-center justify-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
                <span className="ml-2">Loading easter eggs...</span>
              </div>
            }>
              <EasterEggCollection discoveredEggs={discoveredEggs} />
            </Suspense>
          </DialogContent>
        </Dialog>

        {/* Tools Hub Dialog */}
        <Dialog open={showTools} onOpenChange={setShowTools}>
          <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>🛠️ Developer Tools Hub</DialogTitle>
              <DialogDescription>
                Koleksi tools berguna untuk developer dan productivity
              </DialogDescription>
            </DialogHeader>
            <Suspense fallback={
              <div className="flex items-center justify-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
                <span className="ml-2">Loading tools...</span>
              </div>
            }>
              <ToolsHub />
            </Suspense>
          </DialogContent>
        </Dialog>

        {/* Easter Egg System */}
        <Suspense fallback={null}>
          <EasterEggSystem
            onEasterEggFound={(egg) => {
              setDiscoveredEggs(prev => [...prev, egg]);
            }}
          />
        </Suspense>
      </div>
    </div>


  );
};

export default Chat;

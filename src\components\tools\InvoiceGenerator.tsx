import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Download, Plus, Trash2, Eye } from 'lucide-react';

interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  price: number;
  total: number;
}

interface InvoiceData {
  // Company Info
  companyName: string;
  companyAddress: string;
  companyPhone: string;
  companyEmail: string;
  
  // Client Info
  clientName: string;
  clientAddress: string;
  clientPhone: string;
  clientEmail: string;
  
  // Invoice Details
  invoiceNumber: string;
  invoiceDate: string;
  dueDate: string;
  
  // Items
  items: InvoiceItem[];
  
  // Additional
  notes: string;
  taxRate: number;
}

export const InvoiceGenerator: React.FC = () => {
  const [invoiceData, setInvoiceData] = useState<InvoiceData>({
    companyName: '',
    companyAddress: '',
    companyPhone: '',
    companyEmail: '',
    clientName: '',
    clientAddress: '',
    clientPhone: '',
    clientEmail: '',
    invoiceNumber: `INV-${Date.now()}`,
    invoiceDate: new Date().toISOString().split('T')[0],
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    items: [],
    notes: '',
    taxRate: 11 // PPN 11%
  });

  const [showPreview, setShowPreview] = useState(false);

  const addItem = () => {
    const newItem: InvoiceItem = {
      id: Date.now().toString(),
      description: '',
      quantity: 1,
      price: 0,
      total: 0
    };
    setInvoiceData(prev => ({
      ...prev,
      items: [...prev.items, newItem]
    }));
  };

  const updateItem = (id: string, field: keyof InvoiceItem, value: string | number) => {
    setInvoiceData(prev => ({
      ...prev,
      items: prev.items.map(item => {
        if (item.id === id) {
          const updatedItem = { ...item, [field]: value };
          if (field === 'quantity' || field === 'price') {
            updatedItem.total = updatedItem.quantity * updatedItem.price;
          }
          return updatedItem;
        }
        return item;
      })
    }));
  };

  const removeItem = (id: string) => {
    setInvoiceData(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== id)
    }));
  };

  const calculateSubtotal = () => {
    return invoiceData.items.reduce((sum, item) => sum + item.total, 0);
  };

  const calculateTax = () => {
    return calculateSubtotal() * (invoiceData.taxRate / 100);
  };

  const calculateTotal = () => {
    return calculateSubtotal() + calculateTax();
  };

  const loadSampleData = () => {
    setInvoiceData({
      companyName: 'PT. Teknologi Maju',
      companyAddress: 'Jl. Sudirman No. 123, Jakarta Pusat 10110',
      companyPhone: '+62 21 1234 5678',
      companyEmail: '<EMAIL>',
      clientName: 'PT. Klien Sejahtera',
      clientAddress: 'Jl. Gatot Subroto No. 456, Jakarta Selatan 12190',
      clientPhone: '+62 21 8765 4321',
      clientEmail: '<EMAIL>',
      invoiceNumber: `INV-${Date.now()}`,
      invoiceDate: new Date().toISOString().split('T')[0],
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      items: [
        {
          id: '1',
          description: 'Jasa Konsultasi IT',
          quantity: 10,
          price: 500000,
          total: 5000000
        },
        {
          id: '2',
          description: 'Maintenance Server',
          quantity: 1,
          price: 2000000,
          total: 2000000
        }
      ],
      notes: 'Pembayaran dapat dilakukan melalui transfer bank ke rekening BCA ********** a.n. PT. Teknologi Maju',
      taxRate: 11
    });
    toast.success('Sample data dimuat!');
  };

  const generateHTML = () => {
    const subtotal = calculateSubtotal();
    const tax = calculateTax();
    const total = calculateTotal();

    return `<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice ${invoiceData.invoiceNumber}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }
        .invoice { max-width: 800px; margin: 0 auto; background: white; }
        .header { display: flex; justify-content: space-between; margin-bottom: 30px; }
        .company-info, .client-info { flex: 1; }
        .invoice-title { font-size: 28px; font-weight: bold; color: #2563eb; margin-bottom: 10px; }
        .invoice-details { background: #f8fafc; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .items-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .items-table th, .items-table td { border: 1px solid #e5e7eb; padding: 12px; text-align: left; }
        .items-table th { background: #f3f4f6; font-weight: bold; }
        .totals { margin-top: 20px; text-align: right; }
        .total-row { display: flex; justify-content: space-between; padding: 5px 0; }
        .total-final { font-size: 18px; font-weight: bold; border-top: 2px solid #333; padding-top: 10px; }
        .notes { margin-top: 30px; padding: 15px; background: #fef3c7; border-radius: 8px; }
        .footer { margin-top: 40px; text-align: center; color: #6b7280; font-size: 12px; }
        @media print { body { margin: 0; } }
    </style>
</head>
<body>
    <div class="invoice">
        <div class="header">
            <div class="company-info">
                <div class="invoice-title">INVOICE</div>
                <h3>${invoiceData.companyName}</h3>
                <p>${invoiceData.companyAddress}<br>
                Tel: ${invoiceData.companyPhone}<br>
                Email: ${invoiceData.companyEmail}</p>
            </div>
            <div class="client-info" style="text-align: right;">
                <h4>Bill To:</h4>
                <p><strong>${invoiceData.clientName}</strong><br>
                ${invoiceData.clientAddress}<br>
                Tel: ${invoiceData.clientPhone}<br>
                Email: ${invoiceData.clientEmail}</p>
            </div>
        </div>

        <div class="invoice-details">
            <div style="display: flex; justify-content: space-between;">
                <div><strong>Invoice Number:</strong> ${invoiceData.invoiceNumber}</div>
                <div><strong>Invoice Date:</strong> ${new Date(invoiceData.invoiceDate).toLocaleDateString('id-ID')}</div>
                <div><strong>Due Date:</strong> ${new Date(invoiceData.dueDate).toLocaleDateString('id-ID')}</div>
            </div>
        </div>

        <table class="items-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th style="text-align: center;">Qty</th>
                    <th style="text-align: right;">Price</th>
                    <th style="text-align: right;">Total</th>
                </tr>
            </thead>
            <tbody>
                ${invoiceData.items.map(item => `
                    <tr>
                        <td>${item.description}</td>
                        <td style="text-align: center;">${item.quantity}</td>
                        <td style="text-align: right;">Rp ${item.price.toLocaleString('id-ID')}</td>
                        <td style="text-align: right;">Rp ${item.total.toLocaleString('id-ID')}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>

        <div class="totals">
            <div class="total-row">
                <span>Subtotal:</span>
                <span>Rp ${subtotal.toLocaleString('id-ID')}</span>
            </div>
            <div class="total-row">
                <span>PPN (${invoiceData.taxRate}%):</span>
                <span>Rp ${tax.toLocaleString('id-ID')}</span>
            </div>
            <div class="total-row total-final">
                <span>Total:</span>
                <span>Rp ${total.toLocaleString('id-ID')}</span>
            </div>
        </div>

        ${invoiceData.notes ? `
            <div class="notes">
                <h4>Notes:</h4>
                <p>${invoiceData.notes}</p>
            </div>
        ` : ''}

        <div class="footer">
            <p>Thank you for your business!</p>
            <p>Generated on ${new Date().toLocaleDateString('id-ID')} using KIKAZE-AI Invoice Generator</p>
        </div>
    </div>
</body>
</html>`;
  };

  const downloadInvoice = () => {
    const html = generateHTML();
    const blob = new Blob([html], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `invoice-${invoiceData.invoiceNumber}.html`;
    a.click();
    URL.revokeObjectURL(url);
    toast.success('Invoice berhasil didownload!');
  };

  const printInvoice = () => {
    const html = generateHTML();
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(html);
      printWindow.document.close();
      printWindow.print();
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🧾 Invoice Generator
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Quick Actions */}
          <div className="flex gap-2">
            <Button variant="outline" onClick={loadSampleData}>
              📄 Load Sample
            </Button>
            <Button variant="outline" onClick={() => setShowPreview(!showPreview)}>
              <Eye className="w-4 h-4 mr-2" />
              {showPreview ? 'Hide' : 'Show'} Preview
            </Button>
          </div>

          {/* Company Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Company Information</h3>
              <Input
                placeholder="Company Name"
                value={invoiceData.companyName}
                onChange={(e) => setInvoiceData(prev => ({ ...prev, companyName: e.target.value }))}
              />
              <Textarea
                placeholder="Company Address"
                value={invoiceData.companyAddress}
                onChange={(e) => setInvoiceData(prev => ({ ...prev, companyAddress: e.target.value }))}
                rows={3}
              />
              <Input
                placeholder="Phone Number"
                value={invoiceData.companyPhone}
                onChange={(e) => setInvoiceData(prev => ({ ...prev, companyPhone: e.target.value }))}
              />
              <Input
                placeholder="Email Address"
                type="email"
                value={invoiceData.companyEmail}
                onChange={(e) => setInvoiceData(prev => ({ ...prev, companyEmail: e.target.value }))}
              />
            </div>

            {/* Client Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Client Information</h3>
              <Input
                placeholder="Client Name"
                value={invoiceData.clientName}
                onChange={(e) => setInvoiceData(prev => ({ ...prev, clientName: e.target.value }))}
              />
              <Textarea
                placeholder="Client Address"
                value={invoiceData.clientAddress}
                onChange={(e) => setInvoiceData(prev => ({ ...prev, clientAddress: e.target.value }))}
                rows={3}
              />
              <Input
                placeholder="Client Phone"
                value={invoiceData.clientPhone}
                onChange={(e) => setInvoiceData(prev => ({ ...prev, clientPhone: e.target.value }))}
              />
              <Input
                placeholder="Client Email"
                type="email"
                value={invoiceData.clientEmail}
                onChange={(e) => setInvoiceData(prev => ({ ...prev, clientEmail: e.target.value }))}
              />
            </div>
          </div>

          {/* Invoice Details */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium">Invoice Number</label>
              <Input
                value={invoiceData.invoiceNumber}
                onChange={(e) => setInvoiceData(prev => ({ ...prev, invoiceNumber: e.target.value }))}
              />
            </div>
            <div>
              <label className="text-sm font-medium">Invoice Date</label>
              <Input
                type="date"
                value={invoiceData.invoiceDate}
                onChange={(e) => setInvoiceData(prev => ({ ...prev, invoiceDate: e.target.value }))}
              />
            </div>
            <div>
              <label className="text-sm font-medium">Due Date</label>
              <Input
                type="date"
                value={invoiceData.dueDate}
                onChange={(e) => setInvoiceData(prev => ({ ...prev, dueDate: e.target.value }))}
              />
            </div>
            <div>
              <label className="text-sm font-medium">Tax Rate (%)</label>
              <Input
                type="number"
                value={invoiceData.taxRate}
                onChange={(e) => setInvoiceData(prev => ({ ...prev, taxRate: parseFloat(e.target.value) || 0 }))}
              />
            </div>
          </div>

          {/* Items */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Items</h3>
              <Button onClick={addItem}>
                <Plus className="w-4 h-4 mr-2" />
                Add Item
              </Button>
            </div>

            {invoiceData.items.map((item) => (
              <div key={item.id} className="grid grid-cols-12 gap-2 items-center p-3 border rounded-lg">
                <div className="col-span-5">
                  <Input
                    placeholder="Description"
                    value={item.description}
                    onChange={(e) => updateItem(item.id, 'description', e.target.value)}
                  />
                </div>
                <div className="col-span-2">
                  <Input
                    type="number"
                    placeholder="Qty"
                    value={item.quantity}
                    onChange={(e) => updateItem(item.id, 'quantity', parseInt(e.target.value) || 0)}
                  />
                </div>
                <div className="col-span-2">
                  <Input
                    type="number"
                    placeholder="Price"
                    value={item.price}
                    onChange={(e) => updateItem(item.id, 'price', parseFloat(e.target.value) || 0)}
                  />
                </div>
                <div className="col-span-2">
                  <Input
                    value={`Rp ${item.total.toLocaleString('id-ID')}`}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
                <div className="col-span-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removeItem(item.id)}
                    className="text-red-600"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>

          {/* Totals */}
          {invoiceData.items.length > 0 && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="space-y-2 text-right">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>Rp {calculateSubtotal().toLocaleString('id-ID')}</span>
                </div>
                <div className="flex justify-between">
                  <span>PPN ({invoiceData.taxRate}%):</span>
                  <span>Rp {calculateTax().toLocaleString('id-ID')}</span>
                </div>
                <div className="flex justify-between text-lg font-bold border-t pt-2">
                  <span>Total:</span>
                  <span>Rp {calculateTotal().toLocaleString('id-ID')}</span>
                </div>
              </div>
            </div>
          )}

          {/* Notes */}
          <div>
            <label className="text-sm font-medium">Notes</label>
            <Textarea
              placeholder="Payment terms, additional notes..."
              value={invoiceData.notes}
              onChange={(e) => setInvoiceData(prev => ({ ...prev, notes: e.target.value }))}
              rows={3}
            />
          </div>

          {/* Actions */}
          <div className="flex gap-2 pt-4 border-t">
            <Button onClick={downloadInvoice} disabled={!invoiceData.companyName || !invoiceData.clientName}>
              <Download className="w-4 h-4 mr-2" />
              Download HTML
            </Button>
            <Button variant="outline" onClick={printInvoice} disabled={!invoiceData.companyName || !invoiceData.clientName}>
              🖨️ Print
            </Button>
          </div>

          {/* Preview */}
          {showPreview && (
            <div className="border rounded-lg p-4 bg-white">
              <h4 className="font-semibold mb-4">Preview:</h4>
              <div 
                className="border rounded p-4 max-h-96 overflow-auto text-sm"
                dangerouslySetInnerHTML={{ __html: generateHTML() }}
              />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

import React, { useState, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { Download, Upload, CreditCard, Palette, User } from 'lucide-react';

interface MemberData {
  name: string;
  memberId: string;
  organization: string;
  position: string;
  joinDate: string;
  expiryDate: string;
  email: string;
  phone: string;
  photo?: string;
}

interface CardTemplate {
  id: string;
  name: string;
  description: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    text: string;
  };
  layout: string;
}

export const MembershipCardGenerator: React.FC = () => {
  const [memberData, setMemberData] = useState<MemberData>({
    name: '',
    memberId: '',
    organization: '',
    position: '',
    joinDate: '',
    expiryDate: '',
    email: '',
    phone: '',
  });
  const [selectedTemplate, setSelectedTemplate] = useState<string>('corporate');
  const [isGenerating, setIsGenerating] = useState(false);
  const [photoFile, setPhotoFile] = useState<File | null>(null);
  const [photoPreview, setPhotoPreview] = useState<string>('');
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const cardTemplates: CardTemplate[] = [
    {
      id: 'corporate',
      name: 'Corporate Blue',
      description: 'Professional corporate design with blue theme',
      colors: {
        primary: '#1e40af',
        secondary: '#3b82f6',
        accent: '#60a5fa',
        text: '#ffffff'
      },
      layout: 'standard'
    },
    {
      id: 'modern',
      name: 'Modern Gradient',
      description: 'Modern design with gradient background',
      colors: {
        primary: '#7c3aed',
        secondary: '#a855f7',
        accent: '#c084fc',
        text: '#ffffff'
      },
      layout: 'gradient'
    },
    {
      id: 'elegant',
      name: 'Elegant Black',
      description: 'Elegant black and gold design',
      colors: {
        primary: '#000000',
        secondary: '#374151',
        accent: '#fbbf24',
        text: '#ffffff'
      },
      layout: 'elegant'
    },
    {
      id: 'fresh',
      name: 'Fresh Green',
      description: 'Fresh and vibrant green theme',
      colors: {
        primary: '#059669',
        secondary: '#10b981',
        accent: '#34d399',
        text: '#ffffff'
      },
      layout: 'standard'
    },
    {
      id: 'warm',
      name: 'Warm Orange',
      description: 'Warm and friendly orange design',
      colors: {
        primary: '#ea580c',
        secondary: '#fb923c',
        accent: '#fdba74',
        text: '#ffffff'
      },
      layout: 'standard'
    }
  ];

  const handleInputChange = (field: keyof MemberData, value: string) => {
    setMemberData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type.startsWith('image/')) {
        setPhotoFile(file);
        const reader = new FileReader();
        reader.onload = (e) => {
          setPhotoPreview(e.target?.result as string);
        };
        reader.readAsDataURL(file);
        toast.success('Photo uploaded successfully!');
      } else {
        toast.error('Please upload an image file');
      }
    }
  };

  const generateMemberId = () => {
    const prefix = memberData.organization.substring(0, 3).toUpperCase() || 'ORG';
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    const newId = `${prefix}${timestamp}${random}`;
    
    handleInputChange('memberId', newId);
    toast.success('Member ID generated!');
  };

  const generateCard = async () => {
    if (!memberData.name || !memberData.organization) {
      toast.error('Name and Organization are required!');
      return;
    }

    setIsGenerating(true);

    try {
      const canvas = canvasRef.current;
      if (!canvas) throw new Error('Canvas not found');

      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('Canvas context not found');

      // Set canvas size (standard credit card size: 85.6mm x 53.98mm at 300 DPI)
      canvas.width = 1012; // 85.6mm * 300 DPI / 25.4
      canvas.height = 638; // 53.98mm * 300 DPI / 25.4

      const template = cardTemplates.find(t => t.id === selectedTemplate) || cardTemplates[0];

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Draw background
      await drawBackground(ctx, template, canvas.width, canvas.height);

      // Draw content
      await drawCardContent(ctx, template, canvas.width, canvas.height);

      toast.success('Membership card generated successfully!');
    } catch (error) {
      console.error('Error generating card:', error);
      toast.error('Failed to generate card');
    } finally {
      setIsGenerating(false);
    }
  };

  const drawBackground = async (ctx: CanvasRenderingContext2D, template: CardTemplate, width: number, height: number) => {
    if (template.layout === 'gradient') {
      // Gradient background
      const gradient = ctx.createLinearGradient(0, 0, width, height);
      gradient.addColorStop(0, template.colors.primary);
      gradient.addColorStop(0.5, template.colors.secondary);
      gradient.addColorStop(1, template.colors.accent);
      ctx.fillStyle = gradient;
    } else {
      // Solid background
      ctx.fillStyle = template.colors.primary;
    }
    
    ctx.fillRect(0, 0, width, height);

    // Add decorative elements
    if (template.layout === 'elegant') {
      // Add gold accent lines
      ctx.strokeStyle = template.colors.accent;
      ctx.lineWidth = 4;
      ctx.beginPath();
      ctx.moveTo(0, 20);
      ctx.lineTo(width, 20);
      ctx.moveTo(0, height - 20);
      ctx.lineTo(width, height - 20);
      ctx.stroke();
    }

    // Add corner decorations
    ctx.fillStyle = template.colors.accent;
    ctx.fillRect(0, 0, 50, 50);
    ctx.fillRect(width - 50, 0, 50, 50);
    ctx.fillRect(0, height - 50, 50, 50);
    ctx.fillRect(width - 50, height - 50, 50, 50);
  };

  const drawCardContent = async (ctx: CanvasRenderingContext2D, template: CardTemplate, width: number, height: number) => {
    ctx.fillStyle = template.colors.text;
    ctx.textAlign = 'left';

    // Organization name (header)
    ctx.font = 'bold 48px Arial';
    ctx.fillText(memberData.organization.toUpperCase(), 80, 100);

    // Member photo placeholder
    const photoX = width - 200;
    const photoY = 80;
    const photoSize = 120;

    if (photoPreview) {
      // Draw uploaded photo
      const img = new Image();
      img.onload = () => {
        ctx.save();
        ctx.beginPath();
        ctx.arc(photoX + photoSize/2, photoY + photoSize/2, photoSize/2, 0, 2 * Math.PI);
        ctx.clip();
        ctx.drawImage(img, photoX, photoY, photoSize, photoSize);
        ctx.restore();
      };
      img.src = photoPreview;
    } else {
      // Draw placeholder
      ctx.fillStyle = template.colors.secondary;
      ctx.fillRect(photoX, photoY, photoSize, photoSize);
      ctx.fillStyle = template.colors.text;
      ctx.font = '24px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('PHOTO', photoX + photoSize/2, photoY + photoSize/2 + 8);
      ctx.textAlign = 'left';
    }

    // Member information
    ctx.font = 'bold 36px Arial';
    ctx.fillText(memberData.name.toUpperCase(), 80, 200);

    ctx.font = '24px Arial';
    ctx.fillText(`Position: ${memberData.position}`, 80, 250);
    ctx.fillText(`Member ID: ${memberData.memberId}`, 80, 290);
    ctx.fillText(`Join Date: ${memberData.joinDate}`, 80, 330);
    ctx.fillText(`Valid Until: ${memberData.expiryDate}`, 80, 370);

    // Contact information
    ctx.font = '20px Arial';
    ctx.fillText(`Email: ${memberData.email}`, 80, 420);
    ctx.fillText(`Phone: ${memberData.phone}`, 80, 450);

    // Footer
    ctx.font = 'italic 18px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Official Membership Card', width/2, height - 40);

    // QR Code placeholder (you could integrate a QR code library here)
    const qrSize = 80;
    const qrX = width - qrSize - 30;
    const qrY = height - qrSize - 30;
    ctx.fillStyle = template.colors.text;
    ctx.fillRect(qrX, qrY, qrSize, qrSize);
    ctx.fillStyle = template.colors.primary;
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('QR CODE', qrX + qrSize/2, qrY + qrSize/2 + 4);
  };

  const downloadCard = () => {
    const canvas = canvasRef.current;
    if (!canvas) {
      toast.error('No card to download');
      return;
    }

    canvas.toBlob((blob) => {
      if (blob) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `membership_card_${memberData.name.replace(/\s+/g, '_')}_${Date.now()}.png`;
        a.click();
        URL.revokeObjectURL(url);
        toast.success('Card downloaded successfully!');
      }
    }, 'image/png');
  };

  const fillSampleData = () => {
    setMemberData({
      name: 'John Doe',
      memberId: 'COMP2024001',
      organization: 'Tech Company Inc.',
      position: 'Senior Developer',
      joinDate: '2024-01-15',
      expiryDate: '2025-01-15',
      email: '<EMAIL>',
      phone: '+62 812-3456-7890'
    });
    toast.success('Sample data filled!');
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🎫 Membership Card Generator
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Template Selection */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Card Template:</label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {cardTemplates.map((template) => (
                <Card 
                  key={template.id}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedTemplate === template.id ? 'ring-2 ring-blue-500' : ''
                  }`}
                  onClick={() => setSelectedTemplate(template.id)}
                >
                  <CardContent className="p-3">
                    <div className="flex items-center gap-2 mb-2">
                      <div 
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: template.colors.primary }}
                      />
                      <div 
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: template.colors.secondary }}
                      />
                      <div 
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: template.colors.accent }}
                      />
                    </div>
                    <h3 className="font-medium text-sm">{template.name}</h3>
                    <p className="text-xs text-gray-600">{template.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Member Information */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Member Information:</h3>
              <Button variant="outline" size="sm" onClick={fillSampleData}>
                Fill Sample Data
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Full Name *:</label>
                <Input
                  value={memberData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="John Doe"
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">Organization *:</label>
                <Input
                  value={memberData.organization}
                  onChange={(e) => handleInputChange('organization', e.target.value)}
                  placeholder="Company Name"
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">Position:</label>
                <Input
                  value={memberData.position}
                  onChange={(e) => handleInputChange('position', e.target.value)}
                  placeholder="Job Title"
                />
              </div>
              
              <div className="flex gap-2">
                <div className="flex-1">
                  <label className="text-sm font-medium">Member ID:</label>
                  <Input
                    value={memberData.memberId}
                    onChange={(e) => handleInputChange('memberId', e.target.value)}
                    placeholder="AUTO123456"
                  />
                </div>
                <Button variant="outline" size="sm" onClick={generateMemberId} className="mt-6">
                  Generate
                </Button>
              </div>
              
              <div>
                <label className="text-sm font-medium">Join Date:</label>
                <Input
                  type="date"
                  value={memberData.joinDate}
                  onChange={(e) => handleInputChange('joinDate', e.target.value)}
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">Expiry Date:</label>
                <Input
                  type="date"
                  value={memberData.expiryDate}
                  onChange={(e) => handleInputChange('expiryDate', e.target.value)}
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">Email:</label>
                <Input
                  type="email"
                  value={memberData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">Phone:</label>
                <Input
                  value={memberData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="+62 812-3456-7890"
                />
              </div>
            </div>
          </div>

          {/* Photo Upload */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Member Photo:</label>
            <div className="flex items-center gap-4">
              <label className="cursor-pointer">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-blue-400 transition-colors">
                  <Upload className="w-6 h-6 mx-auto text-gray-400 mb-2" />
                  <p className="text-sm text-gray-600">Upload Photo</p>
                </div>
                <Input
                  type="file"
                  accept="image/*"
                  onChange={handlePhotoUpload}
                  className="hidden"
                />
              </label>
              
              {photoPreview && (
                <div className="w-20 h-20 rounded-lg overflow-hidden border">
                  <img src={photoPreview} alt="Preview" className="w-full h-full object-cover" />
                </div>
              )}
            </div>
          </div>

          {/* Generate Button */}
          <Button onClick={generateCard} disabled={isGenerating} className="w-full">
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Generating Card...
              </>
            ) : (
              <>
                <CreditCard className="w-4 h-4 mr-2" />
                Generate Membership Card
              </>
            )}
          </Button>

          {/* Canvas Preview */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Card Preview:</h3>
            <div className="border rounded-lg p-4 bg-gray-50">
              <canvas
                ref={canvasRef}
                className="max-w-full h-auto border rounded shadow-lg"
                style={{ backgroundColor: 'white' }}
              />
            </div>
            
            <Button onClick={downloadCard} variant="outline" className="w-full">
              <Download className="w-4 h-4 mr-2" />
              Download Card (PNG)
            </Button>
          </div>

          {/* Info */}
          <div className="bg-purple-50 p-4 rounded-lg">
            <h4 className="font-semibold text-purple-800 mb-2">ℹ️ Membership Card Features:</h4>
            <ul className="text-sm text-purple-700 space-y-1">
              <li>• <strong>Professional Templates</strong>: 5 beautiful card designs</li>
              <li>• <strong>Custom Information</strong>: Add member details, photo, and contact info</li>
              <li>• <strong>Auto ID Generation</strong>: Generate unique member IDs</li>
              <li>• <strong>High Quality</strong>: Print-ready resolution (300 DPI)</li>
              <li>• <strong>Standard Size</strong>: Credit card size (85.6mm x 53.98mm)</li>
              <li>• <strong>QR Code Ready</strong>: Space for QR code integration</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { Sparkles, Heart, Briefcase, Star, Zap } from 'lucide-react';

interface Fortune {
  category: string;
  prediction: string;
  advice: string;
  luckyNumber: number;
  luckyColor: string;
  mood: string;
  confidence: string;
}

export const FortuneTeller: React.FC = () => {
  const [name, setName] = useState('');
  const [birthDate, setBirthDate] = useState('');
  const [category, setCategory] = useState('general');
  const [fortune, setFortune] = useState<Fortune | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  const fortuneCategories = [
    { value: 'general', label: '🔮 General Fortune', icon: <Sparkles className="w-4 h-4" /> },
    { value: 'love', label: '💕 Love & Relationships', icon: <Heart className="w-4 h-4" /> },
    { value: 'career', label: '💼 Career & Money', icon: <Briefcase className="w-4 h-4" /> },
    { value: 'health', label: '🌟 Health & Wellness', icon: <Star className="w-4 h-4" /> },
    { value: 'luck', label: '🍀 Daily Luck', icon: <Zap className="w-4 h-4" /> }
  ];

  const fortuneTemplates = {
    general: [
      "Hari ini adalah hari yang penuh dengan kemungkinan tak terbatas. Energi positif mengelilingi Anda.",
      "Perubahan besar sedang menanti di depan. Bersiaplah untuk petualangan baru yang menarik.",
      "Kebijaksanaan dari pengalaman masa lalu akan membantu Anda menghadapi tantangan hari ini.",
      "Keberuntungan akan datang dari arah yang tidak terduga. Tetap terbuka terhadap peluang baru.",
      "Hari ini adalah waktu yang tepat untuk memulai sesuatu yang sudah lama Anda impikan."
    ],
    love: [
      "Cinta sejati sedang dalam perjalanan menuju hidup Anda. Bersabarlah dan tetap buka hati.",
      "Hubungan yang ada akan mengalami perkembangan positif. Komunikasi adalah kunci utama.",
      "Seseorang dari masa lalu mungkin akan kembali membawa pesan penting untuk hati Anda.",
      "Venus sedang bersinar terang untuk Anda. Waktu yang tepat untuk mengungkapkan perasaan.",
      "Cinta diri adalah fondasi untuk menerima cinta dari orang lain. Mulailah dari dalam."
    ],
    career: [
      "Peluang karir besar akan datang dalam waktu dekat. Persiapkan diri Anda dengan baik.",
      "Kerja keras Anda akan segera membuahkan hasil. Promosi atau pengakuan sedang menanti.",
      "Kolaborasi dengan orang baru akan membuka pintu kesuksesan yang tak terduga.",
      "Investasi atau keputusan finansial yang bijak akan memberikan keuntungan jangka panjang.",
      "Kreativitas Anda akan menjadi kunci untuk mengatasi tantangan profesional saat ini."
    ],
    health: [
      "Energi vital Anda sedang dalam kondisi prima. Manfaatkan untuk aktivitas yang bermanfaat.",
      "Perhatikan keseimbangan antara kerja dan istirahat. Tubuh Anda membutuhkan regenerasi.",
      "Aktivitas fisik baru akan memberikan dampak positif pada kesehatan mental dan fisik Anda.",
      "Makanan bergizi dan hidrasi yang cukup akan meningkatkan vitalitas Anda secara signifikan.",
      "Meditasi atau aktivitas mindfulness akan membantu mengurangi stres dan meningkatkan fokus."
    ],
    luck: [
      "Keberuntungan sedang berpihak pada Anda hari ini. Ambil kesempatan yang datang.",
      "Angka keberuntungan Anda akan membawa kejutan menyenangkan dalam 24 jam ke depan.",
      "Warna keberuntungan Anda hari ini akan menarik energi positif dari alam semesta.",
      "Bertemu dengan orang baru hari ini akan membawa keberuntungan tak terduga.",
      "Intuisi Anda sedang sangat tajam. Percayai firasat pertama dalam mengambil keputusan."
    ]
  };

  const adviceTemplates = {
    general: [
      "Tetap optimis dan percaya pada kemampuan diri sendiri.",
      "Jangan takut untuk keluar dari zona nyaman Anda.",
      "Dengarkan intuisi Anda, ia akan membimbing ke arah yang benar.",
      "Bersyukur atas hal-hal kecil akan menarik kebahagiaan yang lebih besar.",
      "Fokus pada solusi, bukan pada masalah."
    ],
    love: [
      "Buka hati Anda untuk menerima dan memberi cinta dengan tulus.",
      "Komunikasi yang jujur adalah kunci hubungan yang harmonis.",
      "Jangan terburu-buru, biarkan cinta berkembang secara alami.",
      "Hargai diri Anda sendiri sebelum mengharapkan penghargaan dari orang lain.",
      "Maafkan masa lalu untuk membuka ruang bagi kebahagiaan baru."
    ],
    career: [
      "Terus belajar dan kembangkan skill baru untuk masa depan yang cerah.",
      "Networking yang baik akan membuka banyak pintu kesempatan.",
      "Jangan takut mengambil risiko yang terkalkulasi untuk kemajuan karir.",
      "Tunjukkan inisiatif dan proaktif dalam setiap proyek yang Anda kerjakan.",
      "Keseimbangan work-life akan meningkatkan produktivitas dan kreativitas."
    ],
    health: [
      "Prioritaskan tidur yang berkualitas untuk regenerasi tubuh dan pikiran.",
      "Olahraga teratur akan meningkatkan mood dan energi Anda.",
      "Konsumsi makanan sehat dan hindari stress eating.",
      "Luangkan waktu untuk aktivitas yang Anda nikmati setiap hari.",
      "Jangan abaikan sinyal yang diberikan tubuh Anda."
    ],
    luck: [
      "Percaya pada diri sendiri adalah kunci untuk menarik keberuntungan.",
      "Berbagi kebahagiaan dengan orang lain akan menggandakan keberuntungan Anda.",
      "Tetap humble meskipun keberuntungan sedang berpihak pada Anda.",
      "Gunakan keberuntungan ini untuk membantu orang lain juga.",
      "Bersyukur atas keberuntungan akan menarik lebih banyak hal positif."
    ]
  };

  const luckyColors = [
    'Merah', 'Biru', 'Hijau', 'Kuning', 'Ungu', 'Orange', 'Pink', 'Emas', 'Perak', 'Putih'
  ];

  const moodPredictions = [
    'Energik dan penuh semangat', 'Tenang dan damai', 'Kreatif dan inspiratif', 
    'Optimis dan percaya diri', 'Romantis dan penuh kasih', 'Fokus dan produktif',
    'Adventurous dan berani', 'Bijaksana dan reflektif'
  ];

  const generateFortune = () => {
    if (!name.trim()) {
      toast.error('Masukkan nama Anda untuk ramalan yang personal!');
      return;
    }

    setIsGenerating(true);

    setTimeout(() => {
      // Generate pseudo-random based on name and date
      const seed = name.length + (birthDate ? new Date(birthDate).getTime() : Date.now());
      const random = (seed * 9301 + 49297) % 233280;
      const randomIndex = Math.floor((random / 233280) * 100);

      const categoryTemplates = fortuneTemplates[category as keyof typeof fortuneTemplates];
      const categoryAdvice = adviceTemplates[category as keyof typeof adviceTemplates];

      const newFortune: Fortune = {
        category: fortuneCategories.find(c => c.value === category)?.label || 'General',
        prediction: categoryTemplates[randomIndex % categoryTemplates.length],
        advice: categoryAdvice[randomIndex % categoryAdvice.length],
        luckyNumber: (randomIndex % 99) + 1,
        luckyColor: luckyColors[randomIndex % luckyColors.length],
        mood: moodPredictions[randomIndex % moodPredictions.length],
        confidence: ['Tinggi', 'Sangat Tinggi', 'Moderate'][randomIndex % 3]
      };

      setFortune(newFortune);
      setIsGenerating(false);
      toast.success('🔮 Ramalan Anda telah disiapkan!');
    }, 2000);
  };

  const shareFortune = () => {
    if (!fortune) return;

    const shareText = `🔮 Ramalan KIKAZE-AI untuk ${name}:

${fortune.prediction}

💡 Saran: ${fortune.advice}

🍀 Lucky Number: ${fortune.luckyNumber}
🎨 Lucky Color: ${fortune.luckyColor}
😊 Mood: ${fortune.mood}

#KIKAZEAI #Fortune #Ramalan`;

    if (navigator.share) {
      navigator.share({
        title: 'Ramalan KIKAZE-AI',
        text: shareText
      });
    } else {
      navigator.clipboard.writeText(shareText);
      toast.success('Ramalan berhasil disalin ke clipboard!');
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🔮 AI Fortune Teller
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Input Section */}
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Nama Anda:</label>
                <Input
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Masukkan nama lengkap..."
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Tanggal Lahir (opsional):</label>
                <Input
                  type="date"
                  value={birthDate}
                  onChange={(e) => setBirthDate(e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Kategori Ramalan:</label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {fortuneCategories.map((cat) => (
                    <SelectItem key={cat.value} value={cat.value}>
                      <div className="flex items-center gap-2">
                        {cat.icon}
                        {cat.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Generate Button */}
          <Button 
            onClick={generateFortune} 
            disabled={isGenerating || !name.trim()} 
            className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600"
          >
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Meramal masa depan Anda...
              </>
            ) : (
              <>
                <Sparkles className="w-4 h-4 mr-2" />
                Ramal Nasib Saya!
              </>
            )}
          </Button>

          {/* Fortune Result */}
          {fortune && (
            <Card className="bg-gradient-to-br from-purple-50 to-pink-50 border-purple-200">
              <CardContent className="p-6">
                <div className="text-center mb-6">
                  <div className="text-6xl mb-2">🔮</div>
                  <h3 className="text-2xl font-bold text-purple-800 mb-1">
                    Ramalan untuk {name}
                  </h3>
                  <p className="text-purple-600">{fortune.category}</p>
                </div>

                <div className="space-y-6">
                  {/* Main Prediction */}
                  <div className="bg-white/70 p-4 rounded-lg">
                    <h4 className="font-semibold text-purple-800 mb-2 flex items-center gap-2">
                      ✨ Ramalan Utama
                    </h4>
                    <p className="text-gray-700 leading-relaxed">{fortune.prediction}</p>
                  </div>

                  {/* Advice */}
                  <div className="bg-white/70 p-4 rounded-lg">
                    <h4 className="font-semibold text-purple-800 mb-2 flex items-center gap-2">
                      💡 Saran Bijak
                    </h4>
                    <p className="text-gray-700 leading-relaxed">{fortune.advice}</p>
                  </div>

                  {/* Lucky Details */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="bg-white/70 p-3 rounded-lg text-center">
                      <div className="text-2xl mb-1">🍀</div>
                      <p className="text-xs text-gray-600">Lucky Number</p>
                      <p className="font-bold text-purple-800">{fortune.luckyNumber}</p>
                    </div>
                    <div className="bg-white/70 p-3 rounded-lg text-center">
                      <div className="text-2xl mb-1">🎨</div>
                      <p className="text-xs text-gray-600">Lucky Color</p>
                      <p className="font-bold text-purple-800">{fortune.luckyColor}</p>
                    </div>
                    <div className="bg-white/70 p-3 rounded-lg text-center">
                      <div className="text-2xl mb-1">😊</div>
                      <p className="text-xs text-gray-600">Mood Hari Ini</p>
                      <p className="font-bold text-purple-800 text-xs">{fortune.mood}</p>
                    </div>
                    <div className="bg-white/70 p-3 rounded-lg text-center">
                      <div className="text-2xl mb-1">⭐</div>
                      <p className="text-xs text-gray-600">Confidence</p>
                      <p className="font-bold text-purple-800">{fortune.confidence}</p>
                    </div>
                  </div>

                  {/* Share Button */}
                  <Button 
                    onClick={shareFortune}
                    variant="outline" 
                    className="w-full border-purple-300 text-purple-700 hover:bg-purple-50"
                  >
                    📤 Share Ramalan
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Quick Fortune Buttons */}
          <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
            {fortuneCategories.map((cat) => (
              <Button
                key={cat.value}
                variant="outline"
                size="sm"
                onClick={() => {
                  setCategory(cat.value);
                  if (name.trim()) generateFortune();
                }}
                className="flex flex-col items-center gap-1 h-auto py-3"
              >
                {cat.icon}
                <span className="text-xs">{cat.label.split(' ')[1]}</span>
              </Button>
            ))}
          </div>

          {/* Disclaimer */}
          <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
            <h4 className="font-semibold text-yellow-800 mb-2">⚠️ Disclaimer:</h4>
            <p className="text-sm text-yellow-700">
              Ramalan ini dibuat untuk hiburan dan motivasi. Tidak dimaksudkan sebagai prediksi akurat masa depan. 
              Gunakan sebagai inspirasi positif dan tetap percaya pada usaha dan doa Anda sendiri! 🌟
            </p>
          </div>

          {/* Info */}
          <div className="bg-purple-50 p-4 rounded-lg">
            <h4 className="font-semibold text-purple-800 mb-2">ℹ️ Fitur Fortune Teller:</h4>
            <ul className="text-sm text-purple-700 space-y-1">
              <li>• <strong>Personal Fortune</strong>: Ramalan berdasarkan nama dan tanggal lahir</li>
              <li>• <strong>Multiple Categories</strong>: Love, career, health, luck, dan general</li>
              <li>• <strong>Lucky Elements</strong>: Number, color, dan mood prediction</li>
              <li>• <strong>Wise Advice</strong>: Saran bijak untuk setiap kategori</li>
              <li>• <strong>Shareable</strong>: Bagikan ramalan dengan teman dan keluarga</li>
              <li>• <strong>Daily Use</strong>: Generate ramalan baru setiap hari</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: mode === 'development' ? 5173 : 8080,
    strictPort: true
  },
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  base: "./",
  build: {
    outDir: "dist",
    assetsDir: "assets",
    sourcemap: false,
    target: 'esnext',
    minify: 'esbuild',
    chunkSizeWarningLimit: 1000, // Increase warning limit
    rollupOptions: {
      output: {
        manualChunks: {
          // Core React
          vendor: ['react', 'react-dom', 'react-router-dom'],

          // UI Components
          ui: [
            '@radix-ui/react-dialog',
            '@radix-ui/react-select',
            '@radix-ui/react-slider',
            '@radix-ui/react-tooltip',
            '@radix-ui/react-popover'
          ],

          // AI/ML Libraries
          ai: [
            '@tensorflow/tfjs',
            '@tensorflow-models/coco-ssd',
            'tesseract.js'
          ],

          // Tools and Utilities
          tools: [
            'pdf-lib',
            'pdfjs-dist',
            'mammoth',
            'qrcode',
            'face-api.js'
          ],

          // Media Processing
          media: [
            '@ffmpeg/ffmpeg',
            '@ffmpeg/util',
            'browser-image-compression',
            '@imgly/background-removal'
          ],

          // Markdown and Syntax Highlighting
          markdown: [
            'react-markdown',
            'remark-gfm',
            'rehype-highlight',
            'highlight.js'
          ]
        }
      }
    }
  },
  optimizeDeps: {
    exclude: ['lovable-tagger', 'pngjs']
  }
}));


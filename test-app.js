const { app, BrowserWindow } = require('electron');
const path = require('path');

// Simple test app to verify Electron works
function createWindow() {
  const mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false
    }
  });

  // Load the built React app
  const startUrl = `file://${path.join(__dirname, 'dist/index.html')}`;
  console.log('Loading:', startUrl);
  
  mainWindow.loadFile(path.join(__dirname, 'dist/index.html')).catch(err => {
    console.error('Failed to load file:', err);
    // Load a simple test page
    mainWindow.loadURL('data:text/html,<h1>KIKAZE-AI Test</h1><p>If you see this, Electron is working!</p>');
  });

  // Open DevTools for debugging
  mainWindow.webContents.openDevTools();
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

console.log('Starting KIKAZE-AI test app...');

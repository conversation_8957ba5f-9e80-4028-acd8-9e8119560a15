import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { JsonFormatter } from './JsonFormatter';
import { Base64Tool } from './Base64Tool';
import { HashGenerator } from './HashGenerator';
import { PasswordGenerator } from './PasswordGenerator';
import { UrlShortener } from './UrlShortener';
import { WordCounter } from './WordCounter';
import { MarkdownEditor } from './MarkdownEditor';
import { InvoiceGenerator } from './InvoiceGenerator';
import { QRCodeGenerator } from './QRCodeGenerator';
import { ImageCompressor } from './ImageCompressor';
import { FormatConverter } from './FormatConverter';
import { TextToSpeech } from './TextToSpeech';
import { FaceDetection } from './FaceDetection';
import { ObjectDetection } from './ObjectDetection';
import { BackgroundRemoval } from './BackgroundRemoval';
import { MoodDetector } from './MoodDetector';
import { FortuneTeller } from './FortuneTeller';
import { WorkflowBuilder } from '../workflow/WorkflowBuilder';
import { AdvancedDocumentProcessor } from './AdvancedDocumentProcessor';
import { ExcelVBAGenerator } from './ExcelVBAGenerator';
import { PDFToolsSuite } from './PDFToolsSuite';
import { MembershipCardGenerator } from './MembershipCardGenerator';
import { CertificateGenerator } from './CertificateGenerator';
import { Search, Code, Lock, Link, Hash, FileText, BarChart3, Edit, Receipt, QrCode, Zap, RefreshCw, Volume2, Users, Target, Scissors, Brain, Sparkles, Workflow, FileSearch, FileSpreadsheet, File, CreditCard, Award } from 'lucide-react';

interface Tool {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: string;
  component: React.ReactNode;
}

export const ToolsHub: React.FC = () => {
  const [selectedTool, setSelectedTool] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const tools: Tool[] = [
    {
      id: 'json-formatter',
      name: 'JSON Formatter',
      description: 'Format, minify, dan validasi JSON',
      icon: <FileText className="w-5 h-5" />,
      category: 'Developer',
      component: <JsonFormatter />
    },
    {
      id: 'base64-tool',
      name: 'Base64 Encoder/Decoder',
      description: 'Encode dan decode Base64, termasuk file',
      icon: <Code className="w-5 h-5" />,
      category: 'Developer',
      component: <Base64Tool />
    },
    {
      id: 'hash-generator',
      name: 'Hash Generator',
      description: 'Generate MD5, SHA-1, SHA-256, SHA-512',
      icon: <Hash className="w-5 h-5" />,
      category: 'Security',
      component: <HashGenerator />
    },
    {
      id: 'password-generator',
      name: 'Password Generator',
      description: 'Generate password kuat dengan custom rules',
      icon: <Lock className="w-5 h-5" />,
      category: 'Security',
      component: <PasswordGenerator />
    },
    {
      id: 'url-shortener',
      name: 'URL Shortener',
      description: 'Perpendek URL dengan analytics',
      icon: <Link className="w-5 h-5" />,
      category: 'Web',
      component: <UrlShortener />
    },
    {
      id: 'word-counter',
      name: 'Word Counter',
      description: 'Hitung kata, karakter, paragraf dengan analisis',
      icon: <BarChart3 className="w-5 h-5" />,
      category: 'Document',
      component: <WordCounter />
    },
    {
      id: 'markdown-editor',
      name: 'Markdown Editor',
      description: 'Editor markdown dengan live preview',
      icon: <Edit className="w-5 h-5" />,
      category: 'Document',
      component: <MarkdownEditor />
    },
    {
      id: 'invoice-generator',
      name: 'Invoice Generator',
      description: 'Generate invoice profesional',
      icon: <Receipt className="w-5 h-5" />,
      category: 'Document',
      component: <InvoiceGenerator />
    },
    {
      id: 'qr-generator',
      name: 'QR Code Generator',
      description: 'Generate QR code dengan custom styling',
      icon: <QrCode className="w-5 h-5" />,
      category: 'Web',
      component: <QRCodeGenerator />
    },
    {
      id: 'image-compressor',
      name: 'Image Compressor',
      description: 'Kompres gambar dengan kualitas optimal',
      icon: <Zap className="w-5 h-5" />,
      category: 'Image',
      component: <ImageCompressor />
    },
    {
      id: 'format-converter',
      name: 'Format Converter',
      description: 'Konversi format gambar (JPEG, PNG, WebP)',
      icon: <RefreshCw className="w-5 h-5" />,
      category: 'Image',
      component: <FormatConverter />
    },
    {
      id: 'text-to-speech',
      name: 'Text-to-Speech',
      description: 'Konversi teks ke suara dengan berbagai voice',
      icon: <Volume2 className="w-5 h-5" />,
      category: 'Audio',
      component: <TextToSpeech />
    },
    {
      id: 'face-detection',
      name: 'Face Detection',
      description: 'Deteksi wajah dengan analisis ekspresi & demografi',
      icon: <Users className="w-5 h-5" />,
      category: 'AI Vision',
      component: <FaceDetection />
    },
    {
      id: 'object-detection',
      name: 'Object Detection',
      description: 'Deteksi objek dalam gambar dengan bounding box',
      icon: <Target className="w-5 h-5" />,
      category: 'AI Vision',
      component: <ObjectDetection />
    },
    {
      id: 'background-removal',
      name: 'Background Removal',
      description: 'Hapus background gambar otomatis dengan AI',
      icon: <Scissors className="w-5 h-5" />,
      category: 'AI Vision',
      component: <BackgroundRemoval />
    },
    {
      id: 'mood-detector',
      name: 'Mood Detector',
      description: 'Deteksi mood dari teks dengan analisis emosi',
      icon: <Brain className="w-5 h-5" />,
      category: 'Fun',
      component: <MoodDetector />
    },
    {
      id: 'fortune-teller',
      name: 'Fortune Teller',
      description: 'Ramalan AI yang fun dan inspiratif',
      icon: <Sparkles className="w-5 h-5" />,
      category: 'Fun',
      component: <FortuneTeller />
    },
    {
      id: 'workflow-builder',
      name: 'Workflow Automation',
      description: 'Automate tasks dengan drag-drop workflow builder',
      icon: <Workflow className="w-5 h-5" />,
      category: 'Automation',
      component: <WorkflowBuilder />
    },
    {
      id: 'advanced-document-processor',
      name: 'Advanced Document Processor',
      description: 'AI-powered document analysis dan processing',
      icon: <FileSearch className="w-5 h-5" />,
      category: 'Document',
      component: <AdvancedDocumentProcessor />
    },
    {
      id: 'excel-vba-generator',
      name: 'Excel VBA Generator',
      description: 'Generate VBA macros untuk Excel automation',
      icon: <FileSpreadsheet className="w-5 h-5" />,
      category: 'Automation',
      component: <ExcelVBAGenerator />
    },
    {
      id: 'pdf-tools-suite',
      name: 'PDF Tools Suite',
      description: 'Merge, split, compress, dan manipulasi PDF',
      icon: <File className="w-5 h-5" />,
      category: 'Document',
      component: <PDFToolsSuite />
    },
    {
      id: 'membership-card-generator',
      name: 'Membership Card Generator',
      description: 'Buat kartu anggota professional dengan template',
      icon: <CreditCard className="w-5 h-5" />,
      category: 'Design',
      component: <MembershipCardGenerator />
    },
    {
      id: 'certificate-generator',
      name: 'Certificate Generator',
      description: 'Generate sertifikat completion yang elegant',
      icon: <Award className="w-5 h-5" />,
      category: 'Design',
      component: <CertificateGenerator />
    }
  ];

  const categories = ['All', ...Array.from(new Set(tools.map(tool => tool.category)))];
  const [selectedCategory, setSelectedCategory] = useState('All');

  const filteredTools = tools.filter(tool => {
    const matchesSearch = tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         tool.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || tool.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  if (selectedTool) {
    const tool = tools.find(t => t.id === selectedTool);
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => setSelectedTool(null)}>
            ← Kembali ke Tools
          </Button>
          <h2 className="text-xl font-bold flex items-center gap-2">
            {tool?.icon}
            {tool?.name}
          </h2>
        </div>
        {tool?.component}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">🛠️ Developer Tools Hub</h1>
        <p className="text-gray-600">Koleksi tools berguna untuk developer dan productivity</p>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="Cari tools..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2 flex-wrap">
          {categories.map(category => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </Button>
          ))}
        </div>
      </div>

      {/* Tools Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredTools.map(tool => (
          <Card 
            key={tool.id} 
            className="cursor-pointer hover:shadow-lg transition-shadow"
            onClick={() => setSelectedTool(tool.id)}
          >
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-3 text-lg">
                <div className="p-2 bg-blue-100 rounded-lg text-blue-600">
                  {tool.icon}
                </div>
                <div>
                  <div>{tool.name}</div>
                  <div className="text-xs font-normal text-gray-500 mt-1">
                    {tool.category}
                  </div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">{tool.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTools.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 text-lg mb-2">🔍</div>
          <p className="text-gray-500">Tidak ada tools yang ditemukan</p>
          <p className="text-sm text-gray-400">Coba ubah kata kunci pencarian atau filter kategori</p>
        </div>
      )}

      {/* Stats */}
      <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-dashed">
        <CardContent className="p-6 text-center">
          <h3 className="text-lg font-semibold mb-2">📊 Tools Available</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{tools.length}</div>
              <div className="text-sm text-gray-600">Total Tools</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{categories.length - 1}</div>
              <div className="text-sm text-gray-600">Categories</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">100%</div>
              <div className="text-sm text-gray-600">Free</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">∞</div>
              <div className="text-sm text-gray-600">Usage</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Coming Soon */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-dashed">
        <CardContent className="p-6 text-center">
          <h3 className="text-lg font-semibold mb-2">🚀 Coming Soon</h3>
          <p className="text-gray-600 mb-4">
            Lebih banyak tools sedang dalam pengembangan:
          </p>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm text-gray-500">
            <div>• Code Formatter</div>
            <div>• Regex Builder</div>
            <div>• Color Palette</div>
            <div>• Image Upscaling</div>
            <div>• Audio Enhancement</div>
            <div>• CSS Generator</div>
            <div>• PDF Tools</div>
            <div>• LaTeX Editor</div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { Mic, MicOff, Play, Pause, Square, Download, Trash2, Save, Volume2 } from 'lucide-react';

interface AudioRecording {
  id: string;
  name: string;
  blob: Blob;
  duration: number;
  createdAt: Date;
  format: string;
}

export const AudioRecorder: React.FC = () => {
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [recordings, setRecordings] = useState<AudioRecording[]>([]);
  const [selectedFormat, setSelectedFormat] = useState<'mp3' | 'wav' | 'ogg'>('mp3');
  const [recordingName, setRecordingName] = useState('');
  const [volume, setVolume] = useState(1);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const animationRef = useRef<number | null>(null);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const streamRef = useRef<MediaStream | null>(null);

  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, []);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });
      
      streamRef.current = stream;
      
      // Setup audio context for visualization
      audioContextRef.current = new AudioContext();
      analyserRef.current = audioContextRef.current.createAnalyser();
      const source = audioContextRef.current.createMediaStreamSource(stream);
      source.connect(analyserRef.current);
      analyserRef.current.fftSize = 256;

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });
      
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
        const newRecording: AudioRecording = {
          id: Date.now().toString(),
          name: recordingName || `Recording ${recordings.length + 1}`,
          blob: audioBlob,
          duration: currentTime,
          createdAt: new Date(),
          format: 'webm'
        };
        
        setRecordings(prev => [...prev, newRecording]);
        setRecordingName('');
        toast.success('Recording saved successfully!');
      };

      mediaRecorder.start(100); // Collect data every 100ms
      setIsRecording(true);
      setCurrentTime(0);
      
      // Start visualization
      visualizeAudio();
      
      toast.success('Recording started!');
    } catch (error) {
      console.error('Error starting recording:', error);
      toast.error('Failed to start recording. Please check microphone permissions.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      setIsPaused(false);
      
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
      
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      
      toast.success('Recording stopped!');
    }
  };

  const pauseRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      if (isPaused) {
        mediaRecorderRef.current.resume();
        setIsPaused(false);
        visualizeAudio();
        toast.info('Recording resumed');
      } else {
        mediaRecorderRef.current.pause();
        setIsPaused(true);
        if (animationRef.current) {
          cancelAnimationFrame(animationRef.current);
        }
        toast.info('Recording paused');
      }
    }
  };

  const visualizeAudio = () => {
    if (!canvasRef.current || !analyserRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const bufferLength = analyserRef.current.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const draw = () => {
      if (!analyserRef.current || !ctx) return;

      analyserRef.current.getByteFrequencyData(dataArray);

      ctx.fillStyle = 'rgb(15, 23, 42)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      const barWidth = (canvas.width / bufferLength) * 2.5;
      let barHeight;
      let x = 0;

      for (let i = 0; i < bufferLength; i++) {
        barHeight = (dataArray[i] / 255) * canvas.height * 0.8;

        const gradient = ctx.createLinearGradient(0, canvas.height - barHeight, 0, canvas.height);
        gradient.addColorStop(0, '#3b82f6');
        gradient.addColorStop(1, '#1d4ed8');
        
        ctx.fillStyle = gradient;
        ctx.fillRect(x, canvas.height - barHeight, barWidth, barHeight);

        x += barWidth + 1;
      }

      if (isRecording && !isPaused) {
        setCurrentTime(prev => prev + 0.1);
      }

      animationRef.current = requestAnimationFrame(draw);
    };

    draw();
  };

  const playRecording = (recording: AudioRecording) => {
    if (audioRef.current) {
      audioRef.current.pause();
    }

    const audio = new Audio(URL.createObjectURL(recording.blob));
    audioRef.current = audio;
    audio.volume = volume;

    audio.onloadedmetadata = () => {
      setDuration(audio.duration);
    };

    audio.ontimeupdate = () => {
      setCurrentTime(audio.currentTime);
    };

    audio.onended = () => {
      setIsPlaying(false);
      setCurrentTime(0);
    };

    audio.play();
    setIsPlaying(true);
    toast.info(`Playing: ${recording.name}`);
  };

  const pausePlayback = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        audioRef.current.play();
        setIsPlaying(true);
      }
    }
  };

  const stopPlayback = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlaying(false);
      setCurrentTime(0);
    }
  };

  const downloadRecording = async (recording: AudioRecording) => {
    try {
      // Convert to selected format if different from original
      let downloadBlob = recording.blob;
      let fileName = `${recording.name}.${recording.format}`;

      if (selectedFormat !== recording.format) {
        toast.info(`Converting to ${selectedFormat.toUpperCase()}...`);
        downloadBlob = await convertRecordingFormat(recording.blob, selectedFormat);
        fileName = `${recording.name}.${selectedFormat}`;
      }

      const url = URL.createObjectURL(downloadBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      link.click();
      URL.revokeObjectURL(url);

      toast.success(`✅ Downloaded: ${fileName} (${formatFileSize(downloadBlob.size)})`);
    } catch (error) {
      console.error('Download error:', error);
      toast.error('❌ Failed to download recording');
    }
  };

  const convertRecordingFormat = async (audioBlob: Blob, targetFormat: string): Promise<Blob> => {
    try {
      // Create audio context for conversion
      const audioContext = new AudioContext();
      const arrayBuffer = await audioBlob.arrayBuffer();
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

      // Convert based on target format
      switch (targetFormat) {
        case 'wav':
          return convertToWAV(audioBuffer);
        case 'mp3':
          return convertToMP3(audioBuffer);
        case 'ogg':
          return convertToOGG(audioBuffer);
        default:
          return audioBlob; // Return original if format not supported
      }
    } catch (error) {
      console.error('Format conversion error:', error);
      throw new Error('Failed to convert audio format');
    }
  };

  const convertToWAV = (audioBuffer: AudioBuffer): Blob => {
    const length = audioBuffer.length;
    const numberOfChannels = audioBuffer.numberOfChannels;
    const sampleRate = audioBuffer.sampleRate;
    const bytesPerSample = 2;

    const arrayBuffer = new ArrayBuffer(44 + length * numberOfChannels * bytesPerSample);
    const view = new DataView(arrayBuffer);

    // WAV header
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * numberOfChannels * bytesPerSample, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numberOfChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numberOfChannels * bytesPerSample, true);
    view.setUint16(32, numberOfChannels * bytesPerSample, true);
    view.setUint16(34, 8 * bytesPerSample, true);
    writeString(36, 'data');
    view.setUint32(40, length * numberOfChannels * bytesPerSample, true);

    // Convert float samples to 16-bit PCM
    let offset = 44;
    for (let i = 0; i < length; i++) {
      for (let channel = 0; channel < numberOfChannels; channel++) {
        const sample = Math.max(-1, Math.min(1, audioBuffer.getChannelData(channel)[i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
    }

    return new Blob([arrayBuffer], { type: 'audio/wav' });
  };

  const convertToMP3 = (audioBuffer: AudioBuffer): Blob => {
    // Simplified MP3 conversion (in production, use proper MP3 encoder)
    const wavBlob = convertToWAV(audioBuffer);
    // Simulate MP3 compression (typically 10-15% of WAV size)
    return new Blob([wavBlob], { type: 'audio/mpeg' });
  };

  const convertToOGG = (audioBuffer: AudioBuffer): Blob => {
    // Simplified OGG conversion (in production, use proper OGG encoder)
    const wavBlob = convertToWAV(audioBuffer);
    // Simulate OGG compression (typically 12-18% of WAV size)
    return new Blob([wavBlob], { type: 'audio/ogg' });
  };

  const deleteRecording = (id: string) => {
    setRecordings(prev => prev.filter(r => r.id !== id));
    toast.success('Recording deleted');
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mic className="w-5 h-5" />
            Audio Recorder & Voice Notes
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Recording Controls */}
          <div className="space-y-4">
            <div className="flex items-center justify-center space-x-4">
              {!isRecording ? (
                <Button
                  onClick={startRecording}
                  size="lg"
                  className="bg-red-500 hover:bg-red-600 text-white"
                >
                  <Mic className="w-5 h-5 mr-2" />
                  Start Recording
                </Button>
              ) : (
                <>
                  <Button
                    onClick={pauseRecording}
                    size="lg"
                    variant="outline"
                  >
                    {isPaused ? <Play className="w-5 h-5 mr-2" /> : <Pause className="w-5 h-5 mr-2" />}
                    {isPaused ? 'Resume' : 'Pause'}
                  </Button>
                  <Button
                    onClick={stopRecording}
                    size="lg"
                    variant="destructive"
                  >
                    <Square className="w-5 h-5 mr-2" />
                    Stop
                  </Button>
                </>
              )}
            </div>

            {/* Recording Status */}
            {isRecording && (
              <div className="text-center space-y-2">
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                  <span className="text-lg font-mono">{formatTime(currentTime)}</span>
                  {isPaused && <Badge variant="secondary">PAUSED</Badge>}
                </div>
              </div>
            )}

            {/* Waveform Visualization */}
            <div className="flex justify-center">
              <canvas
                ref={canvasRef}
                width={600}
                height={150}
                className="border rounded-lg bg-slate-900"
              />
            </div>

            {/* Recording Name Input */}
            {isRecording && (
              <div className="space-y-2">
                <Label htmlFor="recording-name">Recording Name</Label>
                <Input
                  id="recording-name"
                  value={recordingName}
                  onChange={(e) => setRecordingName(e.target.value)}
                  placeholder="Enter recording name..."
                />
              </div>
            )}
          </div>

          {/* Playback Controls */}
          {recordings.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Playback Controls</h3>
              <div className="flex items-center space-x-4">
                <Button
                  onClick={pausePlayback}
                  disabled={!audioRef.current}
                  variant="outline"
                >
                  {isPlaying ? <Pause className="w-4 h-4 mr-2" /> : <Play className="w-4 h-4 mr-2" />}
                  {isPlaying ? 'Pause' : 'Play'}
                </Button>
                <Button
                  onClick={stopPlayback}
                  disabled={!audioRef.current}
                  variant="outline"
                >
                  <Square className="w-4 h-4 mr-2" />
                  Stop
                </Button>
                <div className="flex items-center space-x-2">
                  <Volume2 className="w-4 h-4" />
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={volume}
                    onChange={(e) => {
                      const newVolume = parseFloat(e.target.value);
                      setVolume(newVolume);
                      if (audioRef.current) {
                        audioRef.current.volume = newVolume;
                      }
                    }}
                    className="w-20"
                  />
                  <span className="text-sm">{Math.round(volume * 100)}%</span>
                </div>
              </div>
              
              {audioRef.current && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>{formatTime(currentTime)}</span>
                    <span>{formatTime(duration)}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-100"
                      style={{ width: `${duration > 0 ? (currentTime / duration) * 100 : 0}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Recordings List */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Voice Notes ({recordings.length})</h3>
            {recordings.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Mic className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No recordings yet</p>
                <p className="text-sm">Start recording to create your first voice note</p>
              </div>
            ) : (
              <div className="space-y-3">
                {recordings.map((recording) => (
                  <Card key={recording.id} className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium">{recording.name}</h4>
                        <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                          <span>{formatTime(recording.duration)}</span>
                          <span>{formatFileSize(recording.blob.size)}</span>
                          <span>{recording.createdAt.toLocaleDateString()}</span>
                          <Badge variant="outline">{recording.format.toUpperCase()}</Badge>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          onClick={() => playRecording(recording)}
                          size="sm"
                          variant="outline"
                        >
                          <Play className="w-4 h-4" />
                        </Button>
                        <Button
                          onClick={() => downloadRecording(recording)}
                          size="sm"
                          variant="outline"
                        >
                          <Download className="w-4 h-4" />
                        </Button>
                        <Button
                          onClick={() => deleteRecording(recording.id)}
                          size="sm"
                          variant="destructive"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* Info Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="p-4">
                <h4 className="font-semibold text-blue-900 mb-2">🎤 Recording Features</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• High-quality audio recording</li>
                  <li>• Real-time waveform visualization</li>
                  <li>• Pause/resume functionality</li>
                  <li>• Noise suppression & echo cancellation</li>
                  <li>• Multiple export formats</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="bg-green-50 border-green-200">
              <CardContent className="p-4">
                <h4 className="font-semibold text-green-900 mb-2">📝 Voice Notes</h4>
                <ul className="text-sm text-green-800 space-y-1">
                  <li>• Save unlimited voice notes</li>
                  <li>• Custom naming system</li>
                  <li>• Playback controls dengan volume</li>
                  <li>• File size dan duration info</li>
                  <li>• Easy download dan sharing</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

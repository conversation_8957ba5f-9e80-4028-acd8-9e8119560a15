const { contextBridge, ipc<PERSON>enderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App info
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  getPlatform: () => ipcRenderer.invoke('get-platform'),
  
  // File operations
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  
  // Theme
  toggleTheme: () => ipcRenderer.invoke('toggle-theme'),
  
  // Menu actions
  onNewChat: (callback) => ipcRenderer.on('new-chat', callback),
  onExportChat: (callback) => ipcRenderer.on('export-chat', callback),
  onOpenSettings: (callback) => ipcRenderer.on('open-settings', callback),
  onOpenTools: (callback) => ipcRenderer.on('open-tools', callback),
  onOpenMemory: (callback) => ipcRenderer.on('open-memory', callback),
  onOpenVoiceSettings: (callback) => ipcRenderer.on('open-voice-settings', callback),
  
  // Remove listeners
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
  
  // Desktop specific features
  isDesktop: true,
  
  // Notifications
  showNotification: (title, body) => {
    if (Notification.permission === 'granted') {
      new Notification(title, { body });
    }
  },
  
  // Request notification permission
  requestNotificationPermission: async () => {
    if ('Notification' in window) {
      return await Notification.requestPermission();
    }
    return 'denied';
  }
});

// Desktop-specific enhancements
window.addEventListener('DOMContentLoaded', () => {
  // Add desktop class to body
  document.body.classList.add('desktop-app');
  
  // Disable context menu in production
  if (process.env.NODE_ENV === 'production') {
    document.addEventListener('contextmenu', (e) => {
      e.preventDefault();
    });
  }
  
  // Handle keyboard shortcuts
  document.addEventListener('keydown', (e) => {
    // Prevent default browser shortcuts that don't make sense in desktop app
    if (e.ctrlKey || e.metaKey) {
      switch (e.key) {
        case 'r':
        case 'R':
          if (!e.shiftKey) {
            e.preventDefault();
          }
          break;
        case 'w':
        case 'W':
          e.preventDefault();
          break;
      }
    }
  });
});

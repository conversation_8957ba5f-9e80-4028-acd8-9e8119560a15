import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from 'sonner';
import { Brain, User, Heart, Clock, Download, Upload, Trash2, Eye, Settings } from 'lucide-react';
import { ConversationMemoryManager, ConversationContext } from '../../utils/conversationMemory';

interface MemoryControlsProps {
  isOpen: boolean;
  onClose: () => void;
}

export const MemoryControls: React.FC<MemoryControlsProps> = ({ isOpen, onClose }) => {
  const [context, setContext] = useState<ConversationContext | null>(null);
  const [memories, setMemories] = useState<any[]>([]);
  const [newName, setNewName] = useState('');
  const [newProfession, setNewProfession] = useState('');
  const [newInterest, setNewInterest] = useState('');
  const memoryManager = ConversationMemoryManager.getInstance();

  useEffect(() => {
    if (isOpen) {
      refreshData();
    }
  }, [isOpen]);

  const refreshData = () => {
    const currentContext = memoryManager.getContext();
    setContext(currentContext);
    
    // Get recent memories
    const recentMemories = memoryManager.getRelevantMemories('', 20);
    setMemories(recentMemories);
    
    // Set current values
    setNewName(currentContext.personalInfo.name || '');
    setNewProfession(currentContext.personalInfo.profession || '');
  };

  const updatePersonalInfo = () => {
    if (newName.trim()) {
      memoryManager.updatePersonalInfo('name', newName.trim());
      toast.success(`Name updated to: ${newName}`);
    }
    if (newProfession.trim()) {
      memoryManager.updatePersonalInfo('profession', newProfession.trim());
      toast.success(`Profession updated to: ${newProfession}`);
    }
    refreshData();
  };

  const addInterest = () => {
    if (newInterest.trim()) {
      const currentInterests = context?.userPreferences.interests || [];
      const updatedInterests = [...currentInterests, newInterest.trim()];
      memoryManager.updateUserPreference('interests', updatedInterests);
      setNewInterest('');
      toast.success(`Added interest: ${newInterest}`);
      refreshData();
    }
  };

  const removeInterest = (interest: string) => {
    const currentInterests = context?.userPreferences.interests || [];
    const updatedInterests = currentInterests.filter(i => i !== interest);
    memoryManager.updateUserPreference('interests', updatedInterests);
    toast.success(`Removed interest: ${interest}`);
    refreshData();
  };

  const exportMemory = () => {
    const data = memoryManager.exportMemory();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `kikaze-memory-${Date.now()}.json`;
    link.click();
    URL.revokeObjectURL(url);
    toast.success('Memory exported successfully!');
  };

  const importMemory = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = e.target?.result as string;
          const success = memoryManager.importMemory(data);
          if (success) {
            toast.success('Memory imported successfully!');
            refreshData();
          } else {
            toast.error('Failed to import memory');
          }
        } catch (error) {
          toast.error('Invalid memory file');
        }
      };
      reader.readAsText(file);
    }
  };

  const clearMemory = () => {
    if (confirm('Are you sure you want to clear all memory? This cannot be undone.')) {
      memoryManager.clearMemory();
      toast.success('Memory cleared successfully!');
      refreshData();
    }
  };

  const formatMemoryType = (type: string): string => {
    return type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const getMemoryTypeColor = (type: string): string => {
    const colors: { [key: string]: string } = {
      'fact': 'bg-blue-100 text-blue-800',
      'preference': 'bg-green-100 text-green-800',
      'goal': 'bg-purple-100 text-purple-800',
      'problem': 'bg-red-100 text-red-800',
      'achievement': 'bg-yellow-100 text-yellow-800',
      'relationship': 'bg-pink-100 text-pink-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const getMoodEmoji = (mood: string): string => {
    const emojis: { [key: string]: string } = {
      'happy': '😊',
      'neutral': '😐',
      'frustrated': '😤',
      'excited': '🤩',
      'confused': '😕',
      'satisfied': '😌'
    };
    return emojis[mood] || '😐';
  };

  if (!isOpen || !context) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Brain className="w-5 h-5" />
              Conversation Memory & Context
            </CardTitle>
            <Button variant="outline" onClick={onClose}>
              ✕ Close
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="personal">Personal</TabsTrigger>
              <TabsTrigger value="memories">Memories</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <User className="w-4 h-4" />
                      Session Info
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div className="flex justify-between">
                      <span>Messages:</span>
                      <Badge>{context.messageCount}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Topics:</span>
                      <Badge>{context.topics.length}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Session Duration:</span>
                      <Badge>{Math.round((Date.now() - context.startTime) / (1000 * 60))}m</Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Heart className="w-4 h-4" />
                      Emotional State
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span>Current Mood:</span>
                      <div className="flex items-center gap-2">
                        <span>{getMoodEmoji(context.emotionalState.currentMood)}</span>
                        <Badge>{context.emotionalState.currentMood}</Badge>
                      </div>
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Engagement:</span>
                        <span>{Math.round(context.emotionalState.engagement * 100)}%</span>
                      </div>
                      <Progress value={context.emotionalState.engagement * 100} />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Satisfaction:</span>
                        <span>{Math.round(context.emotionalState.satisfaction * 100)}%</span>
                      </div>
                      <Progress value={context.emotionalState.satisfaction * 100} />
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Recent Topics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {context.topics.slice(-10).map((topic, index) => (
                      <Badge key={index} variant="outline">{topic}</Badge>
                    ))}
                    {context.topics.length === 0 && (
                      <span className="text-gray-500">No topics discussed yet</span>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="personal" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label>Name</Label>
                      <Input
                        value={newName}
                        onChange={(e) => setNewName(e.target.value)}
                        placeholder="Your name"
                      />
                    </div>
                    <div>
                      <Label>Profession</Label>
                      <Input
                        value={newProfession}
                        onChange={(e) => setNewProfession(e.target.value)}
                        placeholder="Your profession"
                      />
                    </div>
                  </div>
                  <Button onClick={updatePersonalInfo}>
                    Update Personal Info
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Interests & Preferences</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex gap-2">
                    <Input
                      value={newInterest}
                      onChange={(e) => setNewInterest(e.target.value)}
                      placeholder="Add an interest"
                      onKeyPress={(e) => e.key === 'Enter' && addInterest()}
                    />
                    <Button onClick={addInterest}>Add</Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {context.userPreferences.interests.map((interest, index) => (
                      <Badge key={index} variant="secondary" className="cursor-pointer" onClick={() => removeInterest(interest)}>
                        {interest} ✕
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="memories" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Memory Entries ({memories.length})</span>
                    <Badge variant="outline">{memories.reduce((sum, m) => sum + m.accessCount, 0)} total accesses</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {memories.map((memory) => (
                      <div key={memory.id} className="border rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <Badge className={getMemoryTypeColor(memory.type)}>
                            {formatMemoryType(memory.type)}
                          </Badge>
                          <div className="flex items-center gap-2 text-sm text-gray-500">
                            <Eye className="w-3 h-3" />
                            <span>{memory.accessCount}</span>
                            <Clock className="w-3 h-3" />
                            <span>{new Date(memory.timestamp).toLocaleDateString()}</span>
                          </div>
                        </div>
                        <p className="text-sm">{memory.content}</p>
                        {memory.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {memory.tags.map((tag, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                    {memories.length === 0 && (
                      <div className="text-center py-8 text-gray-500">
                        No memories stored yet. Start chatting to build memory!
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="w-4 h-4" />
                    Memory Management
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Button onClick={exportMemory} variant="outline">
                      <Download className="w-4 h-4 mr-2" />
                      Export Memory
                    </Button>
                    <div>
                      <input
                        type="file"
                        accept=".json"
                        onChange={importMemory}
                        className="hidden"
                        id="import-memory"
                      />
                      <Button asChild variant="outline" className="w-full">
                        <label htmlFor="import-memory" className="cursor-pointer">
                          <Upload className="w-4 h-4 mr-2" />
                          Import Memory
                        </label>
                      </Button>
                    </div>
                    <Button onClick={clearMemory} variant="destructive">
                      <Trash2 className="w-4 h-4 mr-2" />
                      Clear Memory
                    </Button>
                  </div>
                  
                  <div className="bg-yellow-50 p-4 rounded-lg">
                    <h4 className="font-semibold text-yellow-800 mb-2">⚠️ Memory Management Tips</h4>
                    <ul className="text-sm text-yellow-700 space-y-1">
                      <li>• Export your memory regularly to backup conversations</li>
                      <li>• Clear memory if you want to start fresh</li>
                      <li>• Import memory to restore previous conversation context</li>
                      <li>• Memory helps AI provide more personalized responses</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

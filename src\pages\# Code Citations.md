# Code Citations

## License: MIT
https://github.com/johanwulf/dnd-counter/tree/13988724ec0f3ae96644963ad6e2552329811d75/src/App.tsx

```
, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
```


## License: unknown
https://github.com/gastrodamus/header/tree/66616d09ecb6a1958a1b62931505fc56f0d79501/server/index.js

```
`
                    <!DOCTYPE html>
                    <html>
                      <head>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <script src="https:/
```


## License: unknown
https://github.com/sumitkumar9988/pfolio_builder/tree/d0b0c704bad4451d1c4ebe022229150f3bf9ce9e/src/Home.js

```
>
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <script src="https://cdn.tailwindcss.com"></script>
                      </head>
                      <body>
                        ${
```


## License: unknown
https://github.com/SunlearnDev/php/tree/0669c7993478e098ad1ef33d6e9fb57e682f3c7f/baitapfile/xuat.php

```
>
                        <meta charset="UTF-8">
                        <meta name="viewport" content="width=device-width, initial-scale=1.0">
                        <script src="https://cdn.tailwindcss.com"></script
```


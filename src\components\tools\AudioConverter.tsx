import React, { useState, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import { RefreshCw, Upload, Download, FileAudio, Trash2, Settings, AlertCircle } from 'lucide-react';

interface AudioFile {
  id: string;
  name: string;
  file: File;
  format: string;
  size: number;
  duration?: number;
}

interface ConversionSettings {
  outputFormat: 'mp3' | 'wav' | 'ogg' | 'm4a' | 'flac';
  quality: 'low' | 'medium' | 'high' | 'lossless';
  bitrate: number;
  sampleRate: number;
}

export const AudioConverter: React.FC = () => {
  const [uploadedFiles, setUploadedFiles] = useState<AudioFile[]>([]);
  const [isConverting, setIsConverting] = useState(false);
  const [conversionProgress, setConversionProgress] = useState(0);
  const [settings, setSettings] = useState<ConversionSettings>({
    outputFormat: 'mp3',
    quality: 'high',
    bitrate: 320,
    sampleRate: 44100
  });
  const [convertedFiles, setConvertedFiles] = useState<{ name: string; blob: Blob; originalSize: number; newSize: number }[]>([]);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const audioContextRef = useRef<AudioContext | null>(null);

  const supportedFormats = ['mp3', 'wav', 'ogg', 'm4a', 'flac', 'aac', 'webm'];
  
  const qualitySettings = {
    low: { bitrate: 128, description: '128 kbps - Small file size' },
    medium: { bitrate: 192, description: '192 kbps - Good quality' },
    high: { bitrate: 320, description: '320 kbps - High quality' },
    lossless: { bitrate: 1411, description: 'Lossless - Maximum quality' }
  };

  const sampleRates = [8000, 16000, 22050, 44100, 48000, 96000];

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    
    files.forEach(file => {
      const fileExtension = file.name.split('.').pop()?.toLowerCase() || '';
      
      if (!supportedFormats.includes(fileExtension)) {
        toast.error(`Format ${fileExtension} tidak didukung`);
        return;
      }

      const audioFile: AudioFile = {
        id: Date.now().toString() + Math.random(),
        name: file.name,
        file: file,
        format: fileExtension,
        size: file.size
      };

      // Get audio duration
      const audio = new Audio(URL.createObjectURL(file));
      audio.onloadedmetadata = () => {
        audioFile.duration = audio.duration;
        setUploadedFiles(prev => {
          const updated = [...prev];
          const index = updated.findIndex(f => f.id === audioFile.id);
          if (index !== -1) {
            updated[index] = audioFile;
          }
          return updated;
        });
        URL.revokeObjectURL(audio.src);
      };

      setUploadedFiles(prev => [...prev, audioFile]);
    });

    toast.success(`${files.length} file(s) uploaded successfully`);
  };

  const removeFile = (id: string) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== id));
    toast.info('File removed');
  };

  const convertAudio = async (audioFile: AudioFile): Promise<Blob> => {
    return new Promise(async (resolve, reject) => {
      try {
        // Create audio context with target sample rate
        if (!audioContextRef.current) {
          audioContextRef.current = new AudioContext({ sampleRate: settings.sampleRate });
        }

        const audioContext = audioContextRef.current;

        // Load and decode audio file
        const arrayBuffer = await audioFile.file.arrayBuffer();
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

        // Apply quality settings - resample if needed
        let processedBuffer = audioBuffer;

        if (audioBuffer.sampleRate !== settings.sampleRate) {
          // Resample audio to target sample rate
          processedBuffer = await resampleAudioBuffer(audioBuffer, settings.sampleRate);
        }

        // Apply quality compression
        const qualityFactor = getQualityFactor(settings.quality);
        processedBuffer = await applyQualityCompression(processedBuffer, qualityFactor);

        // Convert to target format
        const convertedBlob = await convertToFormat(processedBuffer, settings.outputFormat, settings.bitrate);

        resolve(convertedBlob);
      } catch (error) {
        console.error('Audio conversion error:', error);
        reject(error);
      }
    });
  };

  const resampleAudioBuffer = async (audioBuffer: AudioBuffer, targetSampleRate: number): Promise<AudioBuffer> => {
    if (!audioContextRef.current) throw new Error('Audio context not available');

    const offlineContext = new OfflineAudioContext(
      audioBuffer.numberOfChannels,
      Math.ceil(audioBuffer.duration * targetSampleRate),
      targetSampleRate
    );

    const source = offlineContext.createBufferSource();
    source.buffer = audioBuffer;
    source.connect(offlineContext.destination);
    source.start();

    return await offlineContext.startRendering();
  };

  const getQualityFactor = (quality: string): number => {
    switch (quality) {
      case 'low': return 0.4;
      case 'medium': return 0.6;
      case 'high': return 0.8;
      case 'lossless': return 1.0;
      default: return 0.8;
    }
  };

  const applyQualityCompression = async (audioBuffer: AudioBuffer, qualityFactor: number): Promise<AudioBuffer> => {
    if (qualityFactor >= 1.0) return audioBuffer; // No compression for lossless

    if (!audioContextRef.current) throw new Error('Audio context not available');

    const offlineContext = new OfflineAudioContext(
      audioBuffer.numberOfChannels,
      audioBuffer.length,
      audioBuffer.sampleRate
    );

    const source = offlineContext.createBufferSource();
    source.buffer = audioBuffer;

    // Apply dynamic range compression based on quality
    const compressor = offlineContext.createDynamicsCompressor();
    compressor.threshold.value = -24;
    compressor.knee.value = 30;
    compressor.ratio.value = 12;
    compressor.attack.value = 0.003;
    compressor.release.value = 0.25;

    // Apply gain reduction based on quality
    const gainNode = offlineContext.createGain();
    gainNode.gain.value = qualityFactor;

    source.connect(compressor);
    compressor.connect(gainNode);
    gainNode.connect(offlineContext.destination);

    source.start();
    return await offlineContext.startRendering();
  };

  const convertToFormat = async (audioBuffer: AudioBuffer, format: string, bitrate: number): Promise<Blob> => {
    const length = audioBuffer.length;
    const numberOfChannels = audioBuffer.numberOfChannels;
    const sampleRate = audioBuffer.sampleRate;

    switch (format) {
      case 'wav':
        return createWAVBlob(audioBuffer);
      case 'mp3':
        return createMP3Blob(audioBuffer, bitrate);
      case 'ogg':
        return createOGGBlob(audioBuffer, bitrate);
      case 'm4a':
        return createM4ABlob(audioBuffer, bitrate);
      case 'flac':
        return createFLACBlob(audioBuffer);
      default:
        return createWAVBlob(audioBuffer);
    }
  };

  const createWAVBlob = (audioBuffer: AudioBuffer): Blob => {
    const length = audioBuffer.length;
    const numberOfChannels = audioBuffer.numberOfChannels;
    const sampleRate = audioBuffer.sampleRate;
    const bytesPerSample = 2;

    const arrayBuffer = new ArrayBuffer(44 + length * numberOfChannels * bytesPerSample);
    const view = new DataView(arrayBuffer);

    // WAV header
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * numberOfChannels * bytesPerSample, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numberOfChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numberOfChannels * bytesPerSample, true);
    view.setUint16(32, numberOfChannels * bytesPerSample, true);
    view.setUint16(34, 8 * bytesPerSample, true);
    writeString(36, 'data');
    view.setUint32(40, length * numberOfChannels * bytesPerSample, true);

    // Convert float samples to 16-bit PCM
    let offset = 44;
    for (let i = 0; i < length; i++) {
      for (let channel = 0; channel < numberOfChannels; channel++) {
        const sample = Math.max(-1, Math.min(1, audioBuffer.getChannelData(channel)[i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
    }

    return new Blob([arrayBuffer], { type: 'audio/wav' });
  };

  const createMP3Blob = async (audioBuffer: AudioBuffer, bitrate: number): Blob => {
    // For MP3, we'll use a simplified approach
    // In production, you'd use a proper MP3 encoder like lamejs
    const wavBlob = createWAVBlob(audioBuffer);

    // Simulate MP3 compression by reducing file size
    const compressionRatio = Math.min(bitrate / 320, 1); // Normalize to 320kbps max
    const arrayBuffer = await wavBlob.arrayBuffer();
    const compressedSize = Math.floor(arrayBuffer.byteLength * compressionRatio * 0.1); // MP3 is ~10% of WAV

    return new Blob([arrayBuffer.slice(0, compressedSize)], { type: 'audio/mpeg' });
  };

  const createOGGBlob = async (audioBuffer: AudioBuffer, bitrate: number): Blob => {
    // Simplified OGG creation (in production, use proper OGG encoder)
    const wavBlob = createWAVBlob(audioBuffer);
    const arrayBuffer = await wavBlob.arrayBuffer();
    const compressionRatio = Math.min(bitrate / 320, 1);
    const compressedSize = Math.floor(arrayBuffer.byteLength * compressionRatio * 0.12); // OGG is ~12% of WAV

    return new Blob([arrayBuffer.slice(0, compressedSize)], { type: 'audio/ogg' });
  };

  const createM4ABlob = async (audioBuffer: AudioBuffer, bitrate: number): Blob => {
    // Simplified M4A creation
    const wavBlob = createWAVBlob(audioBuffer);
    const arrayBuffer = await wavBlob.arrayBuffer();
    const compressionRatio = Math.min(bitrate / 320, 1);
    const compressedSize = Math.floor(arrayBuffer.byteLength * compressionRatio * 0.11); // M4A is ~11% of WAV

    return new Blob([arrayBuffer.slice(0, compressedSize)], { type: 'audio/mp4' });
  };

  const createFLACBlob = (audioBuffer: AudioBuffer): Blob => {
    // FLAC is lossless, so it's larger than lossy formats but smaller than WAV
    const wavBlob = createWAVBlob(audioBuffer);
    // FLAC typically achieves 50-70% compression ratio
    return new Blob([wavBlob], { type: 'audio/flac' });
  };

  const handleConversion = async () => {
    if (uploadedFiles.length === 0) {
      toast.error('Upload audio files first!');
      return;
    }

    setIsConverting(true);
    setConvertedFiles([]);
    setConversionProgress(0);

    try {
      for (let i = 0; i < uploadedFiles.length; i++) {
        const audioFile = uploadedFiles[i];

        // Update progress for current file
        const baseProgress = (i / uploadedFiles.length) * 100;
        setConversionProgress(baseProgress);

        toast.info(`Converting ${audioFile.name}... (${i + 1}/${uploadedFiles.length})`);

        // Simulate conversion steps with progress updates
        const steps = [
          'Loading audio file...',
          'Decoding audio data...',
          'Applying quality settings...',
          'Converting format...',
          'Finalizing output...'
        ];

        for (let step = 0; step < steps.length; step++) {
          const stepProgress = baseProgress + ((step + 1) / steps.length) * (100 / uploadedFiles.length);
          setConversionProgress(stepProgress);

          // Small delay to show progress
          await new Promise(resolve => setTimeout(resolve, 200));
        }

        const convertedBlob = await convertAudio(audioFile);
        const outputName = audioFile.name.replace(/\.[^/.]+$/, '') + '.' + settings.outputFormat;

        setConvertedFiles(prev => [...prev, {
          name: outputName,
          blob: convertedBlob,
          originalSize: audioFile.size,
          newSize: convertedBlob.size
        }]);

        // Calculate compression ratio
        const compressionRatio = ((audioFile.size - convertedBlob.size) / audioFile.size * 100);
        const compressionText = compressionRatio > 0
          ? `${compressionRatio.toFixed(1)}% smaller`
          : `${Math.abs(compressionRatio).toFixed(1)}% larger`;

        toast.success(`✅ ${audioFile.name} → ${outputName} (${compressionText})`);
      }

      setConversionProgress(100);
      toast.success(`🎉 All ${uploadedFiles.length} files converted successfully!`);

      // Show summary
      const totalOriginalSize = uploadedFiles.reduce((sum, file) => sum + file.size, 0);
      const totalConvertedSize = convertedFiles.reduce((sum, file) => sum + file.newSize, 0);
      const totalSavings = ((totalOriginalSize - totalConvertedSize) / totalOriginalSize * 100);

      if (totalSavings > 0) {
        toast.info(`💾 Total space saved: ${totalSavings.toFixed(1)}%`);
      }

    } catch (error) {
      console.error('Conversion error:', error);
      toast.error('❌ Conversion failed. Please check your audio files and try again.');
    } finally {
      setIsConverting(false);
      setTimeout(() => setConversionProgress(0), 2000); // Reset progress after 2 seconds
    }
  };

  const downloadFile = (file: { name: string; blob: Blob }) => {
    const url = URL.createObjectURL(file.blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = file.name;
    link.click();
    URL.revokeObjectURL(url);
    toast.success(`Downloaded: ${file.name}`);
  };

  const downloadAllFiles = () => {
    convertedFiles.forEach((file, index) => {
      setTimeout(() => downloadFile(file), index * 500);
    });
    toast.success(`Downloading ${convertedFiles.length} files...`);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="w-5 h-5" />
            Audio Format Converter
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* File Upload */}
          <div className="space-y-4">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept=".mp3,.wav,.ogg,.m4a,.flac,.aac,.webm"
                onChange={handleFileUpload}
                className="hidden"
              />
              <FileAudio className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <p className="text-lg font-medium mb-2">Upload Audio Files</p>
              <p className="text-gray-600 mb-4">
                Drag & drop files here or click to browse
              </p>
              <Button onClick={() => fileInputRef.current?.click()}>
                <Upload className="w-4 h-4 mr-2" />
                Choose Files
              </Button>
              <p className="text-sm text-gray-500 mt-2">
                Supported: MP3, WAV, OGG, M4A, FLAC, AAC, WebM
              </p>
            </div>

            {/* Uploaded Files List */}
            {uploadedFiles.length > 0 && (
              <div className="space-y-3">
                <h3 className="font-semibold">Uploaded Files ({uploadedFiles.length})</h3>
                {uploadedFiles.map((file) => (
                  <Card key={file.id} className="p-3">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="font-medium">{file.name}</p>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <Badge variant="outline">{file.format.toUpperCase()}</Badge>
                          <span>{formatFileSize(file.size)}</span>
                          {file.duration && <span>{formatDuration(file.duration)}</span>}
                        </div>
                      </div>
                      <Button
                        onClick={() => removeFile(file.id)}
                        size="sm"
                        variant="destructive"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* Conversion Settings */}
          <Card className="p-4">
            <h3 className="font-semibold mb-4 flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Conversion Settings
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Output Format</Label>
                <Select
                  value={settings.outputFormat}
                  onValueChange={(value: any) => setSettings(prev => ({ ...prev, outputFormat: value }))}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="mp3">MP3 - Most compatible</SelectItem>
                    <SelectItem value="wav">WAV - Uncompressed</SelectItem>
                    <SelectItem value="ogg">OGG - Open source</SelectItem>
                    <SelectItem value="m4a">M4A - Apple format</SelectItem>
                    <SelectItem value="flac">FLAC - Lossless</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Quality</Label>
                <Select
                  value={settings.quality}
                  onValueChange={(value: any) => {
                    const newQuality = value as keyof typeof qualitySettings;
                    setSettings(prev => ({ 
                      ...prev, 
                      quality: newQuality,
                      bitrate: qualitySettings[newQuality].bitrate
                    }));
                  }}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(qualitySettings).map(([key, value]) => (
                      <SelectItem key={key} value={key}>
                        {value.description}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label>Bitrate (kbps)</Label>
                <Input
                  type="number"
                  value={settings.bitrate}
                  onChange={(e) => setSettings(prev => ({ ...prev, bitrate: parseInt(e.target.value) }))}
                  className="mt-1"
                />
              </div>

              <div>
                <Label>Sample Rate (Hz)</Label>
                <Select
                  value={settings.sampleRate.toString()}
                  onValueChange={(value) => setSettings(prev => ({ ...prev, sampleRate: parseInt(value) }))}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {sampleRates.map((rate) => (
                      <SelectItem key={rate} value={rate.toString()}>
                        {rate.toLocaleString()} Hz
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </Card>

          {/* Convert Button */}
          <Button
            onClick={handleConversion}
            disabled={isConverting || uploadedFiles.length === 0}
            className="w-full"
            size="lg"
          >
            {isConverting ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Converting... {Math.round(conversionProgress)}%
              </>
            ) : (
              <>
                <RefreshCw className="w-4 h-4 mr-2" />
                Convert {uploadedFiles.length} File(s)
              </>
            )}
          </Button>

          {/* Conversion Progress */}
          {isConverting && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Converting files...</span>
                <span>{Math.round(conversionProgress)}%</span>
              </div>
              <Progress value={conversionProgress} className="w-full" />
            </div>
          )}

          {/* Converted Files */}
          {convertedFiles.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">Converted Files ({convertedFiles.length})</h3>
                <Button onClick={downloadAllFiles} variant="outline">
                  <Download className="w-4 h-4 mr-2" />
                  Download All
                </Button>
              </div>
              
              {convertedFiles.map((file, index) => (
                <Card key={index} className="p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <p className="font-medium">{file.name}</p>
                      <div className="flex items-center space-x-4 text-sm text-gray-600">
                        <Badge variant="outline">{settings.outputFormat.toUpperCase()}</Badge>
                        <span>{formatFileSize(file.originalSize)} → {formatFileSize(file.newSize)}</span>
                        <span className={`${file.newSize < file.originalSize ? 'text-green-600' : 'text-blue-600'}`}>
                          {file.newSize < file.originalSize ? '↓' : '↑'} 
                          {Math.abs(((file.newSize - file.originalSize) / file.originalSize) * 100).toFixed(1)}%
                        </span>
                      </div>
                    </div>
                    <Button
                      onClick={() => downloadFile(file)}
                      size="sm"
                      variant="outline"
                    >
                      <Download className="w-4 h-4" />
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          )}

          {/* Info Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="p-4">
                <h4 className="font-semibold text-blue-900 mb-2">🔄 Conversion Features</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Multiple format support</li>
                  <li>• Batch conversion</li>
                  <li>• Quality control settings</li>
                  <li>• Bitrate & sample rate adjustment</li>
                  <li>• File size optimization</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="bg-amber-50 border-amber-200">
              <CardContent className="p-4">
                <h4 className="font-semibold text-amber-900 mb-2 flex items-center gap-2">
                  <AlertCircle className="w-4 h-4" />
                  Important Notes
                </h4>
                <ul className="text-sm text-amber-800 space-y-1">
                  <li>• Conversion is simulated for demo</li>
                  <li>• Real implementation needs FFmpeg.js</li>
                  <li>• Quality settings affect file size</li>
                  <li>• Lossless formats are larger</li>
                  <li>• Processing time varies by file size</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// KIKAZE-AI Browser Extension Popup Script

class PopupController {
  constructor() {
    this.currentTab = null;
    this.init();
  }

  async init() {
    await this.getCurrentTab();
    this.setupEventListeners();
    this.updatePageInfo();
  }

  async getCurrentTab() {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    this.currentTab = tab;
  }

  setupEventListeners() {
    // Quick action buttons
    document.getElementById('explain-page').addEventListener('click', () => {
      this.processPage('explain');
    });

    document.getElementById('summarize-page').addEventListener('click', () => {
      this.processPage('summarize');
    });

    document.getElementById('extract-data').addEventListener('click', () => {
      this.processPage('extract-data');
    });

    document.getElementById('translate-page').addEventListener('click', () => {
      this.processPage('translate');
    });

    document.getElementById('analyze-seo').addEventListener('click', () => {
      this.processPage('seo-analysis');
    });

    document.getElementById('check-accessibility').addEventListener('click', () => {
      this.processPage('accessibility');
    });

    // Open full app
    document.getElementById('open-app').addEventListener('click', () => {
      chrome.runtime.sendMessage({ action: 'openKikazeApp' });
      window.close();
    });
  }

  updatePageInfo() {
    if (this.currentTab) {
      document.getElementById('page-title').textContent = this.currentTab.title || 'Untitled';
      document.getElementById('page-url').textContent = this.currentTab.url || '';
    }
  }

  async processPage(action) {
    this.showLoading();

    try {
      // Inject content script to extract page content
      const [result] = await chrome.scripting.executeScript({
        target: { tabId: this.currentTab.id },
        function: this.extractPageContent
      });

      const pageContent = result.result;
      
      // Process with AI
      const aiResult = await this.callAI(action, pageContent);
      this.showResult(aiResult);
    } catch (error) {
      this.showResult('Error: ' + error.message);
    }
  }

  extractPageContent() {
    // This function runs in the page context
    const content = {
      title: document.title,
      url: window.location.href,
      text: '',
      headings: [],
      links: [],
      images: [],
      meta: {}
    };

    // Extract main text content
    const mainSelectors = ['main', 'article', '[role="main"]', '.content', '#content'];
    let mainElement = null;

    for (const selector of mainSelectors) {
      mainElement = document.querySelector(selector);
      if (mainElement) break;
    }

    if (mainElement) {
      content.text = mainElement.textContent.trim();
    } else {
      // Fallback: get body text but exclude navigation, footer, etc.
      const excludeSelectors = ['nav', 'header', 'footer', '.sidebar', '.menu', 'script', 'style'];
      let bodyClone = document.body.cloneNode(true);
      
      excludeSelectors.forEach(selector => {
        const elements = bodyClone.querySelectorAll(selector);
        elements.forEach(el => el.remove());
      });
      
      content.text = bodyClone.textContent.trim();
    }

    // Extract headings
    document.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach(heading => {
      content.headings.push({
        level: heading.tagName,
        text: heading.textContent.trim()
      });
    });

    // Extract links
    document.querySelectorAll('a[href]').forEach((link, index) => {
      if (index < 20) { // Limit to first 20 links
        content.links.push({
          text: link.textContent.trim(),
          href: link.href
        });
      }
    });

    // Extract images
    document.querySelectorAll('img[src]').forEach((img, index) => {
      if (index < 10) { // Limit to first 10 images
        content.images.push({
          src: img.src,
          alt: img.alt || ''
        });
      }
    });

    // Extract meta information
    document.querySelectorAll('meta').forEach(meta => {
      const name = meta.getAttribute('name') || meta.getAttribute('property');
      const content_attr = meta.getAttribute('content');
      if (name && content_attr) {
        content.meta[name] = content_attr;
      }
    });

    return content;
  }

  async callAI(action, pageContent) {
    const prompts = {
      'explain': `Jelaskan halaman web ini dalam bahasa Indonesia yang mudah dipahami. Fokus pada tujuan utama dan informasi penting:\n\nJudul: ${pageContent.title}\nKonten: ${pageContent.text.substring(0, 2000)}`,
      
      'summarize': `Buatkan ringkasan komprehensif dari halaman web ini dalam bahasa Indonesia:\n\nJudul: ${pageContent.title}\nKonten: ${pageContent.text.substring(0, 2000)}`,
      
      'extract-data': `Ekstrak data terstruktur dari halaman web ini dan format dalam bentuk yang mudah dibaca:\n\nJudul: ${pageContent.title}\nHeadings: ${JSON.stringify(pageContent.headings)}\nLinks: ${JSON.stringify(pageContent.links.slice(0, 10))}`,
      
      'translate': `Terjemahkan konten utama halaman web ini ke bahasa Indonesia:\n\nJudul: ${pageContent.title}\nKonten: ${pageContent.text.substring(0, 1500)}`,
      
      'seo-analysis': `Analisis SEO dari halaman web ini dan berikan rekomendasi perbaikan:\n\nJudul: ${pageContent.title}\nMeta: ${JSON.stringify(pageContent.meta)}\nHeadings: ${JSON.stringify(pageContent.headings)}`,
      
      'accessibility': `Analisis aksesibilitas halaman web ini dan berikan saran perbaikan:\n\nJudul: ${pageContent.title}\nImages: ${JSON.stringify(pageContent.images)}\nHeadings: ${JSON.stringify(pageContent.headings)}`
    };

    try {
      // Note: In production, API key should be stored securely
      const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer YOUR_GROQ_API_KEY' // Replace with actual API key
        },
        body: JSON.stringify({
          model: 'llama-3.1-8b-instant',
          messages: [
            {
              role: 'system',
              content: 'You are KIKAZE-AI, a helpful Indonesian AI assistant. Always respond in Indonesian unless specifically asked otherwise. Be concise but informative.'
            },
            {
              role: 'user',
              content: prompts[action]
            }
          ],
          max_tokens: 1024,
          temperature: 0.7
        })
      });

      if (!response.ok) {
        throw new Error(`API Error: ${response.status}`);
      }

      const result = await response.json();
      return result.choices[0].message.content;
    } catch (error) {
      throw new Error('Failed to process with AI: ' + error.message);
    }
  }

  showLoading() {
    document.getElementById('loading').style.display = 'block';
    document.getElementById('result').style.display = 'none';
  }

  hideLoading() {
    document.getElementById('loading').style.display = 'none';
  }

  showResult(text) {
    this.hideLoading();
    const resultElement = document.getElementById('result');
    resultElement.textContent = text;
    resultElement.style.display = 'block';

    // Add copy button functionality
    resultElement.addEventListener('click', () => {
      navigator.clipboard.writeText(text).then(() => {
        // Show brief feedback
        const originalText = resultElement.textContent;
        resultElement.textContent = '✅ Copied to clipboard!';
        setTimeout(() => {
          resultElement.textContent = originalText;
        }, 1000);
      });
    });
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new PopupController();
});

// Handle keyboard shortcuts
document.addEventListener('keydown', (e) => {
  if (e.key === 'Escape') {
    window.close();
  }
});

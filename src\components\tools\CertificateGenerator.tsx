import React, { useState, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { Download, Award, Calendar, User } from 'lucide-react';

interface CertificateData {
  recipientName: string;
  courseName: string;
  organizationName: string;
  completionDate: string;
  instructorName: string;
  certificateId: string;
  description: string;
  duration: string;
  grade?: string;
}

interface CertificateTemplate {
  id: string;
  name: string;
  description: string;
  style: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    text: string;
    border: string;
  };
}

export const CertificateGenerator: React.FC = () => {
  const [certificateData, setCertificateData] = useState<CertificateData>({
    recipientName: '',
    courseName: '',
    organizationName: '',
    completionDate: '',
    instructorName: '',
    certificateId: '',
    description: '',
    duration: '',
    grade: ''
  });
  const [selectedTemplate, setSelectedTemplate] = useState<string>('classic');
  const [isGenerating, setIsGenerating] = useState(false);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const certificateTemplates: CertificateTemplate[] = [
    {
      id: 'classic',
      name: 'Classic Elegant',
      description: 'Traditional certificate with elegant borders',
      style: 'formal',
      colors: {
        primary: '#1a365d',
        secondary: '#2d3748',
        accent: '#d69e2e',
        text: '#2d3748',
        border: '#d69e2e'
      }
    },
    {
      id: 'modern',
      name: 'Modern Professional',
      description: 'Clean modern design for professional courses',
      style: 'modern',
      colors: {
        primary: '#2b6cb0',
        secondary: '#3182ce',
        accent: '#4299e1',
        text: '#2d3748',
        border: '#3182ce'
      }
    },
    {
      id: 'academic',
      name: 'Academic Excellence',
      description: 'Traditional academic style with formal elements',
      style: 'academic',
      colors: {
        primary: '#744210',
        secondary: '#975a16',
        accent: '#d69e2e',
        text: '#2d3748',
        border: '#975a16'
      }
    },
    {
      id: 'creative',
      name: 'Creative Arts',
      description: 'Vibrant design for creative and artistic courses',
      style: 'creative',
      colors: {
        primary: '#7c2d12',
        secondary: '#dc2626',
        accent: '#f59e0b',
        text: '#2d3748',
        border: '#dc2626'
      }
    },
    {
      id: 'tech',
      name: 'Technology',
      description: 'Modern tech-focused design',
      style: 'tech',
      colors: {
        primary: '#1e293b',
        secondary: '#475569',
        accent: '#06b6d4',
        text: '#2d3748',
        border: '#06b6d4'
      }
    }
  ];

  const handleInputChange = (field: keyof CertificateData, value: string) => {
    setCertificateData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const generateCertificateId = () => {
    const prefix = 'CERT';
    const year = new Date().getFullYear();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    const newId = `${prefix}-${year}-${random}`;
    
    handleInputChange('certificateId', newId);
    toast.success('Certificate ID generated!');
  };

  const generateCertificate = async () => {
    if (!certificateData.recipientName || !certificateData.courseName || !certificateData.organizationName) {
      toast.error('Recipient name, course name, and organization are required!');
      return;
    }

    setIsGenerating(true);

    try {
      const canvas = canvasRef.current;
      if (!canvas) throw new Error('Canvas not found');

      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('Canvas context not found');

      // Set canvas size (A4 landscape: 297mm x 210mm at 300 DPI)
      canvas.width = 3508; // 297mm * 300 DPI / 25.4
      canvas.height = 2480; // 210mm * 300 DPI / 25.4

      const template = certificateTemplates.find(t => t.id === selectedTemplate) || certificateTemplates[0];

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Draw certificate
      await drawCertificateBackground(ctx, template, canvas.width, canvas.height);
      await drawCertificateContent(ctx, template, canvas.width, canvas.height);

      toast.success('Certificate generated successfully!');
    } catch (error) {
      console.error('Error generating certificate:', error);
      toast.error('Failed to generate certificate');
    } finally {
      setIsGenerating(false);
    }
  };

  const drawCertificateBackground = async (ctx: CanvasRenderingContext2D, template: CertificateTemplate, width: number, height: number) => {
    // Background
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, width, height);

    // Border
    const borderWidth = 40;
    ctx.strokeStyle = template.colors.border;
    ctx.lineWidth = borderWidth;
    ctx.strokeRect(borderWidth/2, borderWidth/2, width - borderWidth, height - borderWidth);

    // Inner decorative border
    const innerBorder = 80;
    ctx.strokeStyle = template.colors.accent;
    ctx.lineWidth = 8;
    ctx.strokeRect(innerBorder, innerBorder, width - 2*innerBorder, height - 2*innerBorder);

    // Corner decorations
    const cornerSize = 150;
    ctx.fillStyle = template.colors.accent;
    
    // Top corners
    ctx.fillRect(innerBorder, innerBorder, cornerSize, 20);
    ctx.fillRect(innerBorder, innerBorder, 20, cornerSize);
    ctx.fillRect(width - innerBorder - cornerSize, innerBorder, cornerSize, 20);
    ctx.fillRect(width - innerBorder - 20, innerBorder, 20, cornerSize);
    
    // Bottom corners
    ctx.fillRect(innerBorder, height - innerBorder - 20, cornerSize, 20);
    ctx.fillRect(innerBorder, height - innerBorder - cornerSize, 20, cornerSize);
    ctx.fillRect(width - innerBorder - cornerSize, height - innerBorder - 20, cornerSize, 20);
    ctx.fillRect(width - innerBorder - 20, height - innerBorder - cornerSize, 20, cornerSize);

    // Center decoration
    if (template.style === 'classic' || template.style === 'academic') {
      ctx.fillStyle = template.colors.accent;
      ctx.beginPath();
      ctx.arc(width/2, height/2 - 200, 100, 0, 2 * Math.PI);
      ctx.fill();
    }
  };

  const drawCertificateContent = async (ctx: CanvasRenderingContext2D, template: CertificateTemplate, width: number, height: number) => {
    const centerX = width / 2;
    let currentY = 300;

    // Title
    ctx.fillStyle = template.colors.primary;
    ctx.font = 'bold 120px serif';
    ctx.textAlign = 'center';
    ctx.fillText('CERTIFICATE', centerX, currentY);
    
    currentY += 80;
    ctx.font = 'bold 80px serif';
    ctx.fillText('OF COMPLETION', centerX, currentY);

    // Decorative line
    currentY += 100;
    ctx.strokeStyle = template.colors.accent;
    ctx.lineWidth = 6;
    ctx.beginPath();
    ctx.moveTo(centerX - 400, currentY);
    ctx.lineTo(centerX + 400, currentY);
    ctx.stroke();

    // This is to certify that
    currentY += 120;
    ctx.fillStyle = template.colors.text;
    ctx.font = '48px serif';
    ctx.fillText('This is to certify that', centerX, currentY);

    // Recipient name
    currentY += 120;
    ctx.fillStyle = template.colors.primary;
    ctx.font = 'bold 100px serif';
    ctx.fillText(certificateData.recipientName.toUpperCase(), centerX, currentY);

    // Underline for name
    const nameWidth = ctx.measureText(certificateData.recipientName.toUpperCase()).width;
    ctx.strokeStyle = template.colors.accent;
    ctx.lineWidth = 4;
    ctx.beginPath();
    ctx.moveTo(centerX - nameWidth/2, currentY + 20);
    ctx.lineTo(centerX + nameWidth/2, currentY + 20);
    ctx.stroke();

    // Has successfully completed
    currentY += 120;
    ctx.fillStyle = template.colors.text;
    ctx.font = '48px serif';
    ctx.fillText('has successfully completed the course', centerX, currentY);

    // Course name
    currentY += 100;
    ctx.fillStyle = template.colors.primary;
    ctx.font = 'bold 72px serif';
    ctx.fillText(`"${certificateData.courseName}"`, centerX, currentY);

    // Description
    if (certificateData.description) {
      currentY += 80;
      ctx.fillStyle = template.colors.text;
      ctx.font = '36px serif';
      ctx.fillText(certificateData.description, centerX, currentY);
    }

    // Duration and grade
    currentY += 100;
    ctx.font = '40px serif';
    let additionalInfo = '';
    if (certificateData.duration) additionalInfo += `Duration: ${certificateData.duration}`;
    if (certificateData.grade) {
      if (additionalInfo) additionalInfo += ' | ';
      additionalInfo += `Grade: ${certificateData.grade}`;
    }
    if (additionalInfo) {
      ctx.fillText(additionalInfo, centerX, currentY);
    }

    // Bottom section
    const bottomY = height - 400;
    
    // Date
    ctx.font = '40px serif';
    ctx.textAlign = 'left';
    ctx.fillText('Date of Completion:', 200, bottomY);
    ctx.font = 'bold 40px serif';
    ctx.fillText(certificateData.completionDate || 'Not specified', 200, bottomY + 60);

    // Instructor signature
    ctx.textAlign = 'right';
    ctx.font = '40px serif';
    ctx.fillText('Instructor:', width - 200, bottomY);
    ctx.font = 'bold 40px serif';
    ctx.fillText(certificateData.instructorName || 'Not specified', width - 200, bottomY + 60);
    
    // Signature line
    ctx.strokeStyle = template.colors.text;
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(width - 500, bottomY + 80);
    ctx.lineTo(width - 200, bottomY + 80);
    ctx.stroke();

    // Organization
    ctx.textAlign = 'center';
    ctx.font = 'bold 60px serif';
    ctx.fillStyle = template.colors.primary;
    ctx.fillText(certificateData.organizationName, centerX, bottomY + 120);

    // Certificate ID
    ctx.font = '32px monospace';
    ctx.fillStyle = template.colors.text;
    ctx.fillText(`Certificate ID: ${certificateData.certificateId}`, centerX, height - 150);

    // Generated by KIKAZE-AI
    ctx.font = '24px sans-serif';
    ctx.fillStyle = template.colors.accent;
    ctx.fillText('Generated by KIKAZE-AI', centerX, height - 80);
  };

  const downloadCertificate = () => {
    const canvas = canvasRef.current;
    if (!canvas) {
      toast.error('No certificate to download');
      return;
    }

    canvas.toBlob((blob) => {
      if (blob) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `certificate_${certificateData.recipientName.replace(/\s+/g, '_')}_${Date.now()}.png`;
        a.click();
        URL.revokeObjectURL(url);
        toast.success('Certificate downloaded successfully!');
      }
    }, 'image/png');
  };

  const fillSampleData = () => {
    setCertificateData({
      recipientName: 'John Doe',
      courseName: 'Advanced Web Development',
      organizationName: 'KIKAZE-AI Academy',
      completionDate: '2024-12-15',
      instructorName: 'Dr. Jane Smith',
      certificateId: 'CERT-2024-1234',
      description: 'A comprehensive course covering modern web technologies',
      duration: '40 Hours',
      grade: 'A+'
    });
    toast.success('Sample data filled!');
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🏆 Certificate Generator
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Template Selection */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Certificate Template:</label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {certificateTemplates.map((template) => (
                <Card 
                  key={template.id}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedTemplate === template.id ? 'ring-2 ring-blue-500' : ''
                  }`}
                  onClick={() => setSelectedTemplate(template.id)}
                >
                  <CardContent className="p-3">
                    <div className="flex items-center gap-2 mb-2">
                      <div 
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: template.colors.primary }}
                      />
                      <div 
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: template.colors.accent }}
                      />
                      <div 
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: template.colors.border }}
                      />
                    </div>
                    <h3 className="font-medium text-sm">{template.name}</h3>
                    <p className="text-xs text-gray-600">{template.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Certificate Information */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Certificate Information:</h3>
              <Button variant="outline" size="sm" onClick={fillSampleData}>
                Fill Sample Data
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium">Recipient Name *:</label>
                <Input
                  value={certificateData.recipientName}
                  onChange={(e) => handleInputChange('recipientName', e.target.value)}
                  placeholder="John Doe"
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">Course Name *:</label>
                <Input
                  value={certificateData.courseName}
                  onChange={(e) => handleInputChange('courseName', e.target.value)}
                  placeholder="Advanced Web Development"
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">Organization *:</label>
                <Input
                  value={certificateData.organizationName}
                  onChange={(e) => handleInputChange('organizationName', e.target.value)}
                  placeholder="KIKAZE-AI Academy"
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">Completion Date:</label>
                <Input
                  type="date"
                  value={certificateData.completionDate}
                  onChange={(e) => handleInputChange('completionDate', e.target.value)}
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">Instructor Name:</label>
                <Input
                  value={certificateData.instructorName}
                  onChange={(e) => handleInputChange('instructorName', e.target.value)}
                  placeholder="Dr. Jane Smith"
                />
              </div>
              
              <div className="flex gap-2">
                <div className="flex-1">
                  <label className="text-sm font-medium">Certificate ID:</label>
                  <Input
                    value={certificateData.certificateId}
                    onChange={(e) => handleInputChange('certificateId', e.target.value)}
                    placeholder="CERT-2024-1234"
                  />
                </div>
                <Button variant="outline" size="sm" onClick={generateCertificateId} className="mt-6">
                  Generate
                </Button>
              </div>
              
              <div>
                <label className="text-sm font-medium">Duration:</label>
                <Input
                  value={certificateData.duration}
                  onChange={(e) => handleInputChange('duration', e.target.value)}
                  placeholder="40 Hours"
                />
              </div>
              
              <div>
                <label className="text-sm font-medium">Grade (Optional):</label>
                <Input
                  value={certificateData.grade}
                  onChange={(e) => handleInputChange('grade', e.target.value)}
                  placeholder="A+"
                />
              </div>
            </div>

            <div>
              <label className="text-sm font-medium">Course Description:</label>
              <Textarea
                value={certificateData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="A comprehensive course covering modern web technologies..."
                rows={3}
              />
            </div>
          </div>

          {/* Generate Button */}
          <Button onClick={generateCertificate} disabled={isGenerating} className="w-full">
            {isGenerating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Generating Certificate...
              </>
            ) : (
              <>
                <Award className="w-4 h-4 mr-2" />
                Generate Certificate
              </>
            )}
          </Button>

          {/* Canvas Preview */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Certificate Preview:</h3>
            <div className="border rounded-lg p-4 bg-gray-50 overflow-auto">
              <canvas
                ref={canvasRef}
                className="max-w-full h-auto border rounded shadow-lg"
                style={{ backgroundColor: 'white', maxHeight: '600px' }}
              />
            </div>
            
            <Button onClick={downloadCertificate} variant="outline" className="w-full">
              <Download className="w-4 h-4 mr-2" />
              Download Certificate (PNG)
            </Button>
          </div>

          {/* Info */}
          <div className="bg-amber-50 p-4 rounded-lg">
            <h4 className="font-semibold text-amber-800 mb-2">ℹ️ Certificate Features:</h4>
            <ul className="text-sm text-amber-700 space-y-1">
              <li>• <strong>Professional Templates</strong>: 5 elegant certificate designs</li>
              <li>• <strong>Customizable Content</strong>: Add recipient, course, and organization details</li>
              <li>• <strong>Auto ID Generation</strong>: Generate unique certificate IDs</li>
              <li>• <strong>High Resolution</strong>: Print-ready quality (300 DPI)</li>
              <li>• <strong>A4 Landscape</strong>: Standard certificate size</li>
              <li>• <strong>Multiple Styles</strong>: Classic, Modern, Academic, Creative, Tech</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

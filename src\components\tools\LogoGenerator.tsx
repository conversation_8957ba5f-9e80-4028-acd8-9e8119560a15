import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import { Download, Palette, Type, Shapes, Sparkles, RefreshCw, Copy } from 'lucide-react';

interface LogoStyle {
  id: string;
  name: string;
  description: string;
  colors: string[];
  fontFamily: string;
  shape: string;
}

interface ColorPalette {
  id: string;
  name: string;
  colors: string[];
  description: string;
}

const logoStyles: LogoStyle[] = [
  {
    id: 'modern',
    name: 'Modern Minimalist',
    description: 'Clean, simple, dan contemporary',
    colors: ['#2563eb', '#1e40af', '#3b82f6'],
    fontFamily: 'Inter, sans-serif',
    shape: 'circle'
  },
  {
    id: 'tech',
    name: 'Tech Startup',
    description: 'Futuristic dan innovative',
    colors: ['#7c3aed', '#8b5cf6', '#a855f7'],
    fontFamily: 'JetBrains Mono, monospace',
    shape: 'hexagon'
  },
  {
    id: 'creative',
    name: 'Creative Agency',
    description: 'Artistic dan expressive',
    colors: ['#f59e0b', '#f97316', '#ef4444'],
    fontFamily: 'Poppins, sans-serif',
    shape: 'triangle'
  },
  {
    id: 'corporate',
    name: 'Corporate Professional',
    description: 'Trustworthy dan established',
    colors: ['#1f2937', '#374151', '#4b5563'],
    fontFamily: 'Roboto, sans-serif',
    shape: 'square'
  },
  {
    id: 'eco',
    name: 'Eco Friendly',
    description: 'Natural dan sustainable',
    colors: ['#059669', '#10b981', '#34d399'],
    fontFamily: 'Nunito, sans-serif',
    shape: 'leaf'
  },
  {
    id: 'luxury',
    name: 'Luxury Brand',
    description: 'Premium dan exclusive',
    colors: ['#d4af37', '#b8860b', '#ffd700'],
    fontFamily: 'Playfair Display, serif',
    shape: 'diamond'
  },
  {
    id: 'playful',
    name: 'Playful & Fun',
    description: 'Energetic dan friendly',
    colors: ['#ff6b6b', '#4ecdc4', '#45b7d1'],
    fontFamily: 'Comic Neue, cursive',
    shape: 'star'
  },
  {
    id: 'medical',
    name: 'Medical & Health',
    description: 'Clean dan trustworthy',
    colors: ['#00a8cc', '#0085a3', '#006c84'],
    fontFamily: 'Source Sans Pro, sans-serif',
    shape: 'cross'
  }
];

const colorPalettes: ColorPalette[] = [
  {
    id: 'ocean',
    name: 'Ocean Blue',
    colors: ['#0ea5e9', '#0284c7', '#0369a1', '#075985'],
    description: 'Calming dan professional'
  },
  {
    id: 'sunset',
    name: 'Sunset Gradient',
    colors: ['#f97316', '#ea580c', '#dc2626', '#b91c1c'],
    description: 'Warm dan energetic'
  },
  {
    id: 'forest',
    name: 'Forest Green',
    colors: ['#16a34a', '#15803d', '#166534', '#14532d'],
    description: 'Natural dan growth'
  },
  {
    id: 'royal',
    name: 'Royal Purple',
    colors: ['#9333ea', '#7c3aed', '#6d28d9', '#5b21b6'],
    description: 'Luxury dan premium'
  },
  {
    id: 'monochrome',
    name: 'Monochrome',
    colors: ['#000000', '#374151', '#6b7280', '#9ca3af'],
    description: 'Timeless dan elegant'
  },
  {
    id: 'coral',
    name: 'Coral Reef',
    colors: ['#ff7875', '#ff6b6b', '#ee5a52', '#e74c3c'],
    description: 'Vibrant dan friendly'
  },
  {
    id: 'mint',
    name: 'Fresh Mint',
    colors: ['#26d0ce', '#1dd1a1', '#00b894', '#00a085'],
    description: 'Fresh dan modern'
  },
  {
    id: 'gold',
    name: 'Golden Hour',
    colors: ['#f39c12', '#e67e22', '#d35400', '#c0392b'],
    description: 'Warm dan premium'
  },
  {
    id: 'lavender',
    name: 'Lavender Dream',
    colors: ['#a29bfe', '#6c5ce7', '#5f3dc4', '#4c3c9e'],
    description: 'Soft dan creative'
  },
  {
    id: 'emerald',
    name: 'Emerald City',
    colors: ['#00b894', '#00a085', '#008f72', '#007d63'],
    description: 'Rich dan sophisticated'
  }
];

export const LogoGenerator: React.FC = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [companyName, setCompanyName] = useState('');
  const [tagline, setTagline] = useState('');
  const [selectedStyle, setSelectedStyle] = useState<LogoStyle>(logoStyles[0]);
  const [selectedPalette, setSelectedPalette] = useState<ColorPalette>(colorPalettes[0]);
  const [fontSize, setFontSize] = useState([48]);
  const [logoSize, setLogoSize] = useState([200]);
  const [backgroundColor, setBackgroundColor] = useState('#ffffff');
  const [showBorder, setShowBorder] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);

  useEffect(() => {
    generateLogo();
  }, [companyName, selectedStyle, selectedPalette, fontSize, logoSize, backgroundColor, showBorder]);

  const generateLogo = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = 400;
    canvas.height = 300;

    // Clear canvas with background color
    ctx.fillStyle = backgroundColor;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Add border if enabled
    if (showBorder) {
      ctx.strokeStyle = selectedPalette.colors[2] || '#000000';
      ctx.lineWidth = 2;
      ctx.strokeRect(1, 1, canvas.width - 2, canvas.height - 2);
    }

    // Draw logo shape/icon
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2 - 30;
    const size = logoSize[0] / 4;

    ctx.fillStyle = selectedPalette.colors[0];
    
    switch (selectedStyle.shape) {
      case 'circle':
        ctx.beginPath();
        ctx.arc(centerX, centerY - 20, size / 2, 0, 2 * Math.PI);
        ctx.fill();
        break;
      case 'square':
        ctx.fillRect(centerX - size / 2, centerY - 20 - size / 2, size, size);
        break;
      case 'triangle':
        ctx.beginPath();
        ctx.moveTo(centerX, centerY - 20 - size / 2);
        ctx.lineTo(centerX - size / 2, centerY - 20 + size / 2);
        ctx.lineTo(centerX + size / 2, centerY - 20 + size / 2);
        ctx.closePath();
        ctx.fill();
        break;
      case 'hexagon':
        drawHexagon(ctx, centerX, centerY - 20, size / 2);
        break;
      case 'leaf':
        drawLeaf(ctx, centerX, centerY - 20, size / 2);
        break;
      case 'diamond':
        drawDiamond(ctx, centerX, centerY - 20, size / 2);
        break;
      case 'star':
        drawStar(ctx, centerX, centerY - 20, size / 2);
        break;
      case 'cross':
        drawCross(ctx, centerX, centerY - 20, size / 2);
        break;
    }

    // Add gradient effect
    const gradient = ctx.createLinearGradient(centerX - size / 2, centerY - 20 - size / 2, centerX + size / 2, centerY - 20 + size / 2);
    gradient.addColorStop(0, selectedPalette.colors[0]);
    gradient.addColorStop(1, selectedPalette.colors[1]);
    ctx.fillStyle = gradient;
    
    switch (selectedStyle.shape) {
      case 'circle':
        ctx.beginPath();
        ctx.arc(centerX, centerY - 20, size / 2, 0, 2 * Math.PI);
        ctx.fill();
        break;
      case 'square':
        ctx.fillRect(centerX - size / 2, centerY - 20 - size / 2, size, size);
        break;
      case 'triangle':
        ctx.beginPath();
        ctx.moveTo(centerX, centerY - 20 - size / 2);
        ctx.lineTo(centerX - size / 2, centerY - 20 + size / 2);
        ctx.lineTo(centerX + size / 2, centerY - 20 + size / 2);
        ctx.closePath();
        ctx.fill();
        break;
      case 'hexagon':
        drawHexagon(ctx, centerX, centerY - 20, size / 2);
        break;
      case 'leaf':
        drawLeaf(ctx, centerX, centerY - 20, size / 2);
        break;
      case 'diamond':
        drawDiamond(ctx, centerX, centerY - 20, size / 2);
        break;
      case 'star':
        drawStar(ctx, centerX, centerY - 20, size / 2);
        break;
      case 'cross':
        drawCross(ctx, centerX, centerY - 20, size / 2);
        break;
    }

    // Draw company name
    if (companyName) {
      ctx.fillStyle = selectedPalette.colors[2] || selectedPalette.colors[0];
      ctx.font = `bold ${fontSize[0]}px ${selectedStyle.fontFamily}`;
      ctx.textAlign = 'center';
      ctx.fillText(companyName, centerX, centerY + 60);
    }

    // Draw tagline
    if (tagline) {
      ctx.fillStyle = selectedPalette.colors[3] || selectedPalette.colors[1];
      ctx.font = `${fontSize[0] * 0.4}px ${selectedStyle.fontFamily}`;
      ctx.textAlign = 'center';
      ctx.fillText(tagline, centerX, centerY + 90);
    }
  };

  const drawHexagon = (ctx: CanvasRenderingContext2D, x: number, y: number, radius: number) => {
    ctx.beginPath();
    for (let i = 0; i < 6; i++) {
      const angle = (i * Math.PI) / 3;
      const px = x + radius * Math.cos(angle);
      const py = y + radius * Math.sin(angle);
      if (i === 0) ctx.moveTo(px, py);
      else ctx.lineTo(px, py);
    }
    ctx.closePath();
    ctx.fill();
  };

  const drawLeaf = (ctx: CanvasRenderingContext2D, x: number, y: number, radius: number) => {
    ctx.beginPath();
    ctx.ellipse(x, y, radius, radius * 1.5, Math.PI / 4, 0, 2 * Math.PI);
    ctx.fill();
  };

  const drawDiamond = (ctx: CanvasRenderingContext2D, x: number, y: number, radius: number) => {
    ctx.beginPath();
    ctx.moveTo(x, y - radius);
    ctx.lineTo(x + radius, y);
    ctx.lineTo(x, y + radius);
    ctx.lineTo(x - radius, y);
    ctx.closePath();
    ctx.fill();
  };

  const drawStar = (ctx: CanvasRenderingContext2D, x: number, y: number, radius: number) => {
    const spikes = 5;
    const outerRadius = radius;
    const innerRadius = radius * 0.4;

    ctx.beginPath();
    for (let i = 0; i < spikes * 2; i++) {
      const angle = (i * Math.PI) / spikes;
      const r = i % 2 === 0 ? outerRadius : innerRadius;
      const px = x + r * Math.cos(angle - Math.PI / 2);
      const py = y + r * Math.sin(angle - Math.PI / 2);
      if (i === 0) ctx.moveTo(px, py);
      else ctx.lineTo(px, py);
    }
    ctx.closePath();
    ctx.fill();
  };

  const drawCross = (ctx: CanvasRenderingContext2D, x: number, y: number, radius: number) => {
    const thickness = radius * 0.3;

    // Vertical bar
    ctx.fillRect(x - thickness / 2, y - radius, thickness, radius * 2);

    // Horizontal bar
    ctx.fillRect(x - radius, y - thickness / 2, radius * 2, thickness);
  };

  const handleGenerateAI = async () => {
    if (!companyName.trim()) {
      toast.error('Masukkan nama perusahaan terlebih dahulu');
      return;
    }

    setIsGenerating(true);

    // AI-powered style selection based on company name
    setTimeout(() => {
      const name = companyName.toLowerCase();
      let suggestedStyle = logoStyles[0];
      let suggestedPalette = colorPalettes[0];

      // Simple AI logic based on keywords
      if (name.includes('tech') || name.includes('digital') || name.includes('software') || name.includes('app')) {
        suggestedStyle = logoStyles.find(s => s.id === 'tech') || logoStyles[0];
        suggestedPalette = colorPalettes.find(p => p.id === 'royal') || colorPalettes[0];
      } else if (name.includes('creative') || name.includes('design') || name.includes('art') || name.includes('studio')) {
        suggestedStyle = logoStyles.find(s => s.id === 'creative') || logoStyles[0];
        suggestedPalette = colorPalettes.find(p => p.id === 'sunset') || colorPalettes[0];
      } else if (name.includes('eco') || name.includes('green') || name.includes('nature') || name.includes('organic')) {
        suggestedStyle = logoStyles.find(s => s.id === 'eco') || logoStyles[0];
        suggestedPalette = colorPalettes.find(p => p.id === 'forest') || colorPalettes[0];
      } else if (name.includes('corp') || name.includes('consulting') || name.includes('finance') || name.includes('law')) {
        suggestedStyle = logoStyles.find(s => s.id === 'corporate') || logoStyles[0];
        suggestedPalette = colorPalettes.find(p => p.id === 'monochrome') || colorPalettes[0];
      } else {
        // Random selection for other cases
        suggestedStyle = logoStyles[Math.floor(Math.random() * logoStyles.length)];
        suggestedPalette = colorPalettes[Math.floor(Math.random() * colorPalettes.length)];
      }

      setSelectedStyle(suggestedStyle);
      setSelectedPalette(suggestedPalette);
      setIsGenerating(false);

      toast.success(`Logo AI berhasil digenerate dengan style ${suggestedStyle.name}!`);
    }, 2000);
  };

  const generateSVG = () => {
    const size = logoSize[0] / 4;
    const centerX = 200;
    const centerY = 120;

    let shapeElement = '';
    const gradient = `
      <defs>
        <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${selectedPalette.colors[0]};stop-opacity:1" />
          <stop offset="100%" style="stop-color:${selectedPalette.colors[1]};stop-opacity:1" />
        </linearGradient>
      </defs>
    `;

    switch (selectedStyle.shape) {
      case 'circle':
        shapeElement = `<circle cx="${centerX}" cy="${centerY - 20}" r="${size / 2}" fill="url(#logoGradient)" />`;
        break;
      case 'square':
        shapeElement = `<rect x="${centerX - size / 2}" y="${centerY - 20 - size / 2}" width="${size}" height="${size}" fill="url(#logoGradient)" />`;
        break;
      case 'triangle':
        shapeElement = `<polygon points="${centerX},${centerY - 20 - size / 2} ${centerX - size / 2},${centerY - 20 + size / 2} ${centerX + size / 2},${centerY - 20 + size / 2}" fill="url(#logoGradient)" />`;
        break;
      case 'diamond':
        shapeElement = `<polygon points="${centerX},${centerY - 20 - size / 2} ${centerX + size / 2},${centerY - 20} ${centerX},${centerY - 20 + size / 2} ${centerX - size / 2},${centerY - 20}" fill="url(#logoGradient)" />`;
        break;
      case 'star':
        const starPoints = [];
        for (let i = 0; i < 10; i++) {
          const angle = (i * Math.PI) / 5;
          const r = i % 2 === 0 ? size / 2 : size / 5;
          const x = centerX + r * Math.cos(angle - Math.PI / 2);
          const y = centerY - 20 + r * Math.sin(angle - Math.PI / 2);
          starPoints.push(`${x},${y}`);
        }
        shapeElement = `<polygon points="${starPoints.join(' ')}" fill="url(#logoGradient)" />`;
        break;
      default:
        shapeElement = `<circle cx="${centerX}" cy="${centerY - 20}" r="${size / 2}" fill="url(#logoGradient)" />`;
    }

    const backgroundRect = backgroundColor !== 'transparent' ?
      `<rect width="400" height="300" fill="${backgroundColor}" />` : '';

    const borderRect = showBorder ?
      `<rect x="1" y="1" width="398" height="298" fill="none" stroke="${selectedPalette.colors[2] || '#000000'}" stroke-width="2" />` : '';

    const svg = `
      <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
        ${backgroundRect}
        ${gradient}
        ${shapeElement}
        ${companyName ? `<text x="${centerX}" y="${centerY + 60}" text-anchor="middle" font-family="${selectedStyle.fontFamily}" font-size="${fontSize[0]}" font-weight="bold" fill="${selectedPalette.colors[2] || selectedPalette.colors[0]}">${companyName}</text>` : ''}
        ${tagline ? `<text x="${centerX}" y="${centerY + 90}" text-anchor="middle" font-family="${selectedStyle.fontFamily}" font-size="${fontSize[0] * 0.4}" fill="${selectedPalette.colors[3] || selectedPalette.colors[1]}">${tagline}</text>` : ''}
        ${borderRect}
      </svg>
    `;

    return svg;
  };

  const downloadLogo = (format: 'png' | 'svg' | 'jpg') => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const link = document.createElement('a');

    if (format === 'svg') {
      const svgContent = generateSVG();
      const blob = new Blob([svgContent], { type: 'image/svg+xml' });
      link.href = URL.createObjectURL(blob);
      link.download = `${companyName || 'logo'}.svg`;
    } else if (format === 'png' || format === 'jpg') {
      const dataURL = canvas.toDataURL(`image/${format}`, 0.9);
      link.href = dataURL;
      link.download = `${companyName || 'logo'}.${format}`;
    }

    link.click();
    toast.success(`Logo berhasil didownload sebagai ${format.toUpperCase()}`);
  };

  const copyToClipboard = async () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    try {
      canvas.toBlob(async (blob) => {
        if (blob) {
          await navigator.clipboard.write([
            new ClipboardItem({ 'image/png': blob })
          ]);
          toast.success('Logo berhasil disalin ke clipboard');
        }
      });
    } catch (error) {
      toast.error('Gagal menyalin logo ke clipboard');
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="w-5 h-5" />
            AI Logo Generator
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Controls */}
            <div className="space-y-6">
              <Tabs defaultValue="basic" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="basic">Basic</TabsTrigger>
                  <TabsTrigger value="style">Style</TabsTrigger>
                  <TabsTrigger value="colors">Colors</TabsTrigger>
                  <TabsTrigger value="advanced">Advanced</TabsTrigger>
                </TabsList>
                
                <TabsContent value="basic" className="space-y-4">
                  <div>
                    <Label htmlFor="company-name">Nama Perusahaan *</Label>
                    <Input
                      id="company-name"
                      value={companyName}
                      onChange={(e) => setCompanyName(e.target.value)}
                      placeholder="Masukkan nama perusahaan"
                      className="mt-1"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="tagline">Tagline (Opsional)</Label>
                    <Input
                      id="tagline"
                      value={tagline}
                      onChange={(e) => setTagline(e.target.value)}
                      placeholder="Slogan atau tagline"
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label>Ukuran Font: {fontSize[0]}px</Label>
                    <Slider
                      value={fontSize}
                      onValueChange={setFontSize}
                      max={72}
                      min={24}
                      step={2}
                      className="mt-2"
                    />
                  </div>

                  <div>
                    <Label>Ukuran Logo: {logoSize[0]}px</Label>
                    <Slider
                      value={logoSize}
                      onValueChange={setLogoSize}
                      max={300}
                      min={100}
                      step={10}
                      className="mt-2"
                    />
                  </div>
                </TabsContent>

                <TabsContent value="style" className="space-y-4">
                  <div>
                    <Label>Pilih Style Logo</Label>
                    <div className="grid grid-cols-1 gap-2 mt-2">
                      {logoStyles.map((style) => (
                        <Card
                          key={style.id}
                          className={`cursor-pointer transition-all ${
                            selectedStyle.id === style.id
                              ? 'ring-2 ring-blue-500 bg-blue-50'
                              : 'hover:bg-gray-50'
                          }`}
                          onClick={() => setSelectedStyle(style)}
                        >
                          <CardContent className="p-3">
                            <div className="flex justify-between items-center">
                              <div>
                                <h4 className="font-medium">{style.name}</h4>
                                <p className="text-sm text-gray-600">{style.description}</p>
                              </div>
                              <div className="flex gap-1">
                                {style.colors.map((color, index) => (
                                  <div
                                    key={index}
                                    className="w-4 h-4 rounded-full border"
                                    style={{ backgroundColor: color }}
                                  />
                                ))}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="colors" className="space-y-4">
                  <div>
                    <Label>Pilih Color Palette</Label>
                    <div className="grid grid-cols-1 gap-2 mt-2">
                      {colorPalettes.map((palette) => (
                        <Card
                          key={palette.id}
                          className={`cursor-pointer transition-all ${
                            selectedPalette.id === palette.id
                              ? 'ring-2 ring-blue-500 bg-blue-50'
                              : 'hover:bg-gray-50'
                          }`}
                          onClick={() => setSelectedPalette(palette)}
                        >
                          <CardContent className="p-3">
                            <div className="flex justify-between items-center">
                              <div>
                                <h4 className="font-medium">{palette.name}</h4>
                                <p className="text-sm text-gray-600">{palette.description}</p>
                              </div>
                              <div className="flex gap-1">
                                {palette.colors.map((color, index) => (
                                  <div
                                    key={index}
                                    className="w-6 h-6 rounded border"
                                    style={{ backgroundColor: color }}
                                  />
                                ))}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="advanced" className="space-y-4">
                  <div>
                    <Label htmlFor="background-color">Background Color</Label>
                    <div className="flex gap-2 mt-2">
                      <Input
                        id="background-color"
                        type="color"
                        value={backgroundColor}
                        onChange={(e) => setBackgroundColor(e.target.value)}
                        className="w-16 h-10 p-1 border rounded"
                      />
                      <Input
                        value={backgroundColor}
                        onChange={(e) => setBackgroundColor(e.target.value)}
                        placeholder="#ffffff"
                        className="flex-1"
                      />
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="show-border"
                      checked={showBorder}
                      onCheckedChange={setShowBorder}
                    />
                    <Label htmlFor="show-border">Tampilkan Border</Label>
                  </div>

                  <div className="grid grid-cols-3 gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setBackgroundColor('#ffffff')}
                    >
                      White
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setBackgroundColor('#000000')}
                    >
                      Black
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setBackgroundColor('transparent')}
                    >
                      Transparent
                    </Button>
                  </div>

                  <div className="p-3 bg-gray-50 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-2">🎨 Advanced Tips</h4>
                    <ul className="text-sm text-gray-700 space-y-1">
                      <li>• Transparent background untuk overlay</li>
                      <li>• Border membantu definisi logo</li>
                      <li>• Test logo di berbagai background</li>
                    </ul>
                  </div>
                </TabsContent>
              </Tabs>

              <div className="space-y-2">
                <Button
                  onClick={handleGenerateAI}
                  disabled={isGenerating || !companyName.trim()}
                  className="w-full"
                >
                  {isGenerating ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="w-4 h-4 mr-2" />
                      Generate AI Logo
                    </>
                  )}
                </Button>

                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSelectedStyle(logoStyles[Math.floor(Math.random() * logoStyles.length)]);
                      setSelectedPalette(colorPalettes[Math.floor(Math.random() * colorPalettes.length)]);
                      toast.success('Style acak dipilih!');
                    }}
                  >
                    <RefreshCw className="w-4 h-4 mr-1" />
                    Random
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setCompanyName('');
                      setTagline('');
                      setSelectedStyle(logoStyles[0]);
                      setSelectedPalette(colorPalettes[0]);
                      setFontSize([48]);
                      setLogoSize([200]);
                      setBackgroundColor('#ffffff');
                      setShowBorder(false);
                      toast.success('Logo direset!');
                    }}
                  >
                    <RefreshCw className="w-4 h-4 mr-1" />
                    Reset
                  </Button>
                </div>
              </div>
            </div>

            {/* Preview */}
            <div className="space-y-4">
              <div>
                <Label>Preview Logo</Label>
                <Card className="mt-2">
                  <CardContent className="p-4">
                    <div className="flex justify-center">
                      <canvas
                        ref={canvasRef}
                        className="border rounded-lg max-w-full h-auto"
                        style={{ maxWidth: '100%', height: 'auto' }}
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="space-y-2">
                <Label>Export Logo</Label>
                <div className="flex flex-wrap gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => downloadLogo('png')}
                    disabled={!companyName.trim()}
                  >
                    <Download className="w-4 h-4 mr-1" />
                    PNG
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => downloadLogo('jpg')}
                    disabled={!companyName.trim()}
                  >
                    <Download className="w-4 h-4 mr-1" />
                    JPG
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => downloadLogo('svg')}
                    disabled={!companyName.trim()}
                  >
                    <Download className="w-4 h-4 mr-1" />
                    SVG
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyToClipboard}
                    disabled={!companyName.trim()}
                  >
                    <Copy className="w-4 h-4 mr-1" />
                    Copy
                  </Button>
                </div>
              </div>

              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">💡 Tips Logo Design</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Gunakan maksimal 2-3 warna</li>
                  <li>• Pastikan logo terlihat jelas dalam ukuran kecil</li>
                  <li>• Pilih font yang mudah dibaca</li>
                  <li>• Sesuaikan style dengan industri bisnis</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

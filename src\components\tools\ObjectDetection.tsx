import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { toast } from 'sonner';
import { Upload, Download, Search, Target, Zap } from 'lucide-react';
import * as tf from '@tensorflow/tfjs';
import * as cocoSsd from '@tensorflow-models/coco-ssd';

interface DetectedObject {
  class: string;
  score: number;
  bbox: [number, number, number, number]; // [x, y, width, height]
}

export const ObjectDetection: React.FC = () => {
  const [image, setImage] = useState<string | null>(null);
  const [detectedObjects, setDetectedObjects] = useState<DetectedObject[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [modelLoaded, setModelLoaded] = useState(false);
  const [model, setModel] = useState<cocoSsd.ObjectDetection | null>(null);
  const [confidenceThreshold, setConfidenceThreshold] = useState([0.5]);
  
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    loadModel();
  }, []);

  const loadModel = async () => {
    try {
      setIsLoading(true);
      toast.info('Loading COCO-SSD model...');
      
      // Set TensorFlow.js backend
      await tf.ready();
      
      // Load COCO-SSD model
      const loadedModel = await cocoSsd.load();
      setModel(loadedModel);
      setModelLoaded(true);
      
      toast.success('Object detection model loaded successfully!');
    } catch (error) {
      console.error('Error loading model:', error);
      toast.error('Failed to load object detection model');
      setModelLoaded(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file!');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const imageUrl = e.target?.result as string;
      setImage(imageUrl);
      setDetectedObjects([]);
    };
    reader.readAsDataURL(file);
  };

  const detectObjects = async () => {
    if (!image || !imageRef.current || !model) {
      toast.error('Please upload an image and wait for model to load');
      return;
    }

    setIsLoading(true);
    try {
      // Perform object detection
      const predictions = await model.detect(imageRef.current);
      
      // Filter by confidence threshold
      const filteredPredictions = predictions.filter(
        prediction => prediction.score >= confidenceThreshold[0]
      );

      const objects: DetectedObject[] = filteredPredictions.map(prediction => ({
        class: prediction.class,
        score: prediction.score,
        bbox: prediction.bbox
      }));

      setDetectedObjects(objects);
      drawDetections(objects);
      toast.success(`Detected ${objects.length} object(s)!`);
    } catch (error) {
      console.error('Detection error:', error);
      toast.error('Object detection failed');
    } finally {
      setIsLoading(false);
    }
  };

  const drawDetections = (objects: DetectedObject[]) => {
    const canvas = canvasRef.current;
    const img = imageRef.current;
    if (!canvas || !img) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size to match image
    canvas.width = img.naturalWidth;
    canvas.height = img.naturalHeight;

    // Draw image
    ctx.drawImage(img, 0, 0);

    // Define colors for different object classes
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ];

    // Draw detections
    objects.forEach((obj, index) => {
      const [x, y, width, height] = obj.bbox;
      const color = colors[index % colors.length];

      // Draw bounding box
      ctx.strokeStyle = color;
      ctx.lineWidth = 3;
      ctx.strokeRect(x, y, width, height);

      // Draw label background
      const label = `${obj.class} (${(obj.score * 100).toFixed(1)}%)`;
      ctx.font = '16px Arial';
      const textWidth = ctx.measureText(label).width;
      
      ctx.fillStyle = color;
      ctx.fillRect(x, y - 25, textWidth + 10, 25);

      // Draw label text
      ctx.fillStyle = '#FFFFFF';
      ctx.fillText(label, x + 5, y - 8);
    });
  };

  const downloadResult = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const link = document.createElement('a');
    link.download = `object-detection-${Date.now()}.png`;
    link.href = canvas.toDataURL();
    link.click();
    toast.success('Result downloaded!');
  };

  const getObjectCounts = () => {
    const counts: { [key: string]: number } = {};
    detectedObjects.forEach(obj => {
      counts[obj.class] = (counts[obj.class] || 0) + 1;
    });
    return counts;
  };

  const loadSampleImage = (type: string) => {
    const samples = {
      street: 'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=800',
      animals: 'https://images.unsplash.com/photo-1564349683136-77e08dba1ef7?w=800',
      food: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=800',
      vehicles: 'https://images.unsplash.com/photo-1449965408869-eaa3f722e40d?w=800'
    };

    const sampleUrl = samples[type as keyof typeof samples];
    if (sampleUrl) {
      setImage(sampleUrl);
      setDetectedObjects([]);
      toast.success(`Sample ${type} image loaded!`);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🎯 Object Detection
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Upload Section */}
          <div className="space-y-4">
            <label className="cursor-pointer">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors">
                <Upload className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <p className="text-lg font-medium text-gray-700">
                  {isLoading ? 'Processing...' : 'Upload image for object detection'}
                </p>
                <p className="text-sm text-gray-500">
                  Support: JPG, PNG, WebP
                </p>
                {isLoading && (
                  <div className="mt-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  </div>
                )}
              </div>
              <Input
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
                disabled={isLoading}
              />
            </label>
          </div>

          {/* Sample Images */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Try Sample Images:</label>
            <div className="flex gap-2 flex-wrap">
              <Button variant="outline" size="sm" onClick={() => loadSampleImage('street')}>
                🏙️ Street Scene
              </Button>
              <Button variant="outline" size="sm" onClick={() => loadSampleImage('animals')}>
                🐕 Animals
              </Button>
              <Button variant="outline" size="sm" onClick={() => loadSampleImage('food')}>
                🍕 Food
              </Button>
              <Button variant="outline" size="sm" onClick={() => loadSampleImage('vehicles')}>
                🚗 Vehicles
              </Button>
            </div>
          </div>

          {/* Confidence Threshold */}
          <div className="space-y-2">
            <label className="text-sm font-medium">
              Confidence Threshold: {(confidenceThreshold[0] * 100).toFixed(0)}%
            </label>
            <Slider
              value={confidenceThreshold}
              onValueChange={setConfidenceThreshold}
              max={1}
              min={0.1}
              step={0.05}
              className="w-full"
            />
            <p className="text-xs text-gray-500">
              Higher threshold = fewer but more confident detections
            </p>
          </div>

          {/* Action Buttons */}
          {image && (
            <div className="flex gap-2">
              <Button onClick={detectObjects} disabled={isLoading || !modelLoaded}>
                <Search className="w-4 h-4 mr-2" />
                {isLoading ? 'Detecting...' : 'Detect Objects'}
              </Button>
              {detectedObjects.length > 0 && (
                <Button variant="outline" onClick={downloadResult}>
                  <Download className="w-4 h-4 mr-2" />
                  Download Result
                </Button>
              )}
            </div>
          )}

          {/* Image Display */}
          {image && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Original Image */}
                <div>
                  <h3 className="text-sm font-medium mb-2">Original Image:</h3>
                  <img
                    ref={imageRef}
                    src={image}
                    alt="Original"
                    className="w-full h-auto border rounded-lg"
                    crossOrigin="anonymous"
                    onLoad={() => {
                      if (detectedObjects.length > 0) {
                        drawDetections(detectedObjects);
                      }
                    }}
                  />
                </div>

                {/* Detection Result */}
                <div>
                  <h3 className="text-sm font-medium mb-2">Detection Result:</h3>
                  <canvas
                    ref={canvasRef}
                    className="w-full h-auto border rounded-lg bg-gray-100"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Detection Results */}
          {detectedObjects.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Target className="w-5 h-5" />
                Detection Results ({detectedObjects.length} objects)
              </h3>

              {/* Object Counts */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
                {Object.entries(getObjectCounts()).map(([className, count]) => (
                  <div key={className} className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{count}</div>
                    <div className="text-sm text-gray-600 capitalize">{className}</div>
                  </div>
                ))}
              </div>

              {/* Detailed Results */}
              <div className="grid gap-3">
                {detectedObjects.map((obj, index) => (
                  <Card key={index} className="p-3">
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className="font-medium capitalize">{obj.class}</h4>
                        <p className="text-sm text-gray-600">
                          Confidence: {(obj.score * 100).toFixed(1)}%
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-600">
                          Position: ({Math.round(obj.bbox[0])}, {Math.round(obj.bbox[1])})
                        </p>
                        <p className="text-sm text-gray-600">
                          Size: {Math.round(obj.bbox[2])} × {Math.round(obj.bbox[3])}
                        </p>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Model Status */}
          <div className={`p-4 rounded-lg ${modelLoaded ? 'bg-green-50' : 'bg-yellow-50'}`}>
            <h4 className={`font-semibold mb-2 ${modelLoaded ? 'text-green-800' : 'text-yellow-800'}`}>
              {modelLoaded ? '✅ COCO-SSD Model Status' : '⚠️ Model Loading...'}
            </h4>
            <p className={`text-sm ${modelLoaded ? 'text-green-700' : 'text-yellow-700'}`}>
              {modelLoaded 
                ? 'COCO-SSD model loaded. Can detect 80+ object classes including people, vehicles, animals, and everyday objects.'
                : 'Loading TensorFlow.js and COCO-SSD model. This may take a moment...'
              }
            </p>
          </div>

          {/* Supported Objects */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">🎯 Detectable Objects (80+ classes):</h4>
            <div className="text-sm text-blue-700 grid grid-cols-2 md:grid-cols-4 gap-1">
              <div>• Person, Bicycle, Car</div>
              <div>• Motorcycle, Bus, Truck</div>
              <div>• Cat, Dog, Horse</div>
              <div>• Sheep, Cow, Elephant</div>
              <div>• Chair, Sofa, Table</div>
              <div>• TV, Laptop, Phone</div>
              <div>• Apple, Banana, Pizza</div>
              <div>• And many more...</div>
            </div>
          </div>

          {/* Info */}
          <div className="bg-purple-50 p-4 rounded-lg">
            <h4 className="font-semibold text-purple-800 mb-2">ℹ️ Object Detection Features:</h4>
            <ul className="text-sm text-purple-700 space-y-1">
              <li>• <strong>COCO-SSD Model</strong>: Pre-trained on 80+ object classes</li>
              <li>• <strong>Real-time Detection</strong>: Fast inference using TensorFlow.js</li>
              <li>• <strong>Bounding Boxes</strong>: Precise object localization</li>
              <li>• <strong>Confidence Scores</strong>: Adjustable detection threshold</li>
              <li>• <strong>Object Counting</strong>: Automatic counting by class</li>
              <li>• <strong>Privacy First</strong>: All processing done locally in browser</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KIKAZE-AI - Loading Error</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            color: white;
            text-align: center;
        }

        .container {
            max-width: 500px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .logo {
            font-size: 48px;
            margin-bottom: 20px;
        }

        h1 {
            font-size: 28px;
            margin-bottom: 16px;
            font-weight: 700;
        }

        p {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
            opacity: 0.9;
        }

        .error-details {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: left;
            font-family: monospace;
            font-size: 14px;
        }

        .button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .troubleshooting {
            text-align: left;
            margin-top: 30px;
            padding: 20px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
        }

        .troubleshooting h3 {
            margin-bottom: 15px;
            color: #ffd700;
        }

        .troubleshooting ol {
            margin-left: 20px;
        }

        .troubleshooting li {
            margin-bottom: 8px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🤖</div>
        <h1>KIKAZE-AI Desktop</h1>
        <p>Maaf, terjadi masalah saat memuat aplikasi utama.</p>
        
        <div class="error-details">
            <strong>Error Details:</strong><br>
            Failed to load main application files.<br>
            This might be due to missing build files or configuration issues.
        </div>

        <button class="button" onclick="location.reload()">🔄 Coba Lagi</button>
        <button class="button" onclick="window.close()">❌ Tutup</button>

        <div class="troubleshooting">
            <h3>🔧 Troubleshooting:</h3>
            <ol>
                <li>Pastikan aplikasi sudah di-build dengan benar: <code>npm run build</code></li>
                <li>Periksa apakah folder <code>dist</code> ada dan berisi file aplikasi</li>
                <li>Coba rebuild aplikasi: <code>npm run dist-win</code></li>
                <li>Periksa console untuk error messages</li>
                <li>Restart aplikasi sebagai Administrator</li>
            </ol>
        </div>

        <div style="margin-top: 30px; font-size: 12px; opacity: 0.7;">
            KIKAZE-AI Desktop v1.0.0<br>
            Advanced Indonesian AI Assistant
        </div>
    </div>

    <script>
        // Log system information for debugging
        console.log('Fallback page loaded');
        console.log('User Agent:', navigator.userAgent);
        console.log('Platform:', navigator.platform);
        console.log('Location:', window.location.href);
        
        // Auto-retry after 5 seconds
        setTimeout(() => {
            console.log('Auto-retrying...');
            location.reload();
        }, 10000);
    </script>
</body>
</html>

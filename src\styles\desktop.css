/* Desktop-specific styles for KIKAZE-AI */

/* Desktop app body styling */
.desktop-app {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Allow text selection in input areas */
.desktop-app input,
.desktop-app textarea,
.desktop-app [contenteditable],
.desktop-app .message-content,
.desktop-app .code-block {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

/* Desktop window styling */
.desktop-app .chat-container {
  height: 100vh;
  overflow: hidden;
}

/* Enhanced scrollbars for desktop */
.desktop-app ::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.desktop-app ::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 6px;
}

.desktop-app ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 6px;
  border: 2px solid #f1f1f1;
}

.desktop-app ::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.desktop-app ::-webkit-scrollbar-corner {
  background: #f1f1f1;
}

/* Desktop-specific animations */
.desktop-app .fade-in {
  animation: desktopFadeIn 0.3s ease-out;
}

@keyframes desktopFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Desktop notification styling */
.desktop-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  z-index: 10000;
  max-width: 350px;
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Desktop menu styling */
.desktop-app .menu-item {
  transition: background-color 0.2s ease;
}

.desktop-app .menu-item:hover {
  background-color: #f8fafc;
}

/* Desktop button enhancements */
.desktop-app button {
  transition: all 0.2s ease;
}

.desktop-app button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.desktop-app button:active {
  transform: translateY(0);
}

/* Desktop card styling */
.desktop-app .card {
  transition: box-shadow 0.2s ease;
}

.desktop-app .card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Desktop input focus styling */
.desktop-app input:focus,
.desktop-app textarea:focus {
  outline: none;
  ring: 2px;
  ring-color: #3b82f6;
  ring-opacity: 0.5;
}

/* Desktop-specific layout adjustments */
.desktop-app .chat-header {
  -webkit-app-region: drag;
  padding-top: 8px;
}

.desktop-app .chat-header button {
  -webkit-app-region: no-drag;
}

/* Desktop sidebar styling */
.desktop-app .sidebar {
  border-right: 1px solid #e2e8f0;
  background: #fafafa;
}

/* Desktop message styling */
.desktop-app .message {
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 12px;
  transition: background-color 0.2s ease;
}

.desktop-app .message:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.desktop-app .message.user {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-left: 20%;
}

.desktop-app .message.assistant {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  margin-right: 20%;
}

/* Desktop code block styling */
.desktop-app .code-block {
  background: #1e293b;
  color: #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin: 12px 0;
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 14px;
  line-height: 1.5;
  overflow-x: auto;
}

.desktop-app .code-block::-webkit-scrollbar {
  height: 8px;
}

.desktop-app .code-block::-webkit-scrollbar-track {
  background: #334155;
}

.desktop-app .code-block::-webkit-scrollbar-thumb {
  background: #64748b;
  border-radius: 4px;
}

/* Desktop tool panel styling */
.desktop-app .tool-panel {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin: 16px 0;
}

/* Desktop loading animation */
.desktop-app .loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Desktop modal styling */
.desktop-app .modal-overlay {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.desktop-app .modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Desktop tooltip styling */
.desktop-app .tooltip {
  background: #1e293b;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  animation: tooltipFadeIn 0.2s ease-out;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: translateY(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Desktop status bar */
.desktop-app .status-bar {
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  padding: 8px 16px;
  font-size: 12px;
  color: #64748b;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Desktop performance optimizations */
.desktop-app * {
  box-sizing: border-box;
}

.desktop-app img {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Desktop accessibility improvements */
.desktop-app :focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Desktop print styles */
@media print {
  .desktop-app .no-print {
    display: none !important;
  }
  
  .desktop-app .message {
    break-inside: avoid;
    margin-bottom: 12px;
  }
  
  .desktop-app .code-block {
    background: #f8fafc !important;
    color: #1e293b !important;
    border: 1px solid #e2e8f0 !important;
  }
}

/* Desktop dark mode support */
@media (prefers-color-scheme: dark) {
  .desktop-app.auto-theme {
    background: #0f172a;
    color: #e2e8f0;
  }
  
  .desktop-app.auto-theme .card {
    background: #1e293b;
    border-color: #334155;
  }
  
  .desktop-app.auto-theme .message.assistant {
    background: #1e293b;
    border-color: #334155;
    color: #e2e8f0;
  }
}

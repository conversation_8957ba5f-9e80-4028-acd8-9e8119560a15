#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Building KIKAZE-AI Desktop App...\n');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function step(message) {
  log(`\n📋 ${message}`, 'cyan');
}

function success(message) {
  log(`✅ ${message}`, 'green');
}

function error(message) {
  log(`❌ ${message}`, 'red');
}

function warning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

try {
  // Step 1: Clean previous builds
  step('Cleaning previous builds...');
  if (fs.existsSync('dist')) {
    fs.rmSync('dist', { recursive: true, force: true });
  }
  if (fs.existsSync('dist-electron')) {
    fs.rmSync('dist-electron', { recursive: true, force: true });
  }
  success('Previous builds cleaned');

  // Step 2: Install dependencies if needed
  step('Checking dependencies...');
  if (!fs.existsSync('node_modules')) {
    log('Installing dependencies...', 'yellow');
    execSync('npm install', { stdio: 'inherit' });
  }
  success('Dependencies ready');

  // Step 3: Create icon files if they don't exist
  step('Preparing app icons...');
  const iconsDir = path.join(__dirname, '../electron/assets');
  if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir, { recursive: true });
  }

  // Create placeholder icon files if they don't exist
  const iconFiles = [
    { name: 'icon.png', size: '512x512' },
    { name: 'icon.ico', size: '256x256' },
    { name: 'icon.icns', size: '512x512' }
  ];

  iconFiles.forEach(({ name, size }) => {
    const iconPath = path.join(iconsDir, name);
    if (!fs.existsSync(iconPath)) {
      warning(`Icon ${name} not found. You should add a ${size} icon at ${iconPath}`);
      // Create a simple placeholder
      if (name.endsWith('.png')) {
        // For now, just create an empty file - in production you'd want actual icons
        fs.writeFileSync(iconPath, '');
      }
    }
  });
  success('App icons prepared');

  // Step 4: Build the React app
  step('Building React application...');
  execSync('npm run build', { stdio: 'inherit' });
  success('React application built successfully');

  // Step 5: Verify build output
  step('Verifying build output...');
  if (!fs.existsSync('dist/index.html')) {
    throw new Error('Build output not found. React build may have failed.');
  }
  success('Build output verified');

  // Step 6: Build Electron app
  step('Building Electron application...');
  const platform = process.platform;
  let buildCommand;

  switch (platform) {
    case 'win32':
      buildCommand = 'npm run dist-win';
      break;
    case 'darwin':
      buildCommand = 'npm run dist-mac';
      break;
    case 'linux':
      buildCommand = 'npm run dist-linux';
      break;
    default:
      buildCommand = 'npm run dist';
  }

  log(`Building for platform: ${platform}`, 'blue');
  execSync(buildCommand, { stdio: 'inherit' });
  success('Electron application built successfully');

  // Step 7: Show build results
  step('Build completed! 🎉');
  
  const distDir = path.join(__dirname, '../dist-electron');
  if (fs.existsSync(distDir)) {
    const files = fs.readdirSync(distDir);
    log('\n📦 Generated files:', 'bright');
    files.forEach(file => {
      const filePath = path.join(distDir, file);
      const stats = fs.statSync(filePath);
      const size = (stats.size / 1024 / 1024).toFixed(2);
      log(`   ${file} (${size} MB)`, 'green');
    });
  }

  log('\n🎯 Installation Instructions:', 'bright');
  log('1. Navigate to the dist-electron folder', 'cyan');
  log('2. Run the installer (.exe for Windows, .dmg for Mac, .AppImage for Linux)', 'cyan');
  log('3. Follow the installation wizard', 'cyan');
  log('4. Launch KIKAZE-AI from your desktop or start menu', 'cyan');

  log('\n🚀 KIKAZE-AI Desktop App is ready for distribution!', 'green');

} catch (err) {
  error(`Build failed: ${err.message}`);
  process.exit(1);
}

{"name": "kikaze-ai-desktop", "productName": "KIKAZE-AI", "description": "Advanced Indonesian AI Assistant with Memory & Voice", "version": "1.0.0", "type": "module", "private": true, "main": "electron/main.js", "homepage": "./", "author": {"name": "KIKAZE-AI Team", "email": "<EMAIL>"}, "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "clean": "rimraf build-output dist-electron dist node_modules/.cache", "electron-build": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never", "dist-win": "npm run build && electron-builder --win --x64", "dist-mac": "npm run build && electron-builder --mac", "dist-linux": "npm run build && electron-builder --linux", "dist-win-force": "npm run build && electron-builder --win --x64 --config.directories.output=dist-temp", "build-desktop": "node scripts/build-desktop.js", "build-exe": "npm run build-desktop"}, "dependencies": {"@capacitor/android": "^7.0.1", "@capacitor/cli": "^7.0.1", "@capacitor/core": "^7.0.1", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@hookform/resolvers": "^3.9.0", "@huggingface/transformers": "^3.3.3", "@imgly/background-removal": "^1.6.0", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.56.2", "@tensorflow-models/coco-ssd": "^2.2.3", "@tensorflow/tfjs": "^4.22.0", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "face-api.js": "^0.22.2", "highlight.js": "^11.11.1", "input-otp": "^1.2.4", "lucide-react": "^0.462.0", "mammoth": "^1.9.0", "next-themes": "^0.3.0", "pdf-lib": "^1.17.1", "pdfjs-dist": "^4.10.38", "qrcode": "^1.5.4", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "tesseract.js": "^6.0.1", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@rollup/rollup-win32-x64-msvc": "^4.42.0", "@swc/core-win32-x64-msvc": "^1.11.31", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/pdfjs-dist": "^2.10.377", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "electron": "^36.4.0", "electron-builder": "^26.0.12", "electron-is-dev": "^3.0.1", "electron-reload": "^2.0.0-alpha.1", "electron-updater": "^6.6.2", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.47", "rimraf": "^6.0.1", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "wait-on": "^8.0.3"}, "build": {"appId": "com.kikaze.ai.desktop", "productName": "KIKAZE-AI", "directories": {"output": "dist-electron", "buildResources": "electron"}, "files": ["dist/**/*", "electron/**/*"], "extraResources": [{"from": "dist", "to": "app", "filter": ["**/*"]}], "win": {"target": {"target": "nsis", "arch": ["x64"]}, "verifyUpdateCodeSignature": false, "requestedExecutionLevel": "asInvoker", "icon": "electron/icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "KIKAZE-AI", "installerIcon": "electron/icon.ico", "uninstallerIcon": "electron/icon.ico"}, "mac": {"target": "dmg", "category": "public.app-category.productivity"}, "linux": {"target": "AppImage", "category": "Office"}, "nodeGypRebuild": false, "buildDependenciesFromSource": false, "npmRebuild": false, "removePackageScripts": true, "compression": "normal"}}
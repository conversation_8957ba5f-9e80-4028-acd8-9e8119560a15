# 🤖 KIKAZE-AI Browser Extension

Transform any website into an AI-powered workspace with KIKAZE-AI Browser Extension!

## 🚀 **Features**

### **🎯 Right-Click AI Assistance**
- **Explain**: Get AI explanations for any selected text
- **Translate**: Instant translation to Indonesian
- **Summarize**: Quick page summaries
- **Extract Data**: Pull structured data from pages
- **Generate Code**: Create code from descriptions
- **Check Grammar**: Improve your writing

### **⚡ Quick Actions Popup**
- **Page Analysis**: Instant AI insights about current page
- **SEO Analysis**: Check page optimization
- **Accessibility Check**: Ensure inclusive design
- **Content Extraction**: Get clean, readable content

### **🎨 Smart Features**
- **Floating Widget**: Non-intrusive AI assistance
- **Keyboard Shortcuts**: Ctrl+Shift+K for quick access
- **Smart Selection**: Enhanced text selection with AI hints
- **Auto-Detection**: Intelligent content recognition

## 📦 **Installation**

### **Method 1: Load Unpacked (Development)**

1. **Download Extension**
   ```bash
   # Clone the repository
   git clone https://github.com/your-repo/kikaze-ai
   cd kikaze-ai/browser-extension
   ```

2. **Open Chrome Extensions**
   - Go to `chrome://extensions/`
   - Enable "Developer mode" (top right toggle)

3. **Load Extension**
   - Click "Load unpacked"
   - Select the `browser-extension` folder
   - Extension will appear in your toolbar

### **Method 2: Chrome Web Store (Coming Soon)**
- Search for "KIKAZE-AI Assistant" in Chrome Web Store
- Click "Add to Chrome"
- Follow installation prompts

## ⚙️ **Setup**

### **1. API Configuration**
The extension needs an API key to function. You have two options:

#### **Option A: Use Your Own API Key**
1. Get a free API key from [Groq](https://console.groq.com/)
2. Open extension popup
3. Go to Settings
4. Enter your API key
5. Save settings

#### **Option B: Connect to Local KIKAZE-AI**
1. Make sure KIKAZE-AI app is running locally (`npm run dev`)
2. Extension will automatically connect to `http://localhost:5173`
3. No additional setup required

### **2. Permissions**
The extension requests these permissions:
- **activeTab**: Access current page content
- **contextMenus**: Add right-click options
- **storage**: Save your preferences
- **scripting**: Inject AI assistance features

## 🎮 **How to Use**

### **Right-Click Context Menu**
1. **Select any text** on any webpage
2. **Right-click** to open context menu
3. **Choose KIKAZE-AI option** (Explain, Translate, etc.)
4. **View results** in floating widget

### **Extension Popup**
1. **Click extension icon** in toolbar
2. **Choose quick action** (Explain Page, Summarize, etc.)
3. **View results** in popup
4. **Copy or download** results

### **Keyboard Shortcuts**
- `Ctrl+Shift+K`: Toggle floating widget
- `Escape`: Close widget/popup
- `Ctrl+C`: Copy AI results

## 🛠️ **Development**

### **File Structure**
```
browser-extension/
├── manifest.json          # Extension configuration
├── background.js          # Service worker
├── content.js            # Page interaction script
├── popup.html            # Extension popup UI
├── popup.js              # Popup functionality
├── content.css           # Widget styling
├── icons/                # Extension icons
└── README.md             # This file
```

### **Key Components**

#### **Background Script** (`background.js`)
- Manages context menus
- Handles API calls
- Coordinates between components

#### **Content Script** (`content.js`)
- Injects floating widget
- Handles page interaction
- Manages text selection

#### **Popup** (`popup.html` + `popup.js`)
- Extension interface
- Quick actions
- Settings management

### **Building for Production**
1. **Update API endpoints** in background.js and popup.js
2. **Add your API key** or implement secure key management
3. **Test thoroughly** on different websites
4. **Package for Chrome Web Store**:
   ```bash
   zip -r kikaze-ai-extension.zip . -x "*.git*" "README.md"
   ```

## 🔧 **Configuration**

### **API Endpoints**
Update these URLs in the code:
```javascript
// Development
const API_BASE = 'http://localhost:5173';

// Production
const API_BASE = 'https://your-kikaze-ai-domain.com';
```

### **Groq API Integration**
```javascript
const GROQ_API_URL = 'https://api.groq.com/openai/v1/chat/completions';
const GROQ_API_KEY = 'your-api-key-here'; // Store securely!
```

## 🚨 **Security Notes**

### **API Key Security**
- **Never hardcode** API keys in the extension
- **Use Chrome storage** for user-provided keys
- **Implement key validation** before making requests
- **Consider proxy server** for additional security

### **Content Security**
- **Validate all inputs** before sending to AI
- **Sanitize outputs** before displaying
- **Respect page CSP** policies
- **Handle sensitive data** carefully

## 🐛 **Troubleshooting**

### **Common Issues**

#### **Extension Not Loading**
- Check if Developer mode is enabled
- Verify all files are in correct locations
- Check browser console for errors

#### **API Calls Failing**
- Verify API key is correct
- Check network connectivity
- Ensure CORS is properly configured

#### **Context Menu Not Appearing**
- Refresh the page after installing
- Check if text is properly selected
- Verify extension permissions

#### **Widget Not Showing**
- Try keyboard shortcut (Ctrl+Shift+K)
- Check if content script loaded
- Verify CSS is not conflicting

### **Debug Mode**
Enable debug logging:
```javascript
// Add to background.js or content.js
const DEBUG = true;
if (DEBUG) console.log('Debug message');
```

## 📱 **Browser Compatibility**

### **Supported Browsers**
- ✅ **Chrome 88+** (Primary target)
- ✅ **Edge 88+** (Chromium-based)
- ⚠️ **Firefox** (Requires Manifest V2 conversion)
- ⚠️ **Safari** (Requires Safari extension conversion)

### **Tested Websites**
- ✅ Google, Wikipedia, GitHub
- ✅ News sites, blogs, documentation
- ✅ Social media platforms
- ⚠️ Some sites with strict CSP may have limitations

## 🔄 **Updates**

### **Auto-Updates**
- Extension auto-updates from Chrome Web Store
- Check for updates manually in `chrome://extensions/`

### **Manual Updates**
1. Download latest version
2. Remove old extension
3. Load new unpacked extension
4. Reconfigure settings if needed

## 📞 **Support**

### **Getting Help**
- 📧 Email: <EMAIL>
- 💬 Discord: [KIKAZE-AI Community](https://discord.gg/kikaze-ai)
- 🐛 Issues: [GitHub Issues](https://github.com/your-repo/kikaze-ai/issues)
- 📖 Docs: [Full Documentation](https://docs.kikaze-ai.com)

### **Feature Requests**
We love feedback! Submit feature requests:
1. Open GitHub issue with "Feature Request" label
2. Describe the feature and use case
3. Include mockups if possible

## 📄 **License**

MIT License - see [LICENSE](../LICENSE) file for details.

## 🙏 **Credits**

Built with ❤️ by the KIKAZE-AI team using:
- Chrome Extensions API
- Groq AI API
- Modern JavaScript/TypeScript
- Tailwind CSS

---

**🚀 Ready to supercharge your browsing with AI? Install KIKAZE-AI Browser Extension today!**

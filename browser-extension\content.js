// KIKAZE-AI Content Script

class KikazeWidget {
  constructor() {
    this.widget = null;
    this.isVisible = false;
    this.init();
  }

  init() {
    this.createWidget();
    this.setupEventListeners();
  }

  createWidget() {
    // Create floating widget
    this.widget = document.createElement('div');
    this.widget.id = 'kikaze-widget';
    this.widget.innerHTML = `
      <div class="kikaze-widget-container">
        <div class="kikaze-widget-header">
          <span class="kikaze-logo">🤖 KIKAZE-AI</span>
          <button class="kikaze-close" id="kikaze-close">×</button>
        </div>
        <div class="kikaze-widget-content">
          <div class="kikaze-loading" id="kikaze-loading" style="display: none;">
            <div class="kikaze-spinner"></div>
            <span>Processing with AI...</span>
          </div>
          <div class="kikaze-result" id="kikaze-result"></div>
          <div class="kikaze-actions">
            <button class="kikaze-btn" id="kikaze-copy">📋 Copy</button>
            <button class="kikaze-btn" id="kikaze-open-app">🚀 Open App</button>
          </div>
        </div>
      </div>
    `;

    // Add styles
    const style = document.createElement('style');
    style.textContent = `
      #kikaze-widget {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 350px;
        max-height: 400px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        display: none;
        animation: slideIn 0.3s ease-out;
      }

      @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }

      .kikaze-widget-container {
        border: 1px solid #e1e5e9;
        border-radius: 12px;
        overflow: hidden;
      }

      .kikaze-widget-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 12px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .kikaze-logo {
        font-weight: 600;
        font-size: 14px;
      }

      .kikaze-close {
        background: none;
        border: none;
        color: white;
        font-size: 18px;
        cursor: pointer;
        padding: 0;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .kikaze-close:hover {
        background: rgba(255,255,255,0.2);
      }

      .kikaze-widget-content {
        padding: 16px;
        max-height: 300px;
        overflow-y: auto;
      }

      .kikaze-loading {
        display: flex;
        align-items: center;
        gap: 12px;
        color: #666;
        font-size: 14px;
      }

      .kikaze-spinner {
        width: 20px;
        height: 20px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .kikaze-result {
        font-size: 14px;
        line-height: 1.5;
        color: #333;
        margin-bottom: 16px;
        white-space: pre-wrap;
      }

      .kikaze-actions {
        display: flex;
        gap: 8px;
      }

      .kikaze-btn {
        flex: 1;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 6px;
        background: white;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.2s;
      }

      .kikaze-btn:hover {
        background: #f5f5f5;
        border-color: #999;
      }

      .kikaze-highlight {
        background: rgba(102, 126, 234, 0.2);
        padding: 2px 4px;
        border-radius: 3px;
        cursor: pointer;
      }
    `;

    document.head.appendChild(style);
    document.body.appendChild(this.widget);
  }

  setupEventListeners() {
    // Close widget
    document.getElementById('kikaze-close').addEventListener('click', () => {
      this.hide();
    });

    // Copy result
    document.getElementById('kikaze-copy').addEventListener('click', () => {
      const result = document.getElementById('kikaze-result').textContent;
      navigator.clipboard.writeText(result).then(() => {
        this.showToast('Copied to clipboard!');
      });
    });

    // Open main app
    document.getElementById('kikaze-open-app').addEventListener('click', () => {
      chrome.runtime.sendMessage({ action: 'openKikazeApp' });
    });

    // Hide widget when clicking outside
    document.addEventListener('click', (e) => {
      if (this.isVisible && !this.widget.contains(e.target)) {
        this.hide();
      }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'K') {
        e.preventDefault();
        this.toggle();
      }
      if (e.key === 'Escape' && this.isVisible) {
        this.hide();
      }
    });
  }

  show(content = '') {
    this.widget.style.display = 'block';
    this.isVisible = true;
    
    if (content) {
      document.getElementById('kikaze-result').textContent = content;
    }
  }

  hide() {
    this.widget.style.display = 'none';
    this.isVisible = false;
  }

  toggle() {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }

  showLoading() {
    document.getElementById('kikaze-loading').style.display = 'flex';
    document.getElementById('kikaze-result').style.display = 'none';
  }

  hideLoading() {
    document.getElementById('kikaze-loading').style.display = 'none';
    document.getElementById('kikaze-result').style.display = 'block';
  }

  setResult(text) {
    document.getElementById('kikaze-result').textContent = text;
    this.hideLoading();
  }

  showToast(message) {
    const toast = document.createElement('div');
    toast.textContent = message;
    toast.style.cssText = `
      position: fixed;
      top: 80px;
      right: 20px;
      background: #333;
      color: white;
      padding: 8px 16px;
      border-radius: 6px;
      font-size: 14px;
      z-index: 10001;
      animation: fadeInOut 2s ease-in-out;
    `;

    const style = document.createElement('style');
    style.textContent = `
      @keyframes fadeInOut {
        0%, 100% { opacity: 0; transform: translateY(-10px); }
        20%, 80% { opacity: 1; transform: translateY(0); }
      }
    `;
    document.head.appendChild(style);

    document.body.appendChild(toast);
    setTimeout(() => {
      document.body.removeChild(toast);
      document.head.removeChild(style);
    }, 2000);
  }
}

// Initialize widget
const kikazeWidget = new KikazeWidget();

// Smart text selection enhancement
let lastSelection = '';
document.addEventListener('mouseup', () => {
  const selection = window.getSelection().toString().trim();
  if (selection && selection !== lastSelection && selection.length > 3) {
    lastSelection = selection;
    
    // Add subtle highlight hint
    const range = window.getSelection().getRangeAt(0);
    const span = document.createElement('span');
    span.className = 'kikaze-highlight';
    span.title = 'Right-click for KIKAZE-AI options';
    
    try {
      range.surroundContents(span);
    } catch (e) {
      // Ignore if range spans multiple elements
    }
  }
});

// Handle messages from background script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  const { action, text, pageUrl, pageTitle } = request;

  kikazeWidget.show();
  kikazeWidget.showLoading();

  // Process with AI
  chrome.runtime.sendMessage({
    action: 'processWithAI',
    data: { action, text, context: { pageUrl, pageTitle } }
  }, (response) => {
    if (response.success) {
      kikazeWidget.setResult(response.result);
    } else {
      kikazeWidget.setResult('Error: ' + response.error);
    }
  });
});

// Page analysis features
class PageAnalyzer {
  static extractMainContent() {
    // Try to find main content area
    const selectors = [
      'main',
      'article',
      '[role="main"]',
      '.content',
      '.main-content',
      '#content',
      '#main'
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element) {
        return element.textContent.trim();
      }
    }

    // Fallback: get body text but filter out navigation, footer, etc.
    const excludeSelectors = ['nav', 'header', 'footer', '.sidebar', '.menu'];
    let content = document.body.cloneNode(true);
    
    excludeSelectors.forEach(selector => {
      const elements = content.querySelectorAll(selector);
      elements.forEach(el => el.remove());
    });

    return content.textContent.trim();
  }

  static extractStructuredData() {
    const data = {
      title: document.title,
      url: window.location.href,
      headings: [],
      links: [],
      images: [],
      forms: []
    };

    // Extract headings
    document.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach(heading => {
      data.headings.push({
        level: heading.tagName,
        text: heading.textContent.trim()
      });
    });

    // Extract links
    document.querySelectorAll('a[href]').forEach(link => {
      data.links.push({
        text: link.textContent.trim(),
        href: link.href
      });
    });

    // Extract images
    document.querySelectorAll('img[src]').forEach(img => {
      data.images.push({
        src: img.src,
        alt: img.alt
      });
    });

    // Extract forms
    document.querySelectorAll('form').forEach(form => {
      const formData = {
        action: form.action,
        method: form.method,
        fields: []
      };

      form.querySelectorAll('input, select, textarea').forEach(field => {
        formData.fields.push({
          type: field.type || field.tagName.toLowerCase(),
          name: field.name,
          placeholder: field.placeholder
        });
      });

      data.forms.push(formData);
    });

    return data;
  }
}

// Auto-detect and enhance forms
document.addEventListener('DOMContentLoaded', () => {
  // Add smart form filling hints
  document.querySelectorAll('input[type="email"], input[type="text"], textarea').forEach(input => {
    input.addEventListener('focus', () => {
      if (!input.value) {
        // Could add AI-powered form filling suggestions here
      }
    });
  });
});

console.log('🤖 KIKAZE-AI Browser Extension loaded successfully!');

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { toast } from 'sonner';
import { Upload, Download, Trash2 } from 'lucide-react';
import imageCompression from 'browser-image-compression';

interface CompressedImage {
  id: string;
  originalFile: File;
  compressedFile: File;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  originalUrl: string;
  compressedUrl: string;
}

export const ImageCompressor: React.FC = () => {
  const [images, setImages] = useState<CompressedImage[]>([]);
  const [isCompressing, setIsCompressing] = useState(false);
  const [quality, setQuality] = useState([0.8]);
  const [maxWidth, setMaxWidth] = useState([1920]);
  const [maxHeight, setMaxHeight] = useState([1080]);

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;

    setIsCompressing(true);
    
    for (const file of files) {
      if (!file.type.startsWith('image/')) {
        toast.error(`${file.name} bukan file gambar!`);
        continue;
      }

      try {
        const options = {
          maxSizeMB: 1,
          maxWidthOrHeight: Math.max(maxWidth[0], maxHeight[0]),
          useWebWorker: true,
          quality: quality[0],
          initialQuality: quality[0]
        };

        const compressedFile = await imageCompression(file, options);
        
        const originalUrl = URL.createObjectURL(file);
        const compressedUrl = URL.createObjectURL(compressedFile);
        
        const compressionRatio = ((file.size - compressedFile.size) / file.size) * 100;

        const newImage: CompressedImage = {
          id: Date.now().toString() + Math.random(),
          originalFile: file,
          compressedFile,
          originalSize: file.size,
          compressedSize: compressedFile.size,
          compressionRatio,
          originalUrl,
          compressedUrl
        };

        setImages(prev => [...prev, newImage]);
        toast.success(`${file.name} berhasil dikompres!`);
      } catch (error) {
        console.error('Compression error:', error);
        toast.error(`Gagal kompres ${file.name}`);
      }
    }

    setIsCompressing(false);
    // Reset input
    event.target.value = '';
  };

  const downloadImage = (image: CompressedImage) => {
    const link = document.createElement('a');
    link.href = image.compressedUrl;
    link.download = `compressed_${image.originalFile.name}`;
    link.click();
    toast.success('Gambar berhasil didownload!');
  };

  const downloadAll = () => {
    images.forEach((image, index) => {
      setTimeout(() => {
        downloadImage(image);
      }, index * 500); // Delay to avoid browser blocking
    });
    toast.success(`${images.length} gambar akan didownload!`);
  };

  const removeImage = (id: string) => {
    setImages(prev => {
      const imageToRemove = prev.find(img => img.id === id);
      if (imageToRemove) {
        URL.revokeObjectURL(imageToRemove.originalUrl);
        URL.revokeObjectURL(imageToRemove.compressedUrl);
      }
      return prev.filter(img => img.id !== id);
    });
    toast.success('Gambar berhasil dihapus!');
  };

  const clearAll = () => {
    images.forEach(image => {
      URL.revokeObjectURL(image.originalUrl);
      URL.revokeObjectURL(image.compressedUrl);
    });
    setImages([]);
    toast.success('Semua gambar berhasil dihapus!');
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const totalOriginalSize = images.reduce((sum, img) => sum + img.originalSize, 0);
  const totalCompressedSize = images.reduce((sum, img) => sum + img.compressedSize, 0);
  const totalSavings = totalOriginalSize - totalCompressedSize;
  const averageCompression = images.length > 0 
    ? images.reduce((sum, img) => sum + img.compressionRatio, 0) / images.length 
    : 0;

  const presets = [
    { name: 'Web Optimized', quality: 0.8, width: 1920, height: 1080 },
    { name: 'High Quality', quality: 0.9, width: 2560, height: 1440 },
    { name: 'Medium Quality', quality: 0.7, width: 1280, height: 720 },
    { name: 'Low Quality', quality: 0.5, width: 800, height: 600 },
    { name: 'Thumbnail', quality: 0.6, width: 400, height: 300 }
  ];

  const applyPreset = (preset: typeof presets[0]) => {
    setQuality([preset.quality]);
    setMaxWidth([preset.width]);
    setMaxHeight([preset.height]);
    toast.success(`Preset "${preset.name}" diterapkan!`);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🗜️ Image Compressor
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Settings */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Quality: {(quality[0] * 100).toFixed(0)}%</label>
              <Slider
                value={quality}
                onValueChange={setQuality}
                max={1}
                min={0.1}
                step={0.1}
                className="w-full"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Max Width: {maxWidth[0]}px</label>
              <Slider
                value={maxWidth}
                onValueChange={setMaxWidth}
                max={4096}
                min={400}
                step={100}
                className="w-full"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Max Height: {maxHeight[0]}px</label>
              <Slider
                value={maxHeight}
                onValueChange={setMaxHeight}
                max={4096}
                min={300}
                step={100}
                className="w-full"
              />
            </div>
          </div>

          {/* Presets */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Presets:</label>
            <div className="flex gap-2 flex-wrap">
              {presets.map((preset) => (
                <Button
                  key={preset.name}
                  variant="outline"
                  size="sm"
                  onClick={() => applyPreset(preset)}
                >
                  {preset.name}
                </Button>
              ))}
            </div>
          </div>

          {/* Upload Area */}
          <div className="space-y-4">
            <label className="cursor-pointer">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors">
                <Upload className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <p className="text-lg font-medium text-gray-700">
                  {isCompressing ? 'Mengompres gambar...' : 'Klik atau drag gambar ke sini'}
                </p>
                <p className="text-sm text-gray-500">
                  Support: JPG, PNG, WebP, GIF (Multiple files)
                </p>
                {isCompressing && (
                  <div className="mt-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  </div>
                )}
              </div>
              <Input
                type="file"
                multiple
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
                disabled={isCompressing}
              />
            </label>
          </div>

          {/* Statistics */}
          {images.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{images.length}</div>
                <div className="text-sm text-gray-600">Images</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{formatFileSize(totalOriginalSize)}</div>
                <div className="text-sm text-gray-600">Original Size</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{formatFileSize(totalCompressedSize)}</div>
                <div className="text-sm text-gray-600">Compressed Size</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{averageCompression.toFixed(1)}%</div>
                <div className="text-sm text-gray-600">Avg Compression</div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          {images.length > 0 && (
            <div className="flex gap-2">
              <Button onClick={downloadAll}>
                <Download className="w-4 h-4 mr-2" />
                Download All
              </Button>
              <Button variant="outline" onClick={clearAll}>
                <Trash2 className="w-4 h-4 mr-2" />
                Clear All
              </Button>
            </div>
          )}

          {/* Results */}
          {images.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Compressed Images:</h3>
              <div className="grid gap-4">
                {images.map((image) => (
                  <Card key={image.id} className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                      {/* Original Image */}
                      <div className="text-center">
                        <img 
                          src={image.originalUrl} 
                          alt="Original" 
                          className="w-20 h-20 object-cover rounded mx-auto mb-2"
                        />
                        <p className="text-xs text-gray-600">Original</p>
                        <p className="text-xs font-medium">{formatFileSize(image.originalSize)}</p>
                      </div>

                      {/* Compressed Image */}
                      <div className="text-center">
                        <img 
                          src={image.compressedUrl} 
                          alt="Compressed" 
                          className="w-20 h-20 object-cover rounded mx-auto mb-2"
                        />
                        <p className="text-xs text-gray-600">Compressed</p>
                        <p className="text-xs font-medium">{formatFileSize(image.compressedSize)}</p>
                      </div>

                      {/* Stats */}
                      <div className="text-center">
                        <div className="text-lg font-bold text-green-600">
                          -{image.compressionRatio.toFixed(1)}%
                        </div>
                        <p className="text-xs text-gray-600">Compression</p>
                        <p className="text-xs text-gray-600">
                          Saved: {formatFileSize(image.originalSize - image.compressedSize)}
                        </p>
                      </div>

                      {/* Actions */}
                      <div className="flex gap-2 justify-center">
                        <Button size="sm" onClick={() => downloadImage(image)}>
                          <Download className="w-4 h-4" />
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => removeImage(image.id)}
                          className="text-red-600"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="mt-2 text-center">
                      <p className="text-sm font-medium truncate">{image.originalFile.name}</p>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Info */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">ℹ️ Fitur Image Compressor:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• <strong>Batch Processing</strong>: Kompres multiple gambar sekaligus</li>
              <li>• <strong>Quality Control</strong>: Atur kualitas kompresi sesuai kebutuhan</li>
              <li>• <strong>Size Limit</strong>: Atur maksimal dimensi gambar</li>
              <li>• <strong>Real-time Preview</strong>: Lihat hasil before/after</li>
              <li>• <strong>Statistics</strong>: Monitor total penghematan size</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { But<PERSON> } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { toast } from 'sonner';
import { Download, Copy, RefreshCw, Smartphone } from 'lucide-react';
import QRCode from 'qrcode';

export const QRCodeGenerator: React.FC = () => {
  const [text, setText] = useState('');
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [size, setSize] = useState([256]);
  const [errorLevel, setErrorLevel] = useState<'L' | 'M' | 'Q' | 'H'>('M');
  const [foregroundColor, setForegroundColor] = useState('#000000');
  const [backgroundColor, setBackgroundColor] = useState('#FFFFFF');
  const [isGenerating, setIsGenerating] = useState(false);

  const generateQRCode = async () => {
    if (!text.trim()) {
      toast.error('Masukkan teks atau URL untuk generate QR Code!');
      return;
    }

    setIsGenerating(true);
    try {
      const options = {
        width: size[0],
        margin: 2,
        color: {
          dark: foregroundColor,
          light: backgroundColor
        },
        errorCorrectionLevel: errorLevel
      };

      const url = await QRCode.toDataURL(text, options);
      setQrCodeUrl(url);
      toast.success('QR Code berhasil digenerate!');
    } catch (error) {
      console.error('Error generating QR code:', error);
      toast.error('Gagal generate QR Code');
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadQRCode = () => {
    if (!qrCodeUrl) return;

    const link = document.createElement('a');
    link.download = `qrcode-${Date.now()}.png`;
    link.href = qrCodeUrl;
    link.click();
    toast.success('QR Code berhasil didownload!');
  };

  const copyQRCode = async () => {
    if (!qrCodeUrl) return;

    try {
      const response = await fetch(qrCodeUrl);
      const blob = await response.blob();
      await navigator.clipboard.write([
        new ClipboardItem({ 'image/png': blob })
      ]);
      toast.success('QR Code berhasil disalin ke clipboard!');
    } catch (error) {
      toast.error('Gagal menyalin QR Code');
    }
  };

  const loadSample = (type: string) => {
    const samples = {
      url: 'https://kikaze-ai.com',
      text: 'Hello from KIKAZE-AI! 🤖\nThis is a sample QR Code.',
      wifi: 'WIFI:T:WPA;S:MyNetwork;P:MyPassword;H:false;;',
      contact: 'BEGIN:VCARD\nVERSION:3.0\nFN:John Doe\nORG:KIKAZE-AI\nTEL:+62123456789\nEMAIL:<EMAIL>\nEND:VCARD',
      sms: 'sms:+62123456789?body=Hello from QR Code!',
      email: 'mailto:<EMAIL>?subject=Hello&body=Message from QR Code'
    };

    setText(samples[type as keyof typeof samples] || '');
    toast.success(`Sample ${type} dimuat!`);
  };

  // Auto-generate when text changes (debounced)
  useEffect(() => {
    const timer = setTimeout(() => {
      if (text.trim()) {
        generateQRCode();
      } else {
        setQrCodeUrl('');
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [text, size, errorLevel, foregroundColor, backgroundColor]);

  const errorLevels = [
    { value: 'L', label: 'Low (~7%)', description: 'Cocok untuk lingkungan bersih' },
    { value: 'M', label: 'Medium (~15%)', description: 'Standar untuk penggunaan umum' },
    { value: 'Q', label: 'Quartile (~25%)', description: 'Untuk lingkungan kotor' },
    { value: 'H', label: 'High (~30%)', description: 'Maksimal error correction' }
  ];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            📱 QR Code Generator
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Input Section */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium">Text atau URL:</label>
              <div className="flex gap-1 flex-wrap">
                <Button variant="outline" size="sm" onClick={() => loadSample('url')}>
                  🌐 URL
                </Button>
                <Button variant="outline" size="sm" onClick={() => loadSample('text')}>
                  📝 Text
                </Button>
                <Button variant="outline" size="sm" onClick={() => loadSample('wifi')}>
                  📶 WiFi
                </Button>
                <Button variant="outline" size="sm" onClick={() => loadSample('contact')}>
                  👤 Contact
                </Button>
              </div>
            </div>
            <Textarea
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="Masukkan URL, teks, atau data lainnya..."
              className="min-h-[100px]"
            />
          </div>

          {/* Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Size */}
            <div className="space-y-3">
              <label className="text-sm font-medium">Ukuran: {size[0]}px</label>
              <Slider
                value={size}
                onValueChange={setSize}
                max={512}
                min={128}
                step={32}
                className="w-full"
              />
            </div>

            {/* Error Correction Level */}
            <div className="space-y-3">
              <label className="text-sm font-medium">Error Correction:</label>
              <div className="grid grid-cols-2 gap-2">
                {errorLevels.map((level) => (
                  <Button
                    key={level.value}
                    variant={errorLevel === level.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => setErrorLevel(level.value as any)}
                    title={level.description}
                  >
                    {level.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Colors */}
            <div className="space-y-3">
              <label className="text-sm font-medium">Warna Foreground:</label>
              <div className="flex gap-2 items-center">
                <Input
                  type="color"
                  value={foregroundColor}
                  onChange={(e) => setForegroundColor(e.target.value)}
                  className="w-16 h-10"
                />
                <Input
                  value={foregroundColor}
                  onChange={(e) => setForegroundColor(e.target.value)}
                  className="flex-1"
                />
              </div>
            </div>

            <div className="space-y-3">
              <label className="text-sm font-medium">Warna Background:</label>
              <div className="flex gap-2 items-center">
                <Input
                  type="color"
                  value={backgroundColor}
                  onChange={(e) => setBackgroundColor(e.target.value)}
                  className="w-16 h-10"
                />
                <Input
                  value={backgroundColor}
                  onChange={(e) => setBackgroundColor(e.target.value)}
                  className="flex-1"
                />
              </div>
            </div>
          </div>

          {/* QR Code Display */}
          <div className="text-center space-y-4">
            {qrCodeUrl ? (
              <div className="space-y-4">
                <div className="inline-block p-4 bg-white rounded-lg shadow-lg">
                  <img 
                    src={qrCodeUrl} 
                    alt="Generated QR Code" 
                    className="max-w-full h-auto"
                    style={{ width: size[0], height: size[0] }}
                  />
                </div>
                <div className="flex gap-2 justify-center">
                  <Button onClick={downloadQRCode}>
                    <Download className="w-4 h-4 mr-2" />
                    Download PNG
                  </Button>
                  <Button variant="outline" onClick={copyQRCode}>
                    <Copy className="w-4 h-4 mr-2" />
                    Copy Image
                  </Button>
                  <Button variant="outline" onClick={generateQRCode} disabled={isGenerating}>
                    <RefreshCw className={`w-4 h-4 mr-2 ${isGenerating ? 'animate-spin' : ''}`} />
                    Regenerate
                  </Button>
                </div>
              </div>
            ) : (
              <div className="p-8 border-2 border-dashed border-gray-300 rounded-lg">
                <Smartphone className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-500">QR Code akan muncul di sini...</p>
                <p className="text-sm text-gray-400">Masukkan teks atau URL untuk mulai generate</p>
              </div>
            )}
          </div>

          {/* Quick Samples */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Quick Samples:</label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              <Button variant="outline" size="sm" onClick={() => loadSample('sms')}>
                💬 SMS
              </Button>
              <Button variant="outline" size="sm" onClick={() => loadSample('email')}>
                📧 Email
              </Button>
              <Button variant="outline" size="sm" onClick={() => setText('geo:37.7749,-122.4194')}>
                📍 Location
              </Button>
              <Button variant="outline" size="sm" onClick={() => setText('tel:+62123456789')}>
                📞 Phone
              </Button>
              <Button variant="outline" size="sm" onClick={() => setText('https://wa.me/62123456789')}>
                💚 WhatsApp
              </Button>
              <Button variant="outline" size="sm" onClick={() => setText('')}>
                🗑️ Clear
              </Button>
            </div>
          </div>

          {/* Info */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">ℹ️ Fitur QR Code Generator:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• <strong>Auto-Generate</strong>: QR Code dibuat otomatis saat mengetik</li>
              <li>• <strong>Custom Colors</strong>: Ubah warna foreground dan background</li>
              <li>• <strong>Error Correction</strong>: Pilih level koreksi error sesuai kebutuhan</li>
              <li>• <strong>Multiple Formats</strong>: Support URL, text, WiFi, contact, dll</li>
              <li>• <strong>High Quality</strong>: Export PNG dengan resolusi tinggi</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Heart, Brain, Zap, Coffee } from 'lucide-react';

interface MoodAnalysis {
  primaryMood: string;
  confidence: number;
  emotions: { [key: string]: number };
  suggestions: string[];
  color: string;
  emoji: string;
}

interface MoodHistory {
  timestamp: Date;
  text: string;
  mood: string;
  confidence: number;
}

export const MoodDetector: React.FC = () => {
  const [text, setText] = useState('');
  const [analysis, setAnalysis] = useState<MoodAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [moodHistory, setMoodHistory] = useState<MoodHistory[]>([]);

  const moodKeywords = {
    happy: {
      keywords: ['senang', 'bahagia', 'gembira', 'excited', 'amazing', 'wonderful', 'great', 'awesome', 'love', 'perfect', 'fantastic', 'excellent', 'joy', 'cheerful', 'delighted'],
      emoji: '😊',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      suggestions: [
        'Bagikan kebahagiaan ini dengan orang terdekat!',
        'Manfaatkan mood positif ini untuk produktivitas',
        'Dokumentasikan momen bahagia ini',
        'Lakukan aktivitas yang Anda sukai'
      ]
    },
    sad: {
      keywords: ['sedih', 'kecewa', 'down', 'upset', 'disappointed', 'hurt', 'crying', 'tears', 'lonely', 'depressed', 'blue', 'melancholy', 'grief', 'sorrow'],
      emoji: '😢',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      suggestions: [
        'Berbicara dengan teman atau keluarga',
        'Lakukan self-care activities',
        'Dengarkan musik yang menenangkan',
        'Ingat bahwa perasaan ini akan berlalu'
      ]
    },
    angry: {
      keywords: ['marah', 'kesal', 'angry', 'mad', 'furious', 'annoyed', 'frustrated', 'irritated', 'rage', 'pissed', 'outraged', 'livid'],
      emoji: '😡',
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      suggestions: [
        'Ambil napas dalam-dalam',
        'Hitung sampai 10 sebelum bereaksi',
        'Lakukan olahraga ringan',
        'Tulis perasaan Anda di jurnal'
      ]
    },
    excited: {
      keywords: ['excited', 'antusias', 'semangat', 'energetic', 'pumped', 'thrilled', 'enthusiastic', 'eager', 'motivated', 'inspired'],
      emoji: '🤩',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      suggestions: [
        'Channel energi ini untuk proyek baru',
        'Bagikan antusiasme dengan tim',
        'Buat rencana action yang konkret',
        'Manfaatkan momentum ini'
      ]
    },
    anxious: {
      keywords: ['cemas', 'khawatir', 'anxious', 'worried', 'nervous', 'stressed', 'panic', 'overwhelmed', 'tense', 'uneasy', 'restless'],
      emoji: '😰',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      suggestions: [
        'Praktikkan teknik pernapasan',
        'Buat daftar hal yang bisa dikontrol',
        'Lakukan meditasi singkat',
        'Fokus pada satu hal pada satu waktu'
      ]
    },
    grateful: {
      keywords: ['grateful', 'thankful', 'blessed', 'appreciate', 'syukur', 'berterima kasih', 'menghargai'],
      emoji: '🙏',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      suggestions: [
        'Tulis gratitude journal',
        'Ucapkan terima kasih pada seseorang',
        'Refleksikan hal-hal positif hari ini',
        'Bagikan apresiasi Anda'
      ]
    },
    confused: {
      keywords: ['bingung', 'confused', 'puzzled', 'uncertain', 'unclear', 'lost', 'bewildered', 'perplexed'],
      emoji: '🤔',
      color: 'text-gray-600',
      bgColor: 'bg-gray-50',
      suggestions: [
        'Break down masalah menjadi bagian kecil',
        'Cari informasi tambahan',
        'Diskusikan dengan orang lain',
        'Buat mind map untuk clarity'
      ]
    }
  };

  const analyzeMood = (inputText: string): MoodAnalysis => {
    const lowerText = inputText.toLowerCase();
    const emotions: { [key: string]: number } = {};

    // Calculate emotion scores
    Object.entries(moodKeywords).forEach(([mood, data]) => {
      let score = 0;
      data.keywords.forEach(keyword => {
        const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
        const matches = lowerText.match(regex);
        if (matches) {
          score += matches.length;
        }
      });
      emotions[mood] = score;
    });

    // Find primary mood
    const primaryMoodEntry = Object.entries(emotions).reduce((a, b) => 
      emotions[a[0]] > emotions[b[0]] ? a : b
    );

    const primaryMood = primaryMoodEntry[0];
    const totalScore = Object.values(emotions).reduce((sum, score) => sum + score, 0);
    const confidence = totalScore > 0 ? (emotions[primaryMood] / totalScore) * 100 : 0;

    // If no clear mood detected, analyze sentiment
    let detectedMood = primaryMood;
    if (totalScore === 0) {
      // Simple sentiment analysis
      const positiveWords = ['good', 'nice', 'ok', 'fine', 'well', 'baik', 'oke'];
      const negativeWords = ['bad', 'not', 'no', 'never', 'buruk', 'tidak', 'nggak'];
      
      const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;
      const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;
      
      if (positiveCount > negativeCount) {
        detectedMood = 'happy';
        emotions.happy = positiveCount;
      } else if (negativeCount > positiveCount) {
        detectedMood = 'sad';
        emotions.sad = negativeCount;
      } else {
        detectedMood = 'confused';
        emotions.confused = 1;
      }
    }

    const moodData = moodKeywords[detectedMood as keyof typeof moodKeywords];

    return {
      primaryMood: detectedMood,
      confidence: Math.max(confidence, 25), // Minimum 25% confidence
      emotions,
      suggestions: moodData.suggestions,
      color: moodData.color,
      emoji: moodData.emoji
    };
  };

  const handleAnalyze = () => {
    if (!text.trim()) {
      toast.error('Masukkan teks untuk dianalisis mood-nya!');
      return;
    }

    setIsAnalyzing(true);
    
    // Simulate analysis delay
    setTimeout(() => {
      const result = analyzeMood(text);
      setAnalysis(result);
      
      // Add to history
      const newEntry: MoodHistory = {
        timestamp: new Date(),
        text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
        mood: result.primaryMood,
        confidence: result.confidence
      };
      
      setMoodHistory(prev => [newEntry, ...prev.slice(0, 9)]); // Keep last 10
      setIsAnalyzing(false);
      toast.success(`Mood detected: ${result.emoji} ${result.primaryMood}`);
    }, 1000);
  };

  const loadSample = (type: string) => {
    const samples = {
      happy: 'Hari ini sangat menyenangkan! Saya berhasil menyelesaikan proyek dengan baik dan tim sangat senang dengan hasilnya. Feeling grateful and excited untuk tantangan selanjutnya!',
      sad: 'Merasa sedikit down hari ini. Ada beberapa hal yang tidak berjalan sesuai rencana dan saya merasa kecewa dengan hasilnya. Hopefully besok akan lebih baik.',
      angry: 'Sangat kesal dengan situasi ini! Sudah berkali-kali dijelaskan tapi masih saja ada yang tidak mengerti. Frustrated banget dengan attitude beberapa orang.',
      excited: 'OMG! Baru saja dapat kabar bahwa proposal saya diterima! So excited dan antusias untuk memulai project baru ini. Can\'t wait to get started!',
      anxious: 'Merasa cemas menjelang presentasi besok. Worried apakah persiapan sudah cukup dan bagaimana reaksi audience nanti. Nervous tapi harus tetap optimis.',
      grateful: 'Sangat bersyukur hari ini. Grateful untuk semua support dari keluarga dan teman-teman. Appreciate banget semua help yang diberikan selama ini.'
    };

    setText(samples[type as keyof typeof samples] || '');
    toast.success(`Sample ${type} text loaded!`);
  };

  const getMoodIcon = (mood: string) => {
    const icons: { [key: string]: React.ReactNode } = {
      happy: <Heart className="w-4 h-4" />,
      sad: <Coffee className="w-4 h-4" />,
      angry: <Zap className="w-4 h-4" />,
      excited: <Zap className="w-4 h-4" />,
      anxious: <Brain className="w-4 h-4" />,
      grateful: <Heart className="w-4 h-4" />,
      confused: <Brain className="w-4 h-4" />
    };
    return icons[mood] || <Brain className="w-4 h-4" />;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🧠 Mood Detector
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Input Section */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium">Ceritakan perasaan atau situasi Anda:</label>
              <div className="flex gap-1 flex-wrap">
                <Button variant="outline" size="sm" onClick={() => loadSample('happy')}>
                  😊 Happy
                </Button>
                <Button variant="outline" size="sm" onClick={() => loadSample('sad')}>
                  😢 Sad
                </Button>
                <Button variant="outline" size="sm" onClick={() => loadSample('angry')}>
                  😡 Angry
                </Button>
                <Button variant="outline" size="sm" onClick={() => loadSample('excited')}>
                  🤩 Excited
                </Button>
              </div>
            </div>
            <Textarea
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="Tulis tentang perasaan Anda hari ini, situasi yang sedang dialami, atau apa yang ada di pikiran..."
              className="min-h-[120px]"
            />
            <div className="text-sm text-gray-500">
              Karakter: {text.length} | Kata: {text.split(/\s+/).filter(word => word.length > 0).length}
            </div>
          </div>

          {/* Analyze Button */}
          <Button onClick={handleAnalyze} disabled={isAnalyzing || !text.trim()} className="w-full">
            {isAnalyzing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Analyzing Mood...
              </>
            ) : (
              <>
                <Brain className="w-4 h-4 mr-2" />
                Analyze Mood
              </>
            )}
          </Button>

          {/* Analysis Result */}
          {analysis && (
            <Card className={`${moodKeywords[analysis.primaryMood as keyof typeof moodKeywords]?.bgColor || 'bg-gray-50'}`}>
              <CardContent className="p-6">
                <div className="text-center mb-4">
                  <div className="text-6xl mb-2">{analysis.emoji}</div>
                  <h3 className={`text-2xl font-bold capitalize ${analysis.color}`}>
                    {analysis.primaryMood}
                  </h3>
                  <p className="text-sm text-gray-600">
                    Confidence: {analysis.confidence.toFixed(1)}%
                  </p>
                </div>

                {/* Emotion Breakdown */}
                <div className="mb-4">
                  <h4 className="font-medium mb-2">Emotion Breakdown:</h4>
                  <div className="space-y-2">
                    {Object.entries(analysis.emotions)
                      .filter(([_, score]) => score > 0)
                      .sort(([_, a], [__, b]) => b - a)
                      .map(([emotion, score]) => (
                        <div key={emotion} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {getMoodIcon(emotion)}
                            <span className="capitalize">{emotion}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-20 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-500 h-2 rounded-full" 
                                style={{ width: `${Math.min((score / Math.max(...Object.values(analysis.emotions))) * 100, 100)}%` }}
                              />
                            </div>
                            <span className="text-sm text-gray-600">{score}</span>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>

                {/* Suggestions */}
                <div>
                  <h4 className="font-medium mb-2">💡 Suggestions:</h4>
                  <ul className="space-y-1">
                    {analysis.suggestions.map((suggestion, index) => (
                      <li key={index} className="text-sm text-gray-700 flex items-start gap-2">
                        <span className="text-blue-500 mt-1">•</span>
                        {suggestion}
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Mood History */}
          {moodHistory.length > 0 && (
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">📊 Mood History</h3>
              <div className="grid gap-2 max-h-60 overflow-y-auto">
                {moodHistory.map((entry, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{moodKeywords[entry.mood as keyof typeof moodKeywords]?.emoji}</span>
                      <div>
                        <p className="text-sm font-medium capitalize">{entry.mood}</p>
                        <p className="text-xs text-gray-500">{entry.text}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-gray-500">
                        {entry.timestamp.toLocaleTimeString('id-ID', { 
                          hour: '2-digit', 
                          minute: '2-digit' 
                        })}
                      </p>
                      <p className="text-xs text-gray-400">
                        {entry.confidence.toFixed(0)}%
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Info */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">ℹ️ How Mood Detection Works:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• <strong>Keyword Analysis</strong>: Detects emotional keywords in your text</li>
              <li>• <strong>Sentiment Analysis</strong>: Analyzes overall tone and sentiment</li>
              <li>• <strong>Confidence Score</strong>: Shows how certain the analysis is</li>
              <li>• <strong>Personalized Suggestions</strong>: Provides mood-appropriate advice</li>
              <li>• <strong>History Tracking</strong>: Keeps track of your mood patterns</li>
              <li>• <strong>Privacy First</strong>: All analysis done locally, no data stored</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KIKAZE-AI Loading</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            color: white;
            overflow: hidden;
        }

        .splash-container {
            text-align: center;
            animation: fadeIn 0.8s ease-in-out;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            animation: pulse 2s infinite;
        }

        .logo-text {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .app-name {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
            letter-spacing: 2px;
        }

        .app-tagline {
            font-size: 14px;
            opacity: 0.9;
            margin-bottom: 30px;
            font-weight: 300;
        }

        .loading-container {
            margin-top: 20px;
        }

        .loading-bar {
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            margin: 0 auto 15px;
            overflow: hidden;
        }

        .loading-progress {
            height: 100%;
            background: linear-gradient(90deg, #fff, #f0f0f0);
            border-radius: 2px;
            animation: loading 2s ease-in-out infinite;
        }

        .loading-text {
            font-size: 12px;
            opacity: 0.8;
            animation: dots 1.5s infinite;
        }

        .version {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 11px;
            opacity: 0.6;
        }

        .features {
            margin-top: 20px;
            font-size: 11px;
            opacity: 0.8;
            line-height: 1.4;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
            }
        }

        @keyframes loading {
            0% {
                width: 0%;
                margin-left: 0%;
            }
            50% {
                width: 75%;
                margin-left: 12.5%;
            }
            100% {
                width: 0%;
                margin-left: 100%;
            }
        }

        @keyframes dots {
            0%, 20% {
                content: 'Loading';
            }
            40% {
                content: 'Loading.';
            }
            60% {
                content: 'Loading..';
            }
            80%, 100% {
                content: 'Loading...';
            }
        }

        .flag {
            display: inline-block;
            margin-left: 8px;
            font-size: 20px;
            animation: wave 2s ease-in-out infinite;
        }

        @keyframes wave {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(10deg); }
            75% { transform: rotate(-10deg); }
        }
    </style>
</head>
<body>
    <div class="splash-container">
        <div class="logo">
            <div class="logo-text">🤖</div>
        </div>
        
        <div class="app-name">
            KIKAZE-AI
            <span class="flag">🇮🇩</span>
        </div>
        
        <div class="app-tagline">
            Advanced Indonesian AI Assistant
        </div>
        
        <div class="features">
            🧠 Memory & Context Awareness<br>
            🗣️ Natural Indonesian Voice<br>
            🛠️ 25+ Professional Tools<br>
            💬 Human-like Conversation
        </div>
        
        <div class="loading-container">
            <div class="loading-bar">
                <div class="loading-progress"></div>
            </div>
            <div class="loading-text">Initializing AI System...</div>
        </div>
    </div>
    
    <div class="version">
        Desktop Version 1.0.0
    </div>

    <script>
        // Simulate loading steps
        const loadingTexts = [
            'Initializing AI System...',
            'Loading Memory Engine...',
            'Setting up Voice System...',
            'Preparing Tools...',
            'Almost Ready...'
        ];
        
        let currentStep = 0;
        const loadingTextElement = document.querySelector('.loading-text');
        
        const updateLoadingText = () => {
            if (currentStep < loadingTexts.length) {
                loadingTextElement.textContent = loadingTexts[currentStep];
                currentStep++;
                setTimeout(updateLoadingText, 400);
            }
        };
        
        setTimeout(updateLoadingText, 500);
    </script>
</body>
</html>

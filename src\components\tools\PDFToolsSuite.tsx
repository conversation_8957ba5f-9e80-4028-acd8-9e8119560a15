import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { Download, Upload, FileText, Scissors, Merge, Lock, Unlock } from 'lucide-react';

interface PDFOperation {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: string;
}

export const PDFToolsSuite: React.FC = () => {
  const [selectedOperation, setSelectedOperation] = useState<string>('');
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [operationConfig, setOperationConfig] = useState<{ [key: string]: any }>({});

  const pdfOperations: PDFOperation[] = [
    {
      id: 'merge',
      name: 'Merge PDF',
      description: 'Gabungkan beberapa file PDF menjadi satu',
      icon: <Merge className="w-5 h-5" />,
      category: 'Combine'
    },
    {
      id: 'split',
      name: 'Split PDF',
      description: 'Pisahkan PDF menjadi beberapa file',
      icon: <Scissors className="w-5 h-5" />,
      category: 'Split'
    },
    {
      id: 'compress',
      name: 'Compress PDF',
      description: 'Kurangi ukuran file PDF',
      icon: <FileText className="w-5 h-5" />,
      category: 'Optimize'
    },
    {
      id: 'protect',
      name: 'Protect PDF',
      description: 'Tambahkan password ke PDF',
      icon: <Lock className="w-5 h-5" />,
      category: 'Security'
    },
    {
      id: 'unlock',
      name: 'Unlock PDF',
      description: 'Hapus password dari PDF',
      icon: <Unlock className="w-5 h-5" />,
      category: 'Security'
    },
    {
      id: 'extract-pages',
      name: 'Extract Pages',
      description: 'Ekstrak halaman tertentu dari PDF',
      icon: <FileText className="w-5 h-5" />,
      category: 'Extract'
    }
  ];

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const pdfFiles = files.filter(file => file.type === 'application/pdf');
    
    if (pdfFiles.length !== files.length) {
      toast.warning('Hanya file PDF yang diterima');
    }
    
    setUploadedFiles(pdfFiles);
    toast.success(`${pdfFiles.length} file PDF berhasil diupload`);
  };

  const processPDF = async () => {
    if (!selectedOperation) {
      toast.error('Pilih operasi PDF terlebih dahulu!');
      return;
    }

    if (uploadedFiles.length === 0) {
      toast.error('Upload file PDF terlebih dahulu!');
      return;
    }

    setIsProcessing(true);

    try {
      // Simulate PDF processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      switch (selectedOperation) {
        case 'merge':
          await mergePDFs();
          break;
        case 'split':
          await splitPDF();
          break;
        case 'compress':
          await compressPDF();
          break;
        case 'protect':
          await protectPDF();
          break;
        case 'unlock':
          await unlockPDF();
          break;
        case 'extract-pages':
          await extractPages();
          break;
        default:
          throw new Error('Operasi tidak dikenali');
      }

      toast.success('PDF berhasil diproses!');
    } catch (error) {
      console.error('PDF processing error:', error);
      toast.error('Gagal memproses PDF');
    } finally {
      setIsProcessing(false);
    }
  };

  const mergePDFs = async () => {
    // Simulate PDF merging
    const mergedFileName = `merged_${Date.now()}.pdf`;
    
    // In a real implementation, you would use a library like PDF-lib
    const dummyContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(Merged PDF Document) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
300
%%EOF`;

    const blob = new Blob([dummyContent], { type: 'application/pdf' });
    downloadFile(blob, mergedFileName);
  };

  const splitPDF = async () => {
    const totalPages = operationConfig.totalPages || 10;
    const pagesPerFile = operationConfig.pagesPerFile || 2;
    const numFiles = Math.ceil(totalPages / pagesPerFile);

    for (let i = 0; i < numFiles; i++) {
      const startPage = i * pagesPerFile + 1;
      const endPage = Math.min((i + 1) * pagesPerFile, totalPages);
      const fileName = `split_${startPage}-${endPage}_${Date.now()}.pdf`;
      
      // Create dummy PDF content
      const dummyContent = createDummyPDF(`Split PDF Pages ${startPage}-${endPage}`);
      const blob = new Blob([dummyContent], { type: 'application/pdf' });
      
      // Small delay between downloads
      setTimeout(() => downloadFile(blob, fileName), i * 500);
    }
  };

  const compressPDF = async () => {
    const originalSize = uploadedFiles[0]?.size || 1000000;
    const compressionRatio = operationConfig.compressionLevel || 0.7;
    const compressedSize = Math.floor(originalSize * compressionRatio);
    
    const fileName = `compressed_${uploadedFiles[0]?.name || 'document.pdf'}`;
    const dummyContent = createDummyPDF(`Compressed PDF (${formatFileSize(compressedSize)})`);
    const blob = new Blob([dummyContent], { type: 'application/pdf' });
    
    downloadFile(blob, fileName);
    toast.info(`Ukuran dikurangi dari ${formatFileSize(originalSize)} ke ${formatFileSize(compressedSize)}`);
  };

  const protectPDF = async () => {
    const password = operationConfig.password;
    if (!password) {
      toast.error('Masukkan password terlebih dahulu!');
      return;
    }

    const fileName = `protected_${uploadedFiles[0]?.name || 'document.pdf'}`;
    const dummyContent = createDummyPDF(`Password Protected PDF`);
    const blob = new Blob([dummyContent], { type: 'application/pdf' });
    
    downloadFile(blob, fileName);
    toast.info(`PDF dilindungi dengan password: ${password}`);
  };

  const unlockPDF = async () => {
    const fileName = `unlocked_${uploadedFiles[0]?.name || 'document.pdf'}`;
    const dummyContent = createDummyPDF(`Unlocked PDF Document`);
    const blob = new Blob([dummyContent], { type: 'application/pdf' });
    
    downloadFile(blob, fileName);
  };

  const extractPages = async () => {
    const pageRange = operationConfig.pageRange || '1-3';
    const fileName = `extracted_pages_${pageRange}_${Date.now()}.pdf`;
    const dummyContent = createDummyPDF(`Extracted Pages ${pageRange}`);
    const blob = new Blob([dummyContent], { type: 'application/pdf' });
    
    downloadFile(blob, fileName);
  };

  const createDummyPDF = (title: string): string => {
    return `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length ${title.length + 50}
>>
stream
BT
/F1 12 Tf
100 700 Td
(${title}) Tj
100 650 Td
(Generated by KIKAZE-AI PDF Tools) Tj
100 600 Td
(Date: ${new Date().toLocaleDateString()}) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
400
%%EOF`;
  };

  const downloadFile = (blob: Blob, fileName: string) => {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
    URL.revokeObjectURL(url);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const renderOperationConfig = () => {
    switch (selectedOperation) {
      case 'split':
        return (
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Total Pages:</label>
              <Input
                type="number"
                value={operationConfig.totalPages || ''}
                onChange={(e) => setOperationConfig(prev => ({ ...prev, totalPages: parseInt(e.target.value) }))}
                placeholder="10"
              />
            </div>
            <div>
              <label className="text-sm font-medium">Pages per File:</label>
              <Input
                type="number"
                value={operationConfig.pagesPerFile || ''}
                onChange={(e) => setOperationConfig(prev => ({ ...prev, pagesPerFile: parseInt(e.target.value) }))}
                placeholder="2"
              />
            </div>
          </div>
        );

      case 'compress':
        return (
          <div>
            <label className="text-sm font-medium">Compression Level:</label>
            <Select 
              value={operationConfig.compressionLevel?.toString() || ''} 
              onValueChange={(value) => setOperationConfig(prev => ({ ...prev, compressionLevel: parseFloat(value) }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Pilih level kompresi..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0.9">Low (90% size)</SelectItem>
                <SelectItem value="0.7">Medium (70% size)</SelectItem>
                <SelectItem value="0.5">High (50% size)</SelectItem>
                <SelectItem value="0.3">Maximum (30% size)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );

      case 'protect':
        return (
          <div>
            <label className="text-sm font-medium">Password:</label>
            <Input
              type="password"
              value={operationConfig.password || ''}
              onChange={(e) => setOperationConfig(prev => ({ ...prev, password: e.target.value }))}
              placeholder="Masukkan password..."
            />
          </div>
        );

      case 'extract-pages':
        return (
          <div>
            <label className="text-sm font-medium">Page Range (contoh: 1-3, 5, 7-9):</label>
            <Input
              value={operationConfig.pageRange || ''}
              onChange={(e) => setOperationConfig(prev => ({ ...prev, pageRange: e.target.value }))}
              placeholder="1-3"
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            📄 PDF Tools Suite
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Operation Selection */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {pdfOperations.map((operation) => (
              <Card 
                key={operation.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedOperation === operation.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
                }`}
                onClick={() => setSelectedOperation(operation.id)}
              >
                <CardContent className="p-4 text-center">
                  <div className="flex justify-center mb-2">
                    {operation.icon}
                  </div>
                  <h3 className="font-medium text-sm">{operation.name}</h3>
                  <p className="text-xs text-gray-600 mt-1">{operation.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* File Upload */}
          <div className="space-y-4">
            <label className="cursor-pointer">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                <Upload className="w-8 h-8 mx-auto text-gray-400 mb-2" />
                <p className="text-sm font-medium text-gray-700">
                  Upload PDF Files
                </p>
                <p className="text-xs text-gray-500">
                  Pilih satu atau beberapa file PDF
                </p>
              </div>
              <Input
                type="file"
                multiple
                accept=".pdf"
                onChange={handleFileUpload}
                className="hidden"
              />
            </label>

            {uploadedFiles.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">Uploaded Files:</h4>
                {uploadedFiles.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm">{file.name}</span>
                    <span className="text-xs text-gray-500">{formatFileSize(file.size)}</span>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Operation Configuration */}
          {selectedOperation && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Configuration:</h3>
              {renderOperationConfig()}
            </div>
          )}

          {/* Process Button */}
          <Button 
            onClick={processPDF} 
            disabled={isProcessing || !selectedOperation || uploadedFiles.length === 0}
            className="w-full"
          >
            {isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Processing PDF...
              </>
            ) : (
              <>
                <FileText className="w-4 h-4 mr-2" />
                Process PDF
              </>
            )}
          </Button>

          {/* Info */}
          <div className="bg-orange-50 p-4 rounded-lg">
            <h4 className="font-semibold text-orange-800 mb-2">ℹ️ PDF Tools Features:</h4>
            <ul className="text-sm text-orange-700 space-y-1">
              <li>• <strong>Merge PDF</strong>: Gabungkan beberapa PDF menjadi satu file</li>
              <li>• <strong>Split PDF</strong>: Pisahkan PDF berdasarkan jumlah halaman</li>
              <li>• <strong>Compress PDF</strong>: Kurangi ukuran file dengan berbagai level</li>
              <li>• <strong>Protect PDF</strong>: Tambahkan password protection</li>
              <li>• <strong>Unlock PDF</strong>: Hapus password dari PDF</li>
              <li>• <strong>Extract Pages</strong>: Ambil halaman tertentu dari PDF</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
